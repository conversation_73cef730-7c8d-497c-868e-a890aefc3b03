import 'package:bond_core/bond_core.dart';
import 'package:get_it/get_it.dart';
import 'package:zod/features/onboarding/data/models/onboarding.dart';

import '../../core/services/onboarding_service.dart';

class OnboardingServiceProvider extends ServiceProvider with ResponseDecoding {
  @override
  Future<void> register(GetIt it) async {
    it.registerLazySingleton<OnboardingService>(() => OnboardingService());
  }

  @override
  Map<Type, JsonFactory> get factories => {
        Onboarding: Onboarding.fromJson,
      };
}
