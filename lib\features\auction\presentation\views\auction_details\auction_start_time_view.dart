import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/core/resources/app_colors.dart';
import 'package:zod/core/resources/app_icons.dart';
import 'package:zod/features/auction/data/models/auction.dart';
import 'package:zod/features/auction/presentation/providers/auction_start_timer_provider.dart';

class AuctionStartTimeView extends ConsumerWidget {
  final Auction auction;

  const AuctionStartTimeView({
    super.key,
    required this.auction,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // If auction has an effective start time and is fully subscribed
    if (auction.startAt != null &&
        auction.remainingParticipation != null &&
        auction.remainingParticipation == 0) {
      // Check if we're within 2 hours of the start time
      final now = DateTime.now();
      final difference = auction.startAt!.difference(now);
      final isWithinTwoHours =
          difference.inHours < 2 && difference.isNegative == false;

      if (isWithinTwoHours) {
        // Show countdown timer
        return Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            _buildCountdownTimer(context, ref, auction.effectiveStartTime!),
          ],
        );
      }
    }

    return const SizedBox.shrink();
  }

  Widget _buildCountdownTimer(
      BuildContext context, WidgetRef ref, DateTime targetTime) {
    // Create parameters for the auction start timer provider
    final params = (targetTime: targetTime, auctionId: auction.id);

    // Watch the auction start timer state
    final timerState = ref.watch(auctionStartTimerProvider(params));

    return Container(
      // width: double.infinity,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 15),
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 5),
      decoration: BoxDecoration(
        color: AppColors.royalBlue,
        borderRadius: BorderRadius.circular(50),
        border: Border.all(color: AppColors.royalBlue),
      ),
      child: Row(
        children: [
          SvgPicture.asset(AppIcons.clock),
          const SizedBox(width: 8),
          Text(
            context.localizations.auction_starts_in,
            style: context.textTheme.bodyMedium?.copyWith(
              color: AppColors.white,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            ref
                .read(auctionStartTimerProvider(params).notifier)
                .formatDuration(timerState.remainingTime),
            style: context.textTheme.bodyMedium?.copyWith(
              color: AppColors.white,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
