import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/features/update_app/data/update_app_service.dart';

/// A test page to manually trigger the update app pages
class UpdateAppTestPage extends StatelessWidget {
  static const String route = '/update_app_test';

  const UpdateAppTestPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(context.localizations.update_app),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ElevatedButton(
              onPressed: () {
                // Get the UpdateAppService from GetIt and show the force update page
                final updateAppService = GetIt.I<UpdateAppService>();
                updateAppService.showManualForceUpdate();
              },
              child: Text(context.localizations.update_app_now),
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () {
                // Get the UpdateAppService from GetIt and show the soft update page
                final updateAppService = GetIt.I<UpdateAppService>();
                updateAppService.showManualSoftUpdate();
              },
              child: const Text('Show Soft Update'),
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () {
                // Get the UpdateAppService from GetIt and show the force update page with custom message
                final updateAppService = GetIt.I<UpdateAppService>();
                updateAppService.showManualForceUpdate(
                  customMessage:
                      'This is a custom update message for testing purposes.',
                );
              },
              child: const Text('Show Force Update with Custom Message'),
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () async {
                // Get the UpdateAppService from GetIt and test Remote Config
                final updateAppService = GetIt.I<UpdateAppService>();
                await updateAppService.testRemoteConfig();
              },
              child: const Text('Test Remote Config'),
            ),
          ],
        ),
      ),
    );
  }
}
