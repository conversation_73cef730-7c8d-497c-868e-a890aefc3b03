import 'package:bond_core/bond_core.dart';
import 'package:bond_form/bond_form.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:zod/core/app_theme.dart';
import 'package:zod/core/resources/app_colors.dart';
import 'package:zod/core/resources/app_icons.dart';

import '../../providers/login_form_provider.dart';
import '../../providers/phone_number_validation_provider.dart';

class LoginForm extends ConsumerStatefulWidget {
  const LoginForm({super.key});

  @override
  ConsumerState<LoginForm> createState() => _LoginFormState();
}

class _LoginFormState extends ConsumerState<LoginForm> {
  final FocusNode _phoneFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _phoneFocusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _phoneFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final formController = ref.read(loginProvider.notifier);
    final formState = ref.watch(loginProvider);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          formState.label('phone'),
          style: context.textTheme.bodyMedium?.textColor,
          textAlign: TextAlign.start,
        ),
        const SizedBox(height: 8),
        Directionality(
          textDirection: TextDirection.ltr,
          child: TextFormField(
            focusNode: _phoneFocusNode,
            maxLength: 9,
            textInputAction: TextInputAction.done,
            style: Theme.of(context).textTheme.bodyMedium?.textColor,
            keyboardType: TextInputType.phone,
            initialValue: formState.textFieldValue('phone'),
            decoration: InputDecoration(
              labelStyle: appTextTheme.bodySmall?.textColor,
              errorText: formState.error('phone'),
              hintText: '000 000 000',
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide:
                    const BorderSide(width: 1, color: AppColors.borderColor),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide:
                    const BorderSide(width: 1, color: AppColors.borderColor),
              ),
              hintStyle: Theme.of(context).textTheme.bodyMedium?.textColor,
              prefixIcon: Padding(
                padding: EdgeInsetsDirectional.only(start: 16, end: 8),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SvgPicture.asset(AppIcons.saFlag),
                    const SizedBox(width: 4),
                    Text(
                      '+966',
                      style: Theme.of(context).textTheme.bodyMedium?.textColor,
                    ),
                  ],
                ),
              ),
              prefixIconConstraints:
                  const BoxConstraints(minWidth: 0, minHeight: 0),
            ),
            onChanged: (value) {
              ref.read(phoneNumberValidationProvider.notifier).state = false;

              formController.updateText('country_code', '966');
              formController.updateText('phone', value);

              if (value.length == 9) {
                ref.read(phoneNumberValidationProvider.notifier).state = true;
              }
            },
          ),
        )
      ],
    );
  }
}
