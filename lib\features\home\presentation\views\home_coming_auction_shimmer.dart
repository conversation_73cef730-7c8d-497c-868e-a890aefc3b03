import 'package:flutter/material.dart';
import 'package:skeletonizer/skeletonizer.dart' show Skeletonizer;
import 'package:zod/core/resources/app_colors.dart';
import 'package:zod/features/auction/data/models/auction.dart';

import 'home_coming_auctions_item_view.dart';

class HomeComingAuctionShimmer extends StatelessWidget {
  const HomeComingAuctionShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Skeletonizer(
        containersColor: AppColors.lightGray,
        child: SizedBox(
          height: 340,
          child: ListView.separated(
            separatorBuilder: (BuildContext context, int index) {
              return SizedBox(width: 8);
            },
            shrinkWrap: true,
            itemCount: Auction.fakeList.length,
            scrollDirection: Axis.horizontal,
            padding: EdgeInsetsDirectional.only(start: 16), // Add start padding
            itemBuilder: (context, index) {
              final auction = Auction.fakeList[index];
              return HomeComingAuctionsItemView(
                auction: auction,
                totalItems: Auction.fakeList.length,
                index: index,
              );
            },
          ),
        ));
  }
}
