<?php

namespace App\Notifications;

use App\Models\ContactSubmission;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class NewContactSubmissionNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(
        public ContactSubmission $contactSubmission
    ) {
        //
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('New Contact Form Submission - Rydo')
            ->greeting('Hello Admin!')
            ->line('A new contact form submission has been received.')
            ->line('**From:** ' . $this->contactSubmission->email)
            ->line('**Submitted:** ' . $this->contactSubmission->created_at->format('M d, Y \a\t g:i A'))
            ->line('**Message:**')
            ->line($this->contactSubmission->message)
            ->action('View in Admin Panel', url('/admin/contact-submissions/' . $this->contactSubmission->id))
            ->line('Please review and respond to this submission as soon as possible.')
            ->salutation('Best regards, Rydo System');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'contact_submission_id' => $this->contactSubmission->id,
            'email' => $this->contactSubmission->email,
            'message' => $this->contactSubmission->truncated_message,
            'submitted_at' => $this->contactSubmission->created_at->toISOString(),
        ];
    }
}
