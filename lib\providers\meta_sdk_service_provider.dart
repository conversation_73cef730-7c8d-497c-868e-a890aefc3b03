import 'dart:developer';
import 'package:bond_core/bond_core.dart';
import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'package:zod/core/services/meta_sdk_service.dart';

class MetaSdkServiceProvider extends ServiceProvider {
  @override
  Future<void> register(GetIt it) async {
    try {
      // Register the Meta SDK service as a singleton
      final metaSdkService = MetaSdkService();
      it.registerSingleton(metaSdkService);

      // Initialize the Meta SDK in a non-blocking way
      _initializeMetaSdk(metaSdkService);
    } catch (e) {
      debugPrint('Error registering Meta SDK service: $e');
    }
  }

  // Initialize Meta SDK in a non-blocking way
  Future<void> _initializeMetaSdk(MetaSdkService metaSdkService) async {
    try {
      // Initialize the Meta SDK
      await metaSdkService.initialize();

      // Log app open event
      await metaSdkService.logAppOpen();

      log('Meta SDK initialization completed successfully',
          name: 'MetaSdkServiceProvider');
    } catch (e) {
      debugPrint('Error initializing Meta SDK: $e');
    }
  }
}
