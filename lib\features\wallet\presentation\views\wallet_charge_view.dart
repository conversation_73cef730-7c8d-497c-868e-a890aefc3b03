import 'dart:developer';

import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/core/app_theme.dart';
import 'package:zod/core/app_widgets.dart';
import 'package:zod/core/resources/app_colors.dart';
import 'package:zod/core/resources/app_icons.dart';
import 'package:zod/features/app/app_providers.dart';
import 'package:zod/features/auth/auth.dart';
import 'package:zod/features/wallet/presentation/providers/suggested_points_provider.dart';
import 'package:zod/features/wallet/presentation/providers/wallet_charge_provider.dart';

class WalletChargeView extends ConsumerStatefulWidget {
  const WalletChargeView({super.key});

  static const String route = '/wallet_charge';

  @override
  ConsumerState<WalletChargeView> createState() => _WalletChargeViewState();
}

class _WalletChargeViewState extends ConsumerState<WalletChargeView> {
  @override
  void initState() {
    super.initState();
    // Load suggested points when the view is initialized
    Future.microtask(() {
      ref.read(walletChargeProvider.notifier).loadSuggestedPoints();

      // Track view plans event when the screen is loaded
      // trackViewPlans();
      log('Wallet charge view loaded', name: 'WalletChargeView');
    });
  }

  TextEditingController _pointsController = TextEditingController(text: "0");
  @override
  Widget build(BuildContext context) {
    final walletState = ref.watch(walletChargeProvider);
    final suggestedPointsAsync = ref.watch(suggestedPointsProvider);
    ref.listen(walletChargeProvider, (previous, next) {
      _pointsController.text = next.points.toString();
    });

    return SingleChildScrollView(
      child: suggestedPointsAsync.when(
        data: (suggestedPoints) {
          log("button charge01 ${walletState.points}");
          log("button charge02 ${walletState.minChargePoint}");
          log("button charge03 ${walletState.points}");
          log("button charge04 ${walletState.max}");
          log("button charge ${walletState.points >= walletState.minChargePoint && walletState.points <= walletState.max}");
          log("button charge2 ${walletState.points >= walletState.minChargePoint}");
          log("button charge3 ${walletState.points <= walletState.max}");
          return Container(
            padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom),
            color: AppColors.white,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 1.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Column(
                      children: [
                        SizedBox(height: 16),
                        Text.rich(
                          TextSpan(
                            text: context.localizations.yourCurrentBalance,
                            style: context.textTheme.titleSmall!.medium,
                            children: <InlineSpan>[
                              TextSpan(text: " "),
                              WidgetSpan(
                                child: Transform.translate(
                                  offset: Offset(0, -5),
                                  child: SvgPicture.asset(
                                    AppIcons.coin,
                                    alignment: Alignment.topCenter,
                                  ),
                                ),
                              ),
                              TextSpan(
                                text:
                                    " ${Auth.check() ? Auth.user().availablePoint : 0}",
                                style: context.textTheme.titleSmall!.w700,
                              ),
                            ],
                          ),
                        ),
                        SizedBox(height: 16),
                      ],
                    ),
                  ),
                  Container(
                    color: AppColors.softWhite,
                    padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        ...suggestedPoints.options.map((option) => Expanded(
                              child: GestureDetector(
                                behavior: HitTestBehavior.translucent,
                                onTap: () => ref
                                    .read(walletChargeProvider.notifier)
                                    .setPoints(option.points),
                                child: _pointOption(
                                  context,
                                  option.points,
                                  highlight:
                                      walletState.points == option.points,
                                  isDefault: option.isDefault,
                                ),
                              ),
                            )),
                      ],
                    ),
                  ),
                  SizedBox(height: 20),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      IconButton(
                        icon: walletState.points > suggestedPoints.minPoints
                            ? SvgPicture.asset(AppIcons.walletMinus)
                            : SvgPicture.asset(AppIcons.walletMinusDis),
                        onPressed:
                            walletState.points > suggestedPoints.minPoints
                                ? ref
                                    .read(walletChargeProvider.notifier)
                                    .decreasePoints
                                : null,
                      ),
                      Expanded(
                        child: Container(
                          // padding: EdgeInsets.symmetric(
                          //     horizontal: 30, vertical: 13),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10),
                            // border: Border.all(
                            //   color: (walletState.error ?? "").isEmpty
                            //       ? AppColors.lightGray
                            //       : AppColors.crimsonRed,
                            //   width: 1.5,
                            // ),
                          ),
                          alignment: Alignment.center,
                          child: TextFormField(
                            maxLength: 6,
                            // initialValue: walletState.points.toString(),
                            controller: _pointsController,
                            textAlign: TextAlign.center,
                            style: context.textTheme.bodyMedium!.w700,
                            decoration: InputDecoration(
                              counter: SizedBox(),
                              // prefix,
                              prefixIcon: Transform.translate(
                                offset: Offset(
                                    ref
                                            .read(localProvider)
                                            .languageCode
                                            .toLowerCase()
                                            .contains("en")
                                        ? 20
                                        : -20,
                                    0),
                                child: SvgPicture.asset(
                                  AppIcons.coin,
                                  alignment: Alignment.topCenter,
                                ),
                              ),
                              prefixIconConstraints: BoxConstraints(
                                minWidth: 20,
                                minHeight: 20,
                              ),

                              labelStyle: context.textTheme.bodyMedium!.w700,
                              //text style
                              hintText: context.localizations.points,
                              hintStyle: context.textTheme.bodyMedium!.w700
                                  .copyWith(color: AppColors.lightSlateGray),

                              filled: true,
                              fillColor: AppColors.white,
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(10),
                                borderSide: BorderSide(
                                  color: (walletState.error ?? "").isEmpty
                                      ? AppColors.lightGray
                                      : AppColors.crimsonRed,
                                  width: 1.5,
                                ),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(10),
                                borderSide: BorderSide(
                                  color: (walletState.error ?? "").isEmpty
                                      ? AppColors.lightGray
                                      : AppColors.crimsonRed,
                                  width: 1.5,
                                ),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(10),
                                borderSide: BorderSide(
                                  color: (walletState.error ?? "").isEmpty
                                      ? AppColors.lightGray
                                      : AppColors.crimsonRed,
                                  width: 1.5,
                                ),
                              ),
                            ),
                            keyboardType: TextInputType.number,
                            onChanged: (value) {
                              ref
                                  .read(walletChargeProvider.notifier)
                                  .setPoints(int.parse(value));
                            },
                          ),
                        ),
                      ),
                      IconButton(
                        icon: ((walletState.points < walletState.max))
                            ? SvgPicture.asset(AppIcons.walletPlus)
                            : SvgPicture.asset(AppIcons.walletPlusDis),
                        onPressed: ((walletState.points < walletState.max))
                            ? ref
                                .read(walletChargeProvider.notifier)
                                .increasePoints
                            : null,
                      ),
                    ],
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Column(
                      children: [
                        if (walletState.error?.isNotEmpty ?? false)
                          Padding(
                            padding: const EdgeInsets.only(top: 8.0),
                            child: Row(
                              children: [
                                SvgPicture.asset(AppIcons.wrong),
                                SizedBox(width: 8),
                                Text(
                                  walletState.error!,
                                  style: context.textTheme.bodySmall!
                                      .copyWith(color: AppColors.crimsonRed),
                                ),
                              ],
                            ),
                          ),
                        SizedBox(height: 20),
                        AppButton(
                          title: "",
                          loading: walletState.isLoading,
                          customChild: Text.rich(TextSpan(
                            text: context.localizations.buy_pointsfor,
                            style: !(walletState.error?.isEmpty ?? true)
                                ? context.textTheme.labelLarge
                                    ?.copyWith(color: AppColors.lightSlateGray)
                                : context.textTheme.labelLarge!.white,
                            children: <InlineSpan>[
                              WidgetSpan(
                                child: Transform.translate(
                                  offset: Offset(0, -5),
                                  child: SvgPicture.asset(
                                    AppIcons.riyal,
                                    color: AppColors.white,
                                  ),
                                ),
                              ),
                              if (walletState.points >=
                                      walletState.minChargePoint &&
                                  walletState.points <= walletState.max)
                                TextSpan(
                                  text: " ${walletState.price} ",
                                ),
                            ],
                          )),
                          onPressed: () => ref
                              .read(walletChargeProvider.notifier)
                              .chargeWallet(),
                          enabled: (walletState.points >=
                                  walletState.minChargePoint &&
                              walletState.points <= walletState.max),
                        ),
                        Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 20, vertical: 10),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                    context
                                        .localizations.priceDoesNotIncludeTaxes,
                                    style:
                                        context.textTheme.labelSmall?.medium),
                              ],
                            )),
                        SizedBox(height: 20),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        },
        loading: () => Center(
          child: Padding(
            padding: const EdgeInsets.all(32.0),
            child: CircularProgressIndicator(),
          ),
        ),
        error: (error, stack) => Center(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text(
              error.toString(),
              style: context.textTheme.bodyMedium!
                  .copyWith(color: AppColors.crimsonRed),
            ),
          ),
        ),
      ),
    );
  }

  Widget _pointOption(BuildContext context, int points,
      {bool highlight = false, bool isDefault = false}) {
    return Stack(
      // fit: StackFit.expand,
      clipBehavior: Clip.none,
      children: [
        Container(
          margin: EdgeInsets.symmetric(horizontal: 4),
          padding: EdgeInsets.symmetric(horizontal: 20, vertical: 10),
          decoration: BoxDecoration(
            color: highlight ? AppColors.periwinkle : AppColors.lavenderMist,
            borderRadius: BorderRadius.circular(10),
            border: Border.all(
              color: highlight ? AppColors.primaryColor : AppColors.periwinkle,
              width: 1,
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SvgPicture.asset(
                AppIcons.coin,
              ),
              SizedBox(width: 4),
              Text(
                "$points",
                style: context.textTheme.labelLarge!
                    .copyWith(color: AppColors.steelGray),
              ),
            ],
          ),
        ),
        if (isDefault)
          Positioned.directional(
            textDirection: Directionality.of(context),
            end: 20,
            top: -5,
            child: SvgPicture.asset(
              AppIcons.flash,
              width: 15,
              // height: 10,
            ),
          ),
      ],
    );
  }
}
