import 'package:bond_core/bond_core.dart';
import 'package:bond_form/bond_form.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zod/app/routes.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/core/app_theme.dart';
import 'package:zod/core/app_widgets.dart';
import 'package:zod/core/notifications/notification_alert_view.dart';
import 'package:zod/features/auction/data/models/extra_time_reason.dart';
import 'package:zod/features/auction/presentation/providers/claim_prize_provider.dart';
import 'package:zod/features/auction/presentation/providers/extra_time_provider.dart';
import 'package:zod/features/auction/presentation/providers/extra_time_reason_provider.dart';
import 'package:zod/features/auction/presentation/views/extra_time/extra_time_shimmer_view.dart';
import 'package:zod/features/auction/presentation/views/extra_time/hours_radio_selection_view.dart';
import 'package:zod/features/auction/presentation/views/extra_time/reason_drop_down_view.dart';
import 'package:zod/features/auth/data/errors/validation_error.dart';
import 'package:zod/features/auth/presentation/providers/me_provider.dart';

class ExtraTimePage extends ConsumerWidget {
  const ExtraTimePage({super.key, required this.id});

  final String id;
  static const String route = '/extra-time/:id';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formController =
        ref.read((extraTimeFormProvider(int.parse(id))).notifier);
    final formState = ref.watch(extraTimeFormProvider(int.parse(id)));

    ref.listen(extraTimeFormProvider(int.parse(id)), (previous, next) {
      _formStateListener(context, previous, next, ref);
    });

    // Check if data is loading
    final isLoading = ref.watch(extraTimeReasonProvider).isLoading ||
        ref.watch(extraTimeHoursProvider).isLoading;

    if (isLoading) {
      return const ExtraTimeShimmerView();
    }

    return Padding(
      // Add padding to account for keyboard
      padding: EdgeInsets.only(
        left: 16,
        right: 16,
        bottom: MediaQuery.of(context).viewInsets.bottom + 16,
      ),
      child: SingleChildScrollView(
        // Make sure the content can scroll when keyboard appears
        physics: const AlwaysScrollableScrollPhysics(),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              context.localizations.report_an_issue,
              style: context.textTheme.titleSmall?.w700,
            ),
            const SizedBox(height: 6),
            Text(
              context.localizations.choose_a_reason,
              style: context.textTheme.bodySmall,
            ),
            const SizedBox(height: 8),
            ReasonDropdown(
              reasonItems: ref.watch(extraTimeReasonProvider),
              selectedValue: formState
                  .asyncDropDownValue<ExtraTimeReason?>('issue_reason'),
              hintText: formState.label('issue_reason'),
              errorText: formState.error('issue_reason'),
              onChanged: (value) => formController.updateAsyncDropDown(
                'issue_reason',
                value,
              ),
            ),
            const SizedBox(height: 10),
            Text(
              context.localizations.describe_issue_optional,
              style: context.textTheme.bodySmall,
            ),
            const SizedBox(height: 8),
            TextField(
              onChanged: (value) =>
                  formController.updateText('description', value),
              maxLines: 2,
              decoration: InputDecoration(
                hintText: context.localizations.type_your_problem,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Colors.grey.shade300),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Colors.grey.shade300),
                ),
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
            ),
            const SizedBox(height: 10),
            Text(
              context.localizations.select_hours,
              style: context.textTheme.bodySmall,
            ),
            const SizedBox(height: 8),
            HoursRadioSelectionView(
              hoursItems: ref.watch(extraTimeHoursProvider),
              selectedValue: formState.asyncDropDownValue<int?>('hours'),
              onChanged: (value) => formController.updateAsyncDropDown(
                'hours',
                value,
              ),
            ),
            const SizedBox(height: 16),
            AppButton(
              title: context.localizations.send,
              onPressed: formController.submit,
              enabled: formState.status == BondFormStateStatus.valid,
              loading: formState.status == BondFormStateStatus.submitting,
            )
          ],
        ),
      ),
    );
  }

  void _formStateListener(
      BuildContext context,
      BondFormState<String, ValidationError>? previous,
      BondFormState<String, ValidationError> next,
      WidgetRef ref) async {
    if (next.status == BondFormStateStatus.submitted) {
      NotificationAlert.showLocalNotification(
        next.success,
        type: ToastType.success,
      );
      ref.invalidate(claimPrizeProvider(int.parse(id)));
      ref.invalidate(meProvider);
      goRouter.pop();
    }
  }
}
