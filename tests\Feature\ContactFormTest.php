<?php

namespace Tests\Feature;

use App\Models\ContactSubmission;
use App\Models\User;
use App\Notifications\NewContactSubmissionNotification;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Notification;
use Tests\TestCase;

class ContactFormTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        Notification::fake();
    }

    public function test_contact_form_submission_with_valid_data(): void
    {
        $response = $this->postJson('/api/contact', [
            'email' => '<EMAIL>',
            'message' => 'This is a test message with sufficient length to pass validation.',
        ]);

        $response->assertStatus(201)
            ->assertJson([
                'success' => true,
                'message' => 'Thank you for your message. We will get back to you soon!',
            ])
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'email',
                    'message',
                    'status',
                    'created_at',
                    'updated_at',
                ],
            ]);

        $this->assertDatabaseHas('contact_submissions', [
            'email' => '<EMAIL>',
            'message' => 'This is a test message with sufficient length to pass validation.',
            'status' => 'unread',
        ]);
    }

    public function test_contact_form_validation_requires_email(): void
    {
        $response = $this->postJson('/api/contact', [
            'message' => 'This is a test message with sufficient length.',
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['email']);
    }

    public function test_contact_form_validation_requires_valid_email(): void
    {
        $response = $this->postJson('/api/contact', [
            'email' => 'invalid-email',
            'message' => 'This is a test message with sufficient length.',
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['email']);
    }

    public function test_contact_form_validation_requires_message(): void
    {
        $response = $this->postJson('/api/contact', [
            'email' => '<EMAIL>',
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['message']);
    }

    public function test_contact_form_validation_message_minimum_length(): void
    {
        $response = $this->postJson('/api/contact', [
            'email' => '<EMAIL>',
            'message' => 'Short',
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['message']);
    }

    public function test_contact_form_validation_message_maximum_length(): void
    {
        $response = $this->postJson('/api/contact', [
            'email' => '<EMAIL>',
            'message' => str_repeat('a', 2001), // Exceeds 2000 character limit
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['message']);
    }

    public function test_contact_form_validation_email_maximum_length(): void
    {
        $response = $this->postJson('/api/contact', [
            'email' => str_repeat('a', 250) . '@example.com', // Exceeds 255 character limit
            'message' => 'This is a test message with sufficient length.',
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['email']);
    }

    public function test_contact_form_stores_ip_address_and_user_agent(): void
    {
        $response = $this->postJson('/api/contact', [
            'email' => '<EMAIL>',
            'message' => 'This is a test message with sufficient length.',
        ], [
            'User-Agent' => 'Test User Agent',
        ]);

        $response->assertStatus(201);

        $submission = ContactSubmission::first();
        $this->assertNotNull($submission->ip_address);
        $this->assertEquals('Test User Agent', $submission->user_agent);
    }

    public function test_contact_form_sends_notification_to_admin_users(): void
    {
        // Create admin users
        $admin1 = User::factory()->create(['is_admin' => true]);
        $admin2 = User::factory()->create(['email' => '<EMAIL>']);

        $response = $this->postJson('/api/contact', [
            'email' => '<EMAIL>',
            'message' => 'This is a test message with sufficient length.',
        ]);

        $response->assertStatus(201);

        Notification::assertSentTo(
            [$admin1, $admin2],
            NewContactSubmissionNotification::class
        );
    }

    public function test_contact_form_rate_limiting(): void
    {
        // Make 5 requests (the limit)
        for ($i = 0; $i < 5; $i++) {
            $response = $this->postJson('/api/contact', [
                'email' => "test{$i}@example.com",
                'message' => 'This is a test message with sufficient length.',
            ]);
            $response->assertStatus(201);
        }

        // The 6th request should be rate limited
        $response = $this->postJson('/api/contact', [
            'email' => '<EMAIL>',
            'message' => 'This is a test message with sufficient length.',
        ]);

        $response->assertStatus(429); // Too Many Requests
    }

    public function test_contact_form_trims_and_normalizes_input(): void
    {
        $response = $this->postJson('/api/contact', [
            'email' => '  <EMAIL>  ',
            'message' => '  This is a test message with sufficient length.  ',
        ]);

        $response->assertStatus(201);

        $submission = ContactSubmission::first();
        $this->assertEquals('<EMAIL>', $submission->email);
        $this->assertEquals('This is a test message with sufficient length.', $submission->message);
    }

    public function test_contact_submission_model_scopes(): void
    {
        ContactSubmission::factory()->create(['status' => 'unread']);
        ContactSubmission::factory()->create(['status' => 'read']);
        ContactSubmission::factory()->create(['status' => 'resolved']);

        $this->assertEquals(1, ContactSubmission::unread()->count());
        $this->assertEquals(1, ContactSubmission::read()->count());
        $this->assertEquals(1, ContactSubmission::resolved()->count());
    }

    public function test_contact_submission_status_methods(): void
    {
        $submission = ContactSubmission::factory()->create(['status' => 'unread']);

        $submission->markAsRead();
        $this->assertEquals('read', $submission->fresh()->status);

        $submission->markAsResolved();
        $this->assertEquals('resolved', $submission->fresh()->status);
    }

    public function test_contact_submission_truncated_message_attribute(): void
    {
        $longMessage = str_repeat('This is a long message. ', 20);
        $submission = ContactSubmission::factory()->create(['message' => $longMessage]);

        $truncated = $submission->truncated_message;
        $this->assertLessThanOrEqual(100, strlen($truncated));
        $this->assertStringEndsWith('...', $truncated);
    }

    public function test_contact_submission_status_color_attribute(): void
    {
        $unreadSubmission = ContactSubmission::factory()->create(['status' => 'unread']);
        $readSubmission = ContactSubmission::factory()->create(['status' => 'read']);
        $resolvedSubmission = ContactSubmission::factory()->create(['status' => 'resolved']);

        $this->assertEquals('danger', $unreadSubmission->status_color);
        $this->assertEquals('warning', $readSubmission->status_color);
        $this->assertEquals('success', $resolvedSubmission->status_color);
    }
}
