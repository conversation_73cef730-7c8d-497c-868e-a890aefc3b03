import 'package:flutter/material.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/core/resources/app_colors.dart';
import 'package:zod/features/auction/data/models/auction.dart';

class EntryFeeDisplayWidget extends StatelessWidget {
  final Auction auction;
  final TextStyle? style;
  final TextStyle? discountedStyle;
  final TextStyle? strikethroughStyle;
  final bool hideCurrency;
  const EntryFeeDisplayWidget({
    super.key,
    required this.auction,
    this.style,
    this.discountedStyle,
    this.strikethroughStyle,
    this.hideCurrency = false,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        // Show original price with strikethrough if discount is active
        if (auction.strikethroughEntryFee != null)
          Text(
            '${auction.strikethroughEntryFee} ${hideCurrency ? '' : context.localizations.currency}',
            style: strikethroughStyle ??
                TextStyle(
                  decoration: TextDecoration.lineThrough,
                  color: Colors.grey,
                  fontSize: 14,
                ),
          ),

        // Add spacing if both prices are shown
        if (auction.hasEntryFeeDiscount) const SizedBox(width: 4),

        // Show effective price (discounted or original)
        Text(
          '${auction.effectiveEntryFee ?? 0} ${hideCurrency ? '' : context.localizations.currency}',
          style: auction.hasEntryFeeDiscount
              ? (discountedStyle ??
                  TextStyle(
                    color: AppColors.primaryColor,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ))
              : (style ??
                  TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  )),
        ),
      ],
    );
  }
}

// Extension to make it easy to use throughout the app
extension AuctionEntryFeeExtension on Auction {
  Widget buildEntryFeeDisplay({
    TextStyle? style,
    TextStyle? discountedStyle,
    TextStyle? strikethroughStyle,
    bool hideCurrency = false,
  }) {
    return EntryFeeDisplayWidget(
      auction: this,
      style: style,
      discountedStyle: discountedStyle,
      strikethroughStyle: strikethroughStyle,
      hideCurrency: hideCurrency,
    );
  }
}
