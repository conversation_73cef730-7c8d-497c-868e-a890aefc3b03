import 'dart:async';

import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:zod/app/routes.dart';

import '../core/resources/app_assets.dart';
import '../core/resources/app_colors.dart';
import '../core/services/onboarding_service.dart';
import 'update_app/data/update_app_service.dart';

class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  static final splashPageStreamController = StreamController<int>.broadcast();

  @override
  State<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> {
  String _version = "";

  @override
  void initState() {
    super.initState();
    _loadVersion();
    _initializeApp();
  }

  Future<void> _initializeApp() async {
    await Future.delayed(const Duration(seconds: 2));

    try {
      // Check if force update is active - if so, don't navigate away!
      if (UpdateAppService.isForceUpdateActive) {
        debugPrint(
            '🔴 SplashPage: Force update is active, not navigating to home');
        return;
      }

      final onboardingService = OnboardingService();
      final hasSeenOnboarding = await onboardingService.hasSeenOnboarding();

      if (!hasSeenOnboarding) {
        goRouter.go('/onboarding');
      } else {
        goRouter.go('/home');
      }
    } catch (e) {
      // If there's an error, go to home (but only if no force update)
      if (!UpdateAppService.isForceUpdateActive) {
        goRouter.go('/home');
      }
    }

    SplashPage.splashPageStreamController.add(1);
  }

  Future<void> _loadVersion() async {
    final packageInfo = await PackageInfo.fromPlatform();
    setState(() {
      _version = packageInfo.version;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      body: SafeArea(
        child: Stack(
          children: [
            Positioned.fill(
              child: SvgPicture.asset(AppImagesAssets.splashBackground),
            ),
            Positioned(
              right: 0,
              bottom: 0,
              child: SvgPicture.asset(AppImagesAssets.splashPart),
            ),
            Center(
              child: SvgPicture.asset(
                AppImagesAssets.zod,
                width: 114,
                height: 108,
              ),
            ),
            Positioned(
              left: 0,
              right: 0,
              bottom: 56,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      backgroundColor: AppColors.lightSlateGray,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        AppColors.secondaryColor,
                      ),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _version,
                    style: context.textTheme.bodyMedium,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
