<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\ContactSubmissionRequest;
use App\Http\Resources\ContactSubmissionResource;
use App\Models\ContactSubmission;
use App\Notifications\NewContactSubmissionNotification;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Log;

class ContactController extends Controller
{
    /**
     * Handle contact form submission.
     *
     * @param ContactSubmissionRequest $request
     * @return JsonResponse
     */
    public function __invoke(ContactSubmissionRequest $request): JsonResponse
    {
        try {
            // Create the contact submission
            $contactSubmission = ContactSubmission::create([
                'email' => $request->email,
                'message' => $request->message,
                'status' => 'unread',
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);

            // Send notification to admins
            $this->notifyAdmins($contactSubmission);

            // Log the submission for monitoring
            Log::info('New contact form submission', [
                'id' => $contactSubmission->id,
                'email' => $contactSubmission->email,
                'ip_address' => $contactSubmission->ip_address,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Thank you for your message. We will get back to you soon!',
                'data' => ContactSubmissionResource::make($contactSubmission),
            ], 201);

        } catch (\Exception $e) {
            Log::error('Contact form submission failed', [
                'error' => $e->getMessage(),
                'email' => $request->email ?? 'unknown',
                'ip_address' => $request->ip(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Sorry, there was an error processing your request. Please try again later.',
                'errors' => ['general' => ['Unable to process your request at this time.']],
            ], 500);
        }
    }

    /**
     * Send notification to admin users about new contact submission.
     *
     * @param ContactSubmission $contactSubmission
     * @return void
     */
    private function notifyAdmins(ContactSubmission $contactSubmission): void
    {
        try {
            // Get admin users (assuming they have a specific role or flag)
            $adminUsers = User::where('is_admin', true)
                ->orWhere('email', 'like', '%admin%')
                ->get();

            if ($adminUsers->isNotEmpty()) {
                Notification::send($adminUsers, new NewContactSubmissionNotification($contactSubmission));
            } else {
                // Fallback: log if no admin users found
                Log::warning('No admin users found to notify about contact submission', [
                    'submission_id' => $contactSubmission->id,
                ]);
            }
        } catch (\Exception $e) {
            // Don't fail the main request if notification fails
            Log::error('Failed to send admin notification for contact submission', [
                'submission_id' => $contactSubmission->id,
                'error' => $e->getMessage(),
            ]);
        }
    }
}
