import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zod/app/routes.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/core/notifications/notification_alert_view.dart';
import 'package:zod/features/auth/presentation/providers/me_provider.dart';
import 'package:zod/features/payment/data/api.dart';
import 'package:zod/features/payment/services/payment_service.dart';
import 'package:zod/features/wallet/data/models/wallet_charge_dto.dart';

final paymentProvider =
    StateNotifierProvider<PaymentProvider, PaymentState>((ref) {
  return PaymentProvider(
    paymentService: sl<PaymentService>(),
    paymentApi: sl<PaymentApi>(),
    ref: ref,
  );
});

class PaymentState {
  final bool isLoading;
  final String? error;

  PaymentState({
    this.isLoading = false,
    this.error,
  });

  PaymentState copyWith({
    bool? isLoading,
    String? error,
  }) {
    return PaymentState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

class PaymentProvider extends StateNotifier<PaymentState> {
  final PaymentService paymentService;
  final PaymentApi paymentApi;
  final Ref ref;

  PaymentProvider({
    required this.paymentService,
    required this.paymentApi,
    required this.ref,
  }) : super(PaymentState());

  Future<void> processCardPayment({
    required BuildContext context,
    required WalletChargeDto chargeDto,
  }) async {
    state = state.copyWith(isLoading: true);

    try {
      // Payment service will handle initialization internally
      final totalAmount =
          chargeDto.totalAmount ?? chargeDto.amountPrice.toDouble();

      // Start card payment with backend-calculated amount
      final result = await paymentService.startCardPayment(
        amount: totalAmount,
        currency: "SAR",
        cartDescription: "Purchase ${chargeDto.amountPoint} points",
        chargeDto: chargeDto,
      );

      // Check if the context is still valid
      if (context.mounted) {
        // Handle payment result
        _handlePaymentResult(
          context,
          result,
          chargeDto.copyWith(
            totalAmount: totalAmount,
            paymentMethod: 'card',
          ),
        );
      }
    } catch (e, s) {
      print(s);
      state = state.copyWith(isLoading: false, error: e.toString());
      NotificationAlert.showLocalNotification(
        e.toString(),
        type: ToastType.error,
      );
    }
  }

  Future<void> processApplePayPayment({
    required BuildContext context,
    required WalletChargeDto chargeDto,
  }) async {
    state = state.copyWith(isLoading: true);

    try {
      // Payment service will handle initialization internally
      final totalAmount =
          chargeDto.totalAmount ?? chargeDto.amountPrice.toDouble();

      // Start Apple Pay payment with backend-calculated amount
      final result = await paymentService.startApplePayPayment(
        amount: totalAmount,
        currency: "SAR",
        cartDescription: "Purchase ${chargeDto.amountPoint} points",
        chargeDto: chargeDto,
      );

      // Check if the context is still valid
      if (context.mounted) {
        // Handle payment result
        _handlePaymentResult(
          context,
          result,
          chargeDto.copyWith(
            totalAmount: totalAmount,
            paymentMethod: 'apple_pay',
          ),
        );
      }
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      NotificationAlert.showLocalNotification(
        e.toString(),
        type: ToastType.error,
      );
    }
  }

  Future<void> _handlePaymentResult(
    BuildContext context,
    Map<String, dynamic> result,
    WalletChargeDto chargeDto,
  ) async {
    if (result["status"] == "success") {
      final transactionDetails = result["data"];

      print(">>>>>>>>>" + transactionDetails.toString());
      if (transactionDetails["isSuccess"]) {
        // Payment successful, update wallet
        try {
          // Update chargeDto with payment reference
          final updatedChargeDto = chargeDto.copyWith(
            paymentReference: transactionDetails["transactionReference"],
            transaction_id: transactionDetails["cartID"],
          );

          // Call API to process payment
          final chargeUser = await paymentApi.processPayment(updatedChargeDto);

          if (chargeUser.meta.success ?? false) {
            // Success notification
            NotificationAlert.showLocalNotification(
              chargeUser.meta.message ??
                  "You have successfully purchased points.",
              type: ToastType.success,
              align: Alignment.topCenter,
            );

            // Refresh user data
            ref.refresh(meProvider);

            goRouter.pop();
            goRouter
                .pop(); // Pop payment method selection and charge wallet screens
          } else {
            // API error
            NotificationAlert.showLocalNotification(
              chargeUser.meta.message ?? "",
              type: ToastType.error,
            );
          }
        } catch (e, s) {
          print(s);
          // Error updating wallet
          NotificationAlert.showLocalNotification(
            e.toString(),
            type: ToastType.error,
          );
        }
      } else {
        // Payment failed
        NotificationAlert.showLocalNotification(
          context.localizations.payment_failed,
          type: ToastType.error,
        );
      }
    } else if (result["status"] == "error") {
      // Payment error
      NotificationAlert.showLocalNotification(
        result["message"] ?? context.localizations.payment_error,
        type: ToastType.error,
      );
    } else if (result["status"] == "event") {
      // Payment cancelled
      NotificationAlert.showLocalNotification(
        context.localizations.payment_cancelled,
        type: ToastType.warning,
      );
    }

    state = state.copyWith(isLoading: false);
  }
}
