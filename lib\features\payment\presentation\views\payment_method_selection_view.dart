import 'dart:developer';

import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:zod/app/routes.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/core/app_theme.dart';
import 'package:zod/core/resources/app_colors.dart';
import 'package:zod/core/resources/app_icons.dart';
import 'package:zod/features/payment/presentation/providers/payment_provider_new.dart'
    as payment;
import 'package:zod/features/payment/presentation/views/saved_cards_view.dart';
import 'package:zod/features/wallet/data/models/wallet_charge_dto.dart';

class PaymentMethodSelectionView extends ConsumerWidget {
  final WalletChargeDto chargeDto;

  const PaymentMethodSelectionView({super.key, required this.chargeDto});

  static const String route = '/payment_method_selection';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final paymentState = ref.watch(payment.paymentProvider);

    // Determine payment type based on context
    final isClaimPrizePayment =
        chargeDto.auctionId != null && chargeDto.amountPoint == 0;
    final isEntryFeePayment = chargeDto.auctionId != null &&
        chargeDto.amountPoint > 0 &&
        (chargeDto.auction?.isNotJoined == true);
    final isBiddingPayment = chargeDto.auctionId != null &&
        chargeDto.amountPoint > 0 &&
        (chargeDto.auction?.isJoined == true);
    final isRegularPurchase = chargeDto.auctionId == null;

    // For display purposes in order summary
    final subTotal = chargeDto.amountPrice.toDouble();
    final vatAmount = chargeDto.vatAmount ?? 0.0;
    final totalAmount = chargeDto.totalAmount ?? subTotal;

    String paymentTypeString = 'Unknown';
    if (isClaimPrizePayment)
      paymentTypeString = 'Claim Prize';
    else if (isEntryFeePayment)
      paymentTypeString = 'Entry Fee';
    else if (isBiddingPayment)
      paymentTypeString = 'Bidding';
    else if (isRegularPurchase) paymentTypeString = 'Regular Purchase';

    log("PaymentMethodSelectionView - Payment type: $paymentTypeString");
    log("PaymentMethodSelectionView - Display amounts:");
    log("  - Subtotal: $subTotal");
    log("  - VAT: $vatAmount");
    log("  - Total: $totalAmount");
    log("PaymentMethodSelectionView - Backend will calculate actual payment amounts");

    return Column(
      // mainAxisAlignment: MainAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (chargeDto.showOrderSummary)
          Column(
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 0,
                    ),
                    child: Text(
                      context.localizations.order_summary,
                      style: context.textTheme.titleSmall?.w700,
                    ),
                  ),
                ],
              ),
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(
                      color: AppColors.lightGray,
                      width: 1,
                      style: BorderStyle.solid,
                    ),
                  ),
                ),
                child: Row(
                  children: [
                    Text(
                      context.localizations.subtotal,
                      style: context.textTheme.bodyMedium,
                    ),
                    Spacer(),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const SizedBox(width: 16),
                        Row(
                          children: [
                            Text(
                              subTotal.toStringAsFixed(2),
                              style: context.textTheme.labelLarge?.medium,
                            ),
                            const SizedBox(width: 4),
                            SvgPicture.asset(AppIcons.riyal, height: 15),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(
                      color: AppColors.lightGray,
                      width: 1,
                      style: BorderStyle.solid,
                    ),
                  ),
                ),
                child: Row(
                  children: [
                    Text(
                      context.localizations.tax15,
                      style: context.textTheme.bodyMedium,
                    ),
                    Spacer(),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const SizedBox(width: 16),
                        Row(
                          children: [
                            Text(
                              vatAmount.toStringAsFixed(2),
                              style: context.textTheme.labelLarge?.medium,
                            ),
                            const SizedBox(width: 4),
                            SvgPicture.asset(AppIcons.riyal, height: 15),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(
                      color: AppColors.lightGray,
                      width: 1,
                      style: BorderStyle.solid,
                    ),
                  ),
                ),
                child: Row(
                  children: [
                    Text(
                      context.localizations.total,
                      style: context.textTheme.bodyMedium,
                    ),
                    Spacer(),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const SizedBox(width: 16),
                        Row(
                          children: [
                            Text(
                              totalAmount.toStringAsFixed(2),
                              style: context.textTheme.labelLarge?.medium,
                            ),
                            const SizedBox(width: 4),
                            SvgPicture.asset(AppIcons.riyal, height: 15),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        Container(
          padding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(24),
              topRight: Radius.circular(24),
            ),
          ),
          child: paymentState.isLoading
              ? Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: Center(child: CircularProgressIndicator()))
              : Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Drag handle

                    // Title
                    Align(
                      alignment: AlignmentDirectional.centerStart,
                      child: Text(
                        context.localizations.select_payment_method,
                        style: context.textTheme.bodyLarge?.w700,
                      ),
                    ),

                    SizedBox(height: 8),

                    // Apple Pay option (only show on iOS)
                    if (Theme.of(context).platform == TargetPlatform.iOS)
                      _buildPaymentButton(
                        context,
                        child: SvgPicture.asset(AppIcons.applePay, height: 20),
                        onTap: () => ref
                            .read(payment.paymentProvider.notifier)
                            .processApplePayPayment(
                              context: context,
                              chargeDto: chargeDto.copyWith(
                                vatAmount: vatAmount,
                                totalAmount: totalAmount,
                              ),
                            ),
                      ),
                    if (Theme.of(context).platform == TargetPlatform.iOS)
                      SizedBox(height: 16),

                    // Credit/Debit Card option
                    _buildPaymentButton(
                      context,
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          SvgPicture.asset(AppIcons.visa,
                              height: 15, color: AppColors.darkSteel),
                          SizedBox(width: 5),
                          SvgPicture.asset(AppIcons.masterCard, height: 15),
                        ],
                      ),
                      onTap: () => ref
                          .read(payment.paymentProvider.notifier)
                          .processCardPayment(
                            context: context,
                            chargeDto: chargeDto.copyWith(
                              vatAmount: vatAmount,
                              totalAmount: totalAmount,
                            ),
                          ),
                    ),

                    SizedBox(height: 10),

                    // Saved Cards Payment Option
                    SavedCardsView(chargeDto: chargeDto),
                    SizedBox(height: 10),
                  ],
                ),
        ),
      ],
    );
  }

  Widget _buildPaymentButton(
    BuildContext context, {
    required Widget child,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 12,
        ),
        decoration: BoxDecoration(
          color: AppColors.softWhite,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: AppColors.softSteel,
            width: 1,
          ),
        ),
        child: Center(child: child),
      ),
    );
  }
}
