library;

import 'dart:developer';

import 'package:bond_cache/bond_cache.dart';
import 'package:bond_core/bond_core.dart';
import 'package:zod/core/services/uxcam_service.dart';

import 'data/api.dart';
import 'data/models/user.dart';

export 'data/models/user.dart';
export 'data/models/user_meta.dart';
export 'routes.dart';

class Auth {
  static bool check() => Cache.has('token');

  static User user() => Cache.get('user');

  static String token() => Cache.get('token');

  static Future<User?> me() async {
    try {
      final response = await sl<AuthApi>().me();
      if (response != null) {
        // Tag user session in UXCam when user data is loaded
        await _tagUserInUXCam(response);
      }
      return response;
    } catch (e, s) {
      // log("e ${e.toString()}",stackTrace: s);
      return null;
    }
  }

  static Future<void> logout() async {
    try {
      try {
        await sl<AuthApi>().logout();
      } catch (_) {}
      await clear();

      // Log logout event in UXCam
      await UXCamService.logEvent('user_logout');
      log('User logout event logged in UXCam', name: 'Auth');
    } catch (e) {
      await clear();
    }
  }

  static Future<void> clear() async {
    await Cache.forget('token');
    await Cache.forget('user');
    await Cache.forget('user_meta');
  }

  /// Tag user session in UXCam with user information
  static Future<void> _tagUserInUXCam(User user) async {
    try {
      await UXCamService.tagUserSession(
        userId: user.id.toString(),
        userName: user.name ?? user.nickName,
        additionalProperties: {
          'user_id': user.id,
          'phone': user.fullPhone,
          'email': user.email,
          'nick_name': user.nickName,
          'total_points': user.totalPoint,
          'available_points': user.availablePoint,
          'hold_points': user.holdPoint,
          'has_complete_profile': user.isCompleteProfile,
          'has_address': user.hasAddress ?? false,
          'user_status': user.status?.name,
          'country_code': user.countryCode,
        },
      );

      // Log login event
      await UXCamService.logEvent('user_login', {
        'user_id': user.id,
        'login_method': 'phone',
        'has_complete_profile': user.isCompleteProfile,
        'total_points': user.totalPoint,
      });

      log('User session tagged in UXCam: ${user.id}', name: 'Auth');
    } catch (e) {
      log('Error tagging user in UXCam: $e', name: 'Auth');
    }
  }

  /// Call this method when user completes login for the first time
  static Future<void> onUserAuthenticated(User user) async {
    await _tagUserInUXCam(user);
  }
}
