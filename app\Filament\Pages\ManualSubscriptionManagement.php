<?php

namespace App\Filament\Pages;

use App\Filament\Actions\IsAdminAction;
use App\Models\User;
use App\Services\ManualSubscriptionService;
use Filament\Actions\Action;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Filament\Tables;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Table;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Builder;

class ManualSubscriptionManagement extends Page implements HasTable
{
    use InteractsWithTable;
    
    protected static ?string $navigationIcon = 'heroicon-o-user-plus';
    
    protected static string $view = 'filament.pages.manual-subscription-management';
    
    protected static ?string $navigationGroup = 'Subscription Management';
    
    protected static ?string $title = 'Manual Subscription Management';
    
    protected static ?string $navigationLabel = 'Manual Subscription';
    
    protected static ?int $navigationSort = 5;

    // TODO: Remove this once we have a proper way to manage subscriptions
    public static function canAccess(): bool
    {
        return false;
    }

    public function table(Table $table): Table
    {
        return $table
            ->query(User::query()->with(['latestSubscription', 'subscriptions'])->orderBy('created_at', 'desc'))
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID')
                    ->sortable(),
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('email')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('user_type')
                    ->badge()
                    ->formatStateUsing(function ($record) {
                        if ($record->is_anonymous) {
                            return 'Guest';
                        }
                        if ($record->subscriptions()->where('status', 'active')->exists()) {
                            return 'Premium';
                        }
                        return 'Registered';
                    })
                    ->color(function ($record) {
                        if ($record->is_anonymous) {
                            return 'gray';
                        }
                        if ($record->subscriptions()->where('status', 'active')->exists()) {
                            return 'success';
                        }
                        return 'info';
                    }),
                Tables\Columns\TextColumn::make('latestSubscription.status')
                    ->label('Current Status')
                    ->formatStateUsing(function ($state) {
                        if (!$state) {
                            return 'No Subscription';
                        }
                        return $state->getLabel();
                    })
                    ->badge()
                    ->color(function ($state) {
                        if (!$state) {
                            return 'gray';
                        }
                        return $state->getColor();
                    }),
                Tables\Columns\TextColumn::make('latestSubscription.end_date')
                    ->label('Expires At')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Joined')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('user_type')
                    ->options([
                        'guest' => 'Guest',
                        'registered' => 'Registered',
                        'premium' => 'Premium',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        if (empty($data['value'])) {
                            return $query;
                        }

                        return match ($data['value']) {
                            'guest' => $query->where('is_anonymous', true),
                            'registered' => $query->where('is_anonymous', false)
                                ->whereDoesntHave('subscriptions', function ($query) {
                                    $query->where('status', 'active');
                                }),
                            'premium' => $query->whereHas('subscriptions', function ($query) {
                                $query->where('status', 'active');
                            }),
                            default => $query,
                        };
                    }),
            ])
            ->actions([
                Tables\Actions\Action::make('triggerSubscriptionEvent')
                    ->label('Trigger Event')
                    ->icon('heroicon-o-bolt')
                    ->color('primary')
                    ->form([
                        Forms\Components\Section::make('Subscription Event Details')
                            ->description('Manually trigger a subscription event for this user. This will simulate a RevenueCat webhook event and automatically create payment history.')
                            ->schema([
                                Forms\Components\Select::make('event_type')
                                    ->label('Event Type')
                                    ->options(ManualSubscriptionService::getAvailableEventTypes())
                                    ->required()
                                    ->live()
                                    ->helperText('Select the type of subscription event to trigger'),

                                Forms\Components\Select::make('product_id')
                                    ->label('Product ID')
                                    ->options(ManualSubscriptionService::getAvailableProductIds())
                                    ->default('rydo_premium_monthly')
                                    ->required()
                                    ->live()
                                    ->visible(fn (Forms\Get $get) => in_array($get('event_type'), ['INITIAL_PURCHASE', 'RENEWAL', 'PRODUCT_CHANGE']))
                                    ->helperText('The product/plan to associate with this event'),

                                Forms\Components\Select::make('store')
                                    ->label('Store')
                                    ->options(ManualSubscriptionService::getAvailableStores())
                                    ->default('manual')
                                    ->required()
                                    ->helperText('The store/platform for this subscription'),

                                Forms\Components\TextInput::make('price')
                                    ->label('Price')
                                    ->numeric()
                                    ->step('0.01')
                                    ->default(fn (Forms\Get $get) => match($get('product_id')) {
                                        'rydo_premium_monthly' => 9.99,
                                        'rydo_premium_3_months' => 24.99,
                                        'rydo_premium_yearly' => 89.99,
                                        default => 9.99,
                                    })
                                    ->live()
                                    ->visible(fn (Forms\Get $get) => in_array($get('event_type'), ['INITIAL_PURCHASE', 'RENEWAL', 'PRODUCT_CHANGE']))
                                    ->helperText('The price of the subscription (payment history will be created automatically)'),

                                Forms\Components\TextInput::make('currency')
                                    ->label('Currency')
                                    ->default('USD')
                                    ->maxLength(3)
                                    ->visible(fn (Forms\Get $get) => in_array($get('event_type'), ['INITIAL_PURCHASE', 'RENEWAL', 'PRODUCT_CHANGE']))
                                    ->helperText('The currency code (e.g., USD, EUR)'),

                                Forms\Components\TextInput::make('duration_days')
                                    ->label('Duration (Days)')
                                    ->numeric()
                                    ->default(fn (Forms\Get $get) => match($get('product_id')) {
                                        'rydo_premium_monthly' => 30,
                                        'rydo_premium_3_months' => 90,
                                        'rydo_premium_yearly' => 365,
                                        default => 30,
                                    })
                                    ->live()
                                    ->visible(fn (Forms\Get $get) => in_array($get('event_type'), ['INITIAL_PURCHASE', 'RENEWAL', 'PRODUCT_CHANGE']))
                                    ->helperText('How many days the subscription should last'),

                                Forms\Components\Toggle::make('is_trial_period')
                                    ->label('Is Trial Period')
                                    ->default(false)
                                    ->visible(fn (Forms\Get $get) => $get('event_type') === 'INITIAL_PURCHASE')
                                    ->helperText('Whether this is a trial subscription (no payment history will be created for trials)'),

                                Forms\Components\TextInput::make('country_code')
                                    ->label('Country Code')
                                    ->default('US')
                                    ->maxLength(2)
                                    ->visible(fn (Forms\Get $get) => in_array($get('event_type'), ['INITIAL_PURCHASE', 'RENEWAL', 'PRODUCT_CHANGE']))
                                    ->helperText('The country code for the user'),
                            ]),
                    ])
                    ->action(function (User $record, array $data) {
                        $manualSubscriptionService = app(ManualSubscriptionService::class);
                        
                        $result = $manualSubscriptionService->triggerSubscriptionEvent($record, $data['event_type'], $data);
                        
                        if ($result['success']) {
                            Notification::make()
                                ->success()
                                ->title('Subscription Event Triggered')
                                ->body($result['message'] . (in_array($data['event_type'], ['INITIAL_PURCHASE', 'RENEWAL']) && !($data['is_trial_period'] ?? false) ? ' Payment history has been created.' : ''))
                                ->send();
                        } else {
                            Notification::make()
                                ->danger()
                                ->title('Failed to Trigger Event')
                                ->body($result['message'] ?? 'Unknown error occurred')
                                ->send();
                        }
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkAction::make('bulkTriggerEvent')
                    ->label('Trigger Event for Selected')
                    ->icon('heroicon-o-bolt')
                    ->color('primary')
                    ->form([
                        Forms\Components\Section::make('Bulk Subscription Event')
                            ->description('Trigger the same subscription event for all selected users. Payment history will be created automatically for purchase events.')
                            ->schema([
                                Forms\Components\Select::make('event_type')
                                    ->label('Event Type')
                                    ->options(ManualSubscriptionService::getAvailableEventTypes())
                                    ->required()
                                    ->live()
                                    ->helperText('Select the type of subscription event to trigger'),

                                Forms\Components\Select::make('product_id')
                                    ->label('Product ID')
                                    ->options(ManualSubscriptionService::getAvailableProductIds())
                                    ->default('rydo_premium_monthly')
                                    ->required()
                                    ->live()
                                    ->visible(fn (Forms\Get $get) => in_array($get('event_type'), ['INITIAL_PURCHASE', 'RENEWAL', 'PRODUCT_CHANGE']))
                                    ->helperText('The product/plan to associate with this event'),

                                Forms\Components\Select::make('store')
                                    ->label('Store')
                                    ->options(ManualSubscriptionService::getAvailableStores())
                                    ->default('manual')
                                    ->required()
                                    ->helperText('The store/platform for this subscription'),

                                Forms\Components\TextInput::make('price')
                                    ->label('Price')
                                    ->numeric()
                                    ->step('0.01')
                                    ->default(fn (Forms\Get $get) => match($get('product_id')) {
                                        'rydo_premium_monthly' => 9.99,
                                        'rydo_premium_3_months' => 24.99,
                                        'rydo_premium_yearly' => 89.99,
                                        default => 9.99,
                                    })
                                    ->live()
                                    ->visible(fn (Forms\Get $get) => in_array($get('event_type'), ['INITIAL_PURCHASE', 'RENEWAL', 'PRODUCT_CHANGE']))
                                    ->helperText('The price of the subscription (payment history will be created automatically)'),

                                Forms\Components\TextInput::make('duration_days')
                                    ->label('Duration (Days)')
                                    ->numeric()
                                    ->default(fn (Forms\Get $get) => match($get('product_id')) {
                                        'rydo_premium_monthly' => 30,
                                        'rydo_premium_3_months' => 90,
                                        'rydo_premium_yearly' => 365,
                                        default => 30,
                                    })
                                    ->live()
                                    ->visible(fn (Forms\Get $get) => in_array($get('event_type'), ['INITIAL_PURCHASE', 'RENEWAL', 'PRODUCT_CHANGE']))
                                    ->helperText('How many days the subscription should last'),
                            ]),
                    ])
                    ->action(function ($records, array $data) {
                        $manualSubscriptionService = app(ManualSubscriptionService::class);
                        $successCount = 0;
                        $failureCount = 0;
                        
                        foreach ($records as $user) {
                            $result = $manualSubscriptionService->triggerSubscriptionEvent($user, $data['event_type'], $data);
                            
                            if ($result['success']) {
                                $successCount++;
                            } else {
                                $failureCount++;
                            }
                        }
                        
                        Notification::make()
                            ->success()
                            ->title('Bulk Event Processing Complete')
                            ->body("Successfully processed: {$successCount}, Failed: {$failureCount}. Payment history has been created for purchase events.")
                            ->send();
                    })
                    ->deselectRecordsAfterCompletion(),
            ]);
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('viewPaymentHistory')
                ->label('View Payment History')
                ->icon('heroicon-o-credit-card')
                ->url('/dashboard/payment-histories')
                ->openUrlInNewTab(),
                
            Action::make('viewWebhookLogs')
                ->label('View Webhook Logs')
                ->icon('heroicon-o-document-text')
                ->url('/dashboard/revenuecat-webhook-events')
                ->openUrlInNewTab(),
                
            Action::make('refreshTable')
                ->label('Refresh')
                ->icon('heroicon-o-arrow-path')
                ->action(fn () => $this->resetTable()),
        ];
    }
} 