import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';
import 'package:zod/core/app_theme.dart';
import 'package:zod/core/resources/app_colors.dart';
import 'package:zod/core/widgets/network_image_widget.dart';
import 'package:zod/features/home/<USER>/models/how_work.dart';

class HowItWorkItemView extends StatelessWidget {
  const HowItWorkItemView({
    super.key,
    required this.work,
    required this.paddingBottom,
  });

  final HowWork work;
  final double paddingBottom;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding:
          EdgeInsetsDirectional.only(bottom: paddingBottom, start: 16, end: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(15),
              color: AppColors.softSilver,
            ),
            child: Center(
              child: NetworkImageView(
                work.icon,
                width: 24,
                height: 24,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  work.title,
                  style: context.textTheme.labelLarge,
                ),
                const SizedBox(height: 4),
                Text(
                  work.description,
                  style: context.textTheme.bodySmall?.slateGray,
                  softWrap: true,
                  overflow: TextOverflow.visible,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
