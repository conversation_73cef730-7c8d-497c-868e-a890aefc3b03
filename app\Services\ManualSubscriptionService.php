<?php

namespace App\Services;

use App\Enums\SubscriptionPlan;
use App\Enums\SubscriptionStatus;
use App\Http\Controllers\Api\RevenueCatWebhookController;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class ManualSubscriptionService
{
    public function __construct(
        private RevenueCatWebhookController $webhookController
    ) {}

    /**
     * Manually trigger a subscription event for a user
     *
     * @param User $user
     * @param string $eventType
     * @param array $eventData
     * @return array
     */
    public function triggerSubscriptionEvent(User $user, string $eventType, array $eventData = []): array
    {
        try {
            // Create a mock webhook payload
            $webhookPayload = $this->createMockWebhookPayload($user, $eventType, $eventData);
            
            // Create a mock request object
            $request = new Request();
            $request->replace($webhookPayload);
            
            // Add necessary headers to simulate a real webhook
            $request->headers->set('Content-Type', 'application/json');
            $request->headers->set('User-Agent', 'Manual-Dashboard-Trigger/1.0');
            $request->headers->set('X-Manual-Trigger', 'true');
            
                         Log::info('Manual subscription event triggered from dashboard', [
                 'user_id' => $user->id,
                 'event_type' => $eventType,
                 'triggered_by' => Auth::id(),
                 'payload' => $webhookPayload,
             ]);

            // Process through the existing webhook controller
            $response = $this->webhookController->handle($request);
            
            return [
                'success' => $response->getStatusCode() === 200,
                'status_code' => $response->getStatusCode(),
                'response' => $response->getData(true),
                'message' => $this->getEventMessage($eventType, $response->getStatusCode() === 200)
            ];

        } catch (\Exception $e) {
            Log::error('Manual subscription event failed', [
                'user_id' => $user->id,
                'event_type' => $eventType,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'message' => "Failed to trigger {$eventType} event: " . $e->getMessage()
            ];
        }
    }

    /**
     * Create a mock webhook payload for manual triggering
     *
     * @param User $user
     * @param string $eventType
     * @param array $eventData
     * @return array
     */
    private function createMockWebhookPayload(User $user, string $eventType, array $eventData = []): array
    {
        $baseEvent = [
            'type' => $eventType,
            'app_user_id' => (string) $user->id,
            'customer_info' => [
                'customer_id' => 'manual_' . $user->id
            ],
            'store' => $eventData['store'] ?? 'app_store',
            'environment' => $eventData['environment'] ?? 'PRODUCTION',
            'event_timestamp_ms' => now()->getTimestampMs(),
            'id' => 'manual_' . Str::uuid(),
            'app_id' => config('app.name', 'rydo'),
        ];

        // Add event-specific data
        switch ($eventType) {
            case 'INITIAL_PURCHASE':
            case 'RENEWAL':
            case 'PRODUCT_CHANGE':
                $baseEvent = array_merge($baseEvent, [
                    'product_id' => $eventData['product_id'] ?? 'rydo_premium_monthly',
                    'entitlement_id' => $eventData['entitlement_id'] ?? 'premium',
                    'purchased_at_ms' => now()->getTimestampMs(),
                    'expiration_at_ms' => $this->calculateExpirationTime($eventData['duration_days'] ?? 30),
                    'is_trial_period' => $eventData['is_trial_period'] ?? false,
                    'transaction_id' => 'manual_' . Str::random(16),
                    'original_transaction_id' => 'manual_orig_' . Str::random(16),
                    'price' => $eventData['price'] ?? $this->getDefaultPriceForProduct($eventData['product_id'] ?? 'rydo_premium_monthly'),
                    'currency' => $eventData['currency'] ?? 'USD',
                    'country_code' => $eventData['country_code'] ?? 'US',
                    'period_type' => $eventData['period_type'] ?? 'NORMAL',
                ]);
                break;

            case 'CANCELLATION':
            case 'EXPIRATION':
                $baseEvent = array_merge($baseEvent, [
                    'product_id' => $eventData['product_id'] ?? 'rydo_premium_monthly',
                    'entitlement_id' => $eventData['entitlement_id'] ?? 'premium',
                    'expiration_at_ms' => now()->getTimestampMs(),
                ]);
                break;

            case 'BILLING_ISSUE':
                $baseEvent = array_merge($baseEvent, [
                    'product_id' => $eventData['product_id'] ?? 'rydo_premium_monthly',
                    'entitlement_id' => $eventData['entitlement_id'] ?? 'premium',
                    'grace_period_expiration_at_ms' => now()->addDays(3)->getTimestampMs(),
                ]);
                break;
        }

        return [
            'api_version' => '1.0',
            'event' => $baseEvent
        ];
    }

    /**
     * Calculate expiration time based on duration in days
     *
     * @param int $durationDays
     * @return int
     */
    private function calculateExpirationTime(int $durationDays): int
    {
        return now()->addDays($durationDays)->getTimestampMs();
    }

    /**
     * Get user-friendly message for the event
     *
     * @param string $eventType
     * @param bool $success
     * @return string
     */
    private function getEventMessage(string $eventType, bool $success): string
    {
        if (!$success) {
            return "Failed to process {$eventType} event";
        }

        return match ($eventType) {
            'INITIAL_PURCHASE' => 'Successfully created new subscription',
            'RENEWAL' => 'Successfully renewed subscription',
            'CANCELLATION' => 'Successfully cancelled subscription',
            'EXPIRATION' => 'Successfully expired subscription',
            'BILLING_ISSUE' => 'Successfully logged billing issue',
            'PRODUCT_CHANGE' => 'Successfully changed subscription product',
            default => "Successfully processed {$eventType} event"
        };
    }

    /**
     * Get available event types for manual triggering
     *
     * @return array
     */
    public static function getAvailableEventTypes(): array
    {
        return [
            'INITIAL_PURCHASE' => 'Initial Purchase - Create new subscription',
            'RENEWAL' => 'Renewal - Renew existing subscription',
            'PRODUCT_CHANGE' => 'Product Change - Change subscription plan',
            'CANCELLATION' => 'Cancellation - Cancel subscription',
            'EXPIRATION' => 'Expiration - Expire subscription',
            'BILLING_ISSUE' => 'Billing Issue - Log billing problem',
        ];
    }

    /**
     * Get available product IDs
     *
     * @return array
     */
    public static function getAvailableProductIds(): array
    {
        return [
            'rydo_premium_monthly' => 'Rydo Premium Monthly',
            'rydo_premium_3_months' => 'Rydo Premium 3 Months',
            'rydo_premium_yearly' => 'Rydo Premium Yearly',
        ];
    }

    /**
     * Get available stores
     *
     * @return array
     */
    public static function getAvailableStores(): array
    {
        return [
            'app_store' => 'Apple App Store',
            'play_store' => 'Google Play Store',
            'stripe' => 'Stripe',
            'manual' => 'Manual/Dashboard',
        ];
    }

    /**
     * Get default price for a product ID
     *
     * @param string $productId
     * @return float
     */
    private function getDefaultPriceForProduct(string $productId): float
    {
        return match ($productId) {
            'rydo_premium_monthly' => 9.99,
            'rydo_premium_3_months' => 24.99,
            'rydo_premium_yearly' => 89.99,
            default => 9.99,
        };
    }
} 