import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zod/core/localizations/app_localizations_extension.dart';
import 'package:zod/core/resources/app_colors.dart';

class OnboardingErrorView extends ConsumerWidget {
  final Object error;
  final VoidCallback onSkip;

  const OnboardingErrorView({
    super.key,
    required this.error,
    required this.onSkip,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      backgroundColor: AppColors.white,
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: AppColors.lightSlateGray,
              ),
              const SizedBox(height: 16),
              Text(
                context.localizations.something_went_wrong,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textColor,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                context.localizations.failed_to_load_onboarding,
                style: TextStyle(
                  fontSize: 14,
                  color: AppColors.lightSlateGray,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: onSkip,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryColor,
                  foregroundColor: AppColors.white,
                ),
                child: Text(context.localizations.continue_to_app),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
