import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/core/app_theme.dart';
import 'package:zod/features/home/<USER>/models/review.dart';
import 'package:zod/features/home/<USER>/providers/winner_reviews_auctions_provider.dart';

import 'home_winner_review_auction_shimmer.dart';
import 'winner_review_auctions_item_view.dart';

class HomeWinnerReviewAuctionsView extends ConsumerWidget {
  const HomeWinnerReviewAuctionsView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final winnerReviewsState = ref.watch(winnerReviewsAuctionsProvider);
    return winnerReviewsState.when(
        data: (winnerReviewsAuctions) {
          final reviews = winnerReviewsAuctions.data.data;
          return reviews.isEmpty
              ? SizedBox()
              : Padding(
                  padding: const EdgeInsetsDirectional.only(start: 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(height: 32),
                      Text(
                        context.localizations.people_are_winning,
                        style: context.textTheme.titleSmall?.w700,
                      ),
                      SizedBox(height: 8),
                      SizedBox(
                        height: 180,
                        child: ListView.separated(
                          itemCount: reviews.length,
                          scrollDirection: Axis.horizontal,
                          physics: BouncingScrollPhysics(),
                          separatorBuilder: (_, __) => SizedBox(width: 8),
                          itemBuilder: (context, index) {
                            final review = reviews[index];
                            return WinnerReviewAuctionsItemView(review: review);
                          },
                        ),
                      ),
                    ],
                  ),
                );
        },
        error: (e, s) => Center(child: Text(e.toString())),
        loading: () => HomeWinnerReviewAuctionShimmer());
  }
}
