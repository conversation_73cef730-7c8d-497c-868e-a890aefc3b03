import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';

import 'package:zod/core/widgets/network_image_widget.dart';

import '../../data/models/auction.dart';

class BodyAuctionItemView extends StatelessWidget {
  const BodyAuctionItemView({super.key, required this.auction});

  final Auction auction;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      child: Row(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(13),
            child: NetworkImageView(
              auction.image ?? '',
              width: 80.0,
              height: 80.0,
              radius: 8.0,
            ),
          ),
          SizedBox(width: 8.0),
          Expanded(
            child: Text(
              auction.name,
              maxLines: 2,
              style: context.textTheme.titleSmall
                  ?.copyWith(overflow: TextOverflow.ellipsis),
            ),
          ),
        ],
      ),
    );
  }
}
