import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import '../../../../core/resources/app_colors.dart';

class OnboardingShimmer extends StatelessWidget {
  const OnboardingShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // Large background image shimmer
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            height: MediaQuery.of(context).size.height * 0.7,
            child: Shimmer.fromColors(
              baseColor: AppColors.platinum.withOpacity(0.4),
              highlightColor: AppColors.platinum.withOpacity(0.2),
              child: Container(
                width: double.infinity,
                height: double.infinity,
                decoration: BoxDecoration(
                  color: AppColors.white,
                ),
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        width: 100,
                        height: 100,
                        decoration: BoxDecoration(
                          color: AppColors.white,
                          shape: BoxShape.circle,
                        ),
                      ),
                      const SizedBox(height: 30),
                      Container(
                        width: 180,
                        height: 24,
                        decoration: BoxDecoration(
                          color: AppColors.white,
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      const SizedBox(height: 15),
                      Container(
                        width: 250,
                        height: 18,
                        decoration: BoxDecoration(
                          color: AppColors.white,
                          borderRadius: BorderRadius.circular(9),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),

          // Top controls shimmer
          Positioned(
            top: MediaQuery.of(context).viewPadding.top + 16,
            left: 17,
            right: 17,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Skip button shimmer
                Shimmer.fromColors(
                  baseColor: AppColors.platinum.withOpacity(0.4),
                  highlightColor: AppColors.platinum.withOpacity(0.2),
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    decoration: BoxDecoration(
                      color: AppColors.white,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          width: 16,
                          height: 16,
                          decoration: BoxDecoration(
                            color: AppColors.white,
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        SizedBox(width: 8),
                        Container(
                          width: 40,
                          height: 16,
                          decoration: BoxDecoration(
                            color: AppColors.white,
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                // Language selector shimmer (hidden)
                Visibility(
                  visible: false,
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 5, vertical: 4),
                    decoration: BoxDecoration(
                      color: AppColors.white,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(width: 60, height: 16),
                        SizedBox(width: 8),
                        Container(width: 16, height: 16),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Bottom content shimmer
          Positioned(
            left: 0,
            right: 0,
            bottom: 0,
            height: MediaQuery.of(context).size.height * 0.3,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
              ),
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Column(
                  children: [
                    // Page indicators shimmer
                    Shimmer.fromColors(
                      baseColor: AppColors.platinum.withOpacity(0.4),
                      highlightColor: AppColors.platinum.withOpacity(0.2),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: List.generate(
                          4,
                          (index) => Container(
                            margin: EdgeInsets.symmetric(horizontal: 4),
                            width: index == 0 ? 13 : 8.5,
                            height: index == 0 ? 13 : 8.5,
                            decoration: BoxDecoration(
                              color: AppColors.white,
                              borderRadius: BorderRadius.circular(6),
                            ),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(height: 12),

                    // Title shimmer
                    Shimmer.fromColors(
                      baseColor: AppColors.platinum.withOpacity(0.4),
                      highlightColor: AppColors.platinum.withOpacity(0.2),
                      child: Container(
                        width: 200,
                        height: 28,
                        decoration: BoxDecoration(
                          color: AppColors.white,
                          borderRadius: BorderRadius.circular(14),
                        ),
                      ),
                    ),

                    SizedBox(height: 12),

                    // Description shimmer
                    Shimmer.fromColors(
                      baseColor: AppColors.platinum.withOpacity(0.4),
                      highlightColor: AppColors.platinum.withOpacity(0.2),
                      child: Column(
                        children: [
                          Container(
                            width: double.infinity,
                            height: 16,
                            margin: EdgeInsets.symmetric(horizontal: 20),
                            decoration: BoxDecoration(
                              color: AppColors.white,
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          SizedBox(height: 8),
                          Container(
                            width: 250,
                            height: 16,
                            decoration: BoxDecoration(
                              color: AppColors.white,
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        ],
                      ),
                    ),

                    Spacer(),

                    // Button shimmer
                    Padding(
                      padding: const EdgeInsets.only(bottom: 24),
                      child: Shimmer.fromColors(
                        baseColor: AppColors.platinum.withOpacity(0.4),
                        highlightColor: AppColors.platinum.withOpacity(0.2),
                        child: Container(
                          width: double.infinity,
                          height: 56,
                          decoration: BoxDecoration(
                            color: AppColors.white,
                            borderRadius: BorderRadius.circular(16),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
