<x-filament-panels::page>
    <div class="space-y-6">
        {{-- Info Card --}}
        <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <x-heroicon-o-information-circle class="h-5 w-5 text-blue-400" />
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-blue-800 dark:text-blue-200">
                        Manual Subscription Management
                    </h3>
                    <div class="mt-2 text-sm text-blue-700 dark:text-blue-300">
                        <p>This tool allows you to manually trigger subscription events for selected users. The events will be processed through the same webhook system used by RevenueCat, ensuring consistency with your subscription logic.</p>
                        <p class="mt-2"><strong>Available Actions:</strong></p>
                        <ul class="list-disc list-inside mt-1 space-y-1">
                            <li><strong>Initial Purchase:</strong> Create a new subscription for the user</li>
                            <li><strong>Renewal:</strong> Renew an existing subscription</li>
                            <li><strong>Product Change:</strong> Change the user's subscription plan</li>
                            <li><strong>Cancellation:</strong> Cancel the user's subscription</li>
                            <li><strong>Expiration:</strong> Expire the user's subscription</li>
                            <li><strong>Billing Issue:</strong> Log a billing problem</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        {{-- Warning Card --}}
        <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <x-heroicon-o-exclamation-triangle class="h-5 w-5 text-yellow-400" />
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                        Important Notes
                    </h3>
                    <div class="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
                        <ul class="list-disc list-inside space-y-1">
                            <li>All events triggered here will be logged in the webhook events table</li>
                            <li>Events are processed through the same logic as real RevenueCat webhooks</li>
                            <li>Use this tool for testing, customer support, or handling edge cases</li>
                            <li>Be careful when triggering events for live users - this will affect their subscription status</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        {{-- Main Table --}}
        {{ $this->table }}
    </div>
</x-filament-panels::page> 