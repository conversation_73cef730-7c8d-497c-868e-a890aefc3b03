# PaymentMethodSelectionView

The `PaymentMethodSelectionView` class is responsible for displaying the payment method selection UI as a bottom sheet.

## Location

`lib/features/payment/presentation/views/payment_method_selection_view.dart`

## Dependencies

- `flutter_riverpod`: For state management
- `flutter_svg`: For SVG icons
- `PaymentProvider`: For payment processing
- `WalletChargeDto`: Data model for payment requests

## Class Structure

```dart
class PaymentMethodSelectionView extends ConsumerWidget {
  final WalletChargeDto chargeDto;
  
  const PaymentMethodSelectionView({
    Key? key,
    required this.chargeDto,
  }) : super(key: key);
  
  static const String route = '/payment_method_selection';
  
  @override
  Widget build(BuildContext context, WidgetRef ref);
  
  Widget _buildPaymentButton(
    BuildContext context, {
    required Widget child,
    required VoidCallback onTap,
  });
}
```

## Implementation

### build

Builds the payment method selection UI:

```dart
@override
Widget build(BuildContext context, WidgetRef ref) {
  final paymentState = ref.watch(paymentProvider);
  
  // Calculate VAT and total amount
  final vatAmount = chargeDto.amountPrice * 0.15;
  final totalAmount = chargeDto.amountPrice + vatAmount;
  
  return Scaffold(
    backgroundColor: Colors.transparent,
    body: Container(
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.4),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(24),
                topRight: Radius.circular(24),
              ),
            ),
            child: paymentState.isLoading
              ? Center(child: CircularProgressIndicator())
              : Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Drag handle
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Center(
                        child: Container(
                          width: 40,
                          height: 4,
                          decoration: BoxDecoration(
                            color: AppColors.darkGray,
                            borderRadius: BorderRadius.circular(999),
                          ),
                        ),
                      ),
                    ),
                    
                    // Title
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0),
                      child: Align(
                      alignment: AlignmentDirectional.centerStart,
                        child: Text(
                          context.localizations.select_payment_method,
                          style: context.textTheme.titleLarge?.w700,
                        ),
                      ),
                    ),
                    
                    SizedBox(height: 16),
                    
                    // Apple Pay option (iOS only)
                    if (Theme.of(context).platform == TargetPlatform.iOS)
                      _buildPaymentButton(
                        context,
                        child: SvgPicture.asset(AppIcons.applePay, height: 24),
                        onTap: () => ref.read(paymentProvider.notifier).processApplePayPayment(
                          context: context,
                          chargeDto: chargeDto.copyWith(
                            vatAmount: vatAmount,
                            totalAmount: totalAmount,
                          ),
                        ),
                      ),
                    
                    SizedBox(height: 16),
                    
                    // Credit/Debit Card option
                    _buildPaymentButton(
                      context,
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          SvgPicture.asset(AppIcons.creditCard, height: 24),
                          SizedBox(width: 12),
                          Text(
                            context.localizations.credit_debit_card,
                            style: context.textTheme.bodyLarge?.w500,
                          ),
                        ],
                      ),
                      onTap: () => ref.read(paymentProvider.notifier).processCardPayment(
                        context: context,
                        chargeDto: chargeDto.copyWith(
                          vatAmount: vatAmount,
                          totalAmount: totalAmount,
                        ),
                      ),
                    ),
                    
                    SizedBox(height: 24),
                  ],
                ),
          ),
        ],
      ),
    ),
  );
}
```

### _buildPaymentButton

Builds a payment method button:

```dart
Widget _buildPaymentButton(
  BuildContext context, {
  required Widget child,
  required VoidCallback onTap,
}) {
  return Padding(
    padding: const EdgeInsets.symmetric(horizontal: 16.0),
    child: InkWell(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        height: 46,
        decoration: BoxDecoration(
          color: AppColors.lightGray.withOpacity(0.3),
          border: Border.all(color: AppColors.lightGray),
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 2,
              offset: Offset(0, 1),
            ),
          ],
        ),
        child: Center(child: child),
      ),
    ),
  );
}
```

## Usage Example

```dart
// In ChargeWalletProvider
void chargeWallet(ref) async {
  // Create wallet charge DTO
  WalletChargeDto walletChargeDto =
      WalletChargeDto(state.price, state.points);

  // Show payment method selection as bottom sheet
  showModalBottomSheet(
    context: appKey.currentContext!,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder: (context) =>
        PaymentMethodSelectionView(chargeDto: walletChargeDto),
  );
}
```

## UI Design

The payment method selection UI is implemented as a bottom sheet with:
- A drag handle at the top
- A title "Select Payment Method"
- Payment method buttons:
  - Apple Pay (iOS only)
  - Credit/Debit Card with icon

The UI follows the design from the [Figma mockups](https://www.figma.com/design/WjHk1Dh7JMAuwuWwzFYEUH/Zod-mobile?node-id=2834-41652&t=DFnYN91quOqVYuRz-4).

## Screenshots

![Payment Method Selection](../images/payment_method_selection.png)
