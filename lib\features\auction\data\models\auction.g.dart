// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'auction.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Auction _$AuctionFromJson(Map<String, dynamic> json) => Auction(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      status: $enumDecode(_$AuctionStatusEnumMap, json['status']),
      openBidPoints: (json['open_bid_points'] as num?)?.toInt(),
      maxNumberOfParticipant:
          (json['max_number_of_participant'] as num?)?.toInt(),
      minNumberOfParticipant:
          (json['min_number_of_participant'] as num?)?.toInt(),
      remainingParticipation:
          (json['remaining_participation'] as num?)?.toInt(),
      image: json['image'] as String?,
      startAt: json['start_at'] == null
          ? null
          : DateTime.parse(json['start_at'] as String),
      endedAt: json['ended_at'] == null
          ? null
          : DateTime.parse(json['ended_at'] as String),
      isJoined: json['is_joined'] as bool? ?? false,
      category: json['category'] == null
          ? null
          : Category.fromJson(json['category'] as Map<String, dynamic>),
      participators: (json['participators'] as List<dynamic>?)
          ?.map((e) => Participator.fromJson(e as Map<String, dynamic>))
          .toList(),
      participatorIds: (json['participator_ids'] as List<dynamic>?)
              ?.map((e) => (e as num).toInt())
              .toList() ??
          [],
      bids: (json['bids'] as List<dynamic>?)
          ?.map((e) => BidHistory.fromJson(e as Map<String, dynamic>))
          .toList(),
      totalBidPoints: (json['total_bid_points'] as num?)?.toInt(),
      isWinner: json['is_winner'] as bool? ?? false,
      entryFee: (json['entry_fee'] as num?)?.toInt(),
      discountedEntryFee: (json['discounted_entry_fee'] as num?)?.toInt(),
      description: json['description'] as String?,
      claimProcess: json['claim_process'] as String?,
      directPurchasePoints: (json['direct_purchase_points'] as num?)?.toInt(),
      duration: (json['duration'] as num?)?.toInt(),
      enableFee: json['enable_fee'] as bool? ?? false,
      equivalentPoints: (json['equivalent_points'] as num?)?.toInt(),
      gallery:
          (json['gallery'] as List<dynamic>?)?.map((e) => e as String).toList(),
      incrementBidPoints: (json['increment_bid_points'] as num?)?.toInt(),
      maxBidPoints: (json['max_bid_points'] as num?)?.toInt(),
      isDirectPurchased: json['is_direct_purchased'] as bool? ?? false,
      productCost: (json['product_cost'] as num?)?.toInt(),
      rules: json['rules'] as String?,
      shortDescription: json['short_description'] as String?,
      startAfterHours: json['start_after_hours'] as String?,
      startAtTime: json['start_at_time'] as String?,
      isFavorite: json['is_favorite'] as bool? ?? false,
      suggestionPoints: (json['suggestion_points'] as List<dynamic>?)
              ?.map((e) => (e as num).toDouble())
              .toList() ??
          [],
      technicalSpecifications: json['technical_specifications'] as String?,
      suggestionBids: (json['suggestion_bids'] as List<dynamic>?)
              ?.map((e) => (e as num).toDouble())
              .toList() ??
          [],
      oldPrice: (json['old_price'] as num?)?.toInt(),
      winnerId: (json['winner_id'] as num?)?.toInt() ?? 0,
      isReminderEnabled: json['is_reminder_enabled'] as bool? ?? false,
      prizeExpired: json['prize_expired'] as bool? ?? false,
    );

Map<String, dynamic> _$AuctionToJson(Auction instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'image': instance.image,
      'status': _$AuctionStatusEnumMap[instance.status]!,
      'start_at': instance.startAt?.toIso8601String(),
      'ended_at': instance.endedAt?.toIso8601String(),
      'open_bid_points': instance.openBidPoints,
      'max_bid_points': instance.maxBidPoints,
      'entry_fee': instance.entryFee,
      'discounted_entry_fee': instance.discountedEntryFee,
      'product_cost': instance.productCost,
      'total_bid_points': instance.totalBidPoints,
      'equivalent_points': instance.equivalentPoints,
      'increment_bid_points': instance.incrementBidPoints,
      'remaining_participation': instance.remainingParticipation,
      'min_number_of_participant': instance.minNumberOfParticipant,
      'max_number_of_participant': instance.maxNumberOfParticipant,
      'direct_purchase_points': instance.directPurchasePoints,
      'start_after_hours': instance.startAfterHours,
      'start_at_time': instance.startAtTime,
      'is_joined': instance.isJoined,
      'prize_expired': instance.prizeExpired,
      'is_winner': instance.isWinner,
      'winner_id': instance.winnerId,
      'is_favorite': instance.isFavorite,
      'is_direct_purchased': instance.isDirectPurchased,
      'category': instance.category?.toJson(),
      'participators': instance.participators?.map((e) => e.toJson()).toList(),
      'participator_ids': instance.participatorIds,
      'bids': instance.bids?.map((e) => e.toJson()).toList(),
      'claim_process': instance.claimProcess,
      'technical_specifications': instance.technicalSpecifications,
      'short_description': instance.shortDescription,
      'description': instance.description,
      'rules': instance.rules,
      'duration': instance.duration,
      'old_price': instance.oldPrice,
      'enable_fee': instance.enableFee,
      'gallery': instance.gallery,
      'suggestion_points': instance.suggestionPoints,
      'suggestion_bids': instance.suggestionBids,
      'is_reminder_enabled': instance.isReminderEnabled,
    };

const _$AuctionStatusEnumMap = {
  AuctionStatus.live: 'live',
  AuctionStatus.closed: 'closed',
  AuctionStatus.upComing: 'upcoming',
  AuctionStatus.suspended: 'suspended',
};
