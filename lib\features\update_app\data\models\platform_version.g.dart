// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'platform_version.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PlatformVersion _$PlatformVersionFromJson(Map<String, dynamic> json) =>
    PlatformVersion(
      minVersion: (json['min_version'] as num).toInt(),
      maxVersion: (json['max_version'] as num).toInt(),
      messageAr: json['message_ar'] as String,
      messageEn: json['message_en'] as String,
    );

Map<String, dynamic> _$PlatformVersionToJson(PlatformVersion instance) =>
    <String, dynamic>{
      'min_version': instance.minVersion,
      'max_version': instance.maxVersion,
      'message_ar': instance.messageAr,
      'message_en': instance.messageEn,
    };
