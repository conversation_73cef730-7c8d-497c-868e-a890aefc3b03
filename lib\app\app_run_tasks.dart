import 'dart:convert';
import 'dart:developer';

import 'package:bond_core/bond_core.dart';
import 'package:bond_notifications/bond_notifications.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_uxcam/flutter_uxcam.dart';
import 'package:get_it/get_it.dart';
import 'package:timezone/data/latest.dart' as tz;
import 'package:timezone/timezone.dart' as tz;
import 'package:universal_platform/universal_platform.dart';
import 'package:zod/config/configs.dart';
import 'package:zod/features/auction/presentation/providers/open_auctions_provider.dart';
import 'package:zod/features/auction/presentation/providers/switch_joined_open_auction_provider.dart';
import 'package:zod/features/home/<USER>/providers/coming_auctions_provider.dart';
import 'package:zod/features/splash_page.dart';

import '../core/analytics/events/events.dart';
import '../core/app_analytics.dart';
import '../core/notifications/fcm_token_provider.dart';
import '../core/notifications/local_notification_service.dart';
import '../core/services/websocket_service.dart';
import '../features/auth/auth.dart';

class RunAppTasks extends RunTasks {
  @override
  Future<void> beforeRun(WidgetsBinding widgetsBinding) async {
    FlutterNativeSplash.preserve(widgetsBinding: widgetsBinding);

    // Initialize UXCam
    try {
      final uxcamAppKey = config('UXCAM_APP_KEY');
      if (uxcamAppKey != null && uxcamAppKey.isNotEmpty) {
        FlutterUxcam.optIntoSchematicRecordings();
        FlutterUxConfig uxConfig = FlutterUxConfig(userAppKey: uxcamAppKey);
        FlutterUxcam.startWithConfiguration(uxConfig);
        log('UXCam initialized with key: ${uxcamAppKey.substring(0, 8)}...',
            name: 'RunAppTasks');
      } else {
        log('UXCam app key not configured or is placeholder',
            name: 'RunAppTasks');
      }
    } catch (e) {
      log('Error initializing UXCam: $e', name: 'RunAppTasks');
    }

    // Initialize timezone data and set default timezone to Asia/Riyadh
    tz.initializeTimeZones();
    tz.setLocalLocation(tz.getLocation('Asia/Riyadh'));
    log('Timezone set to Asia/Riyadh', name: 'RunAppTasks');

    if (Auth.check()) {
      log("beforeRun");
      try {
        await Auth.me();
      } catch (_) {}
    }
    FlutterError.onError = FirebaseCrashlytics.instance.recordFlutterFatalError;

    // Track session started event
    AppAnalytics.fire(SessionStartedEvent());
    log('Session started event fired', name: 'RunAppTasks');

    FlutterNativeSplash.remove();
  }

  @override
  Future<void> afterRun() async {
    // Initialize local notification service

    final localNotificationService = GetIt.I<LocalNotificationService>();
    await localNotificationService.createAndroidNotificationChannel();
    await localNotificationService.initialize();
    // Initialize WebSocket service
    try {
      final webSocketService = GetIt.I<WebSocketService>();
      await webSocketService.initialize();
      log('WebSocket service initialized', name: 'RunAppTasks');
    } catch (e) {
      log('Error initializing WebSocket service: $e', name: 'RunAppTasks');
    }

    try {
      final permission = await FirebaseMessaging.instance.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );

      try {
        if (permission.authorizationStatus == AuthorizationStatus.authorized) {
          AppAnalytics.fire(NotificationOnEvent());
        } else {
          AppAnalytics.fire(NotificationOffEvent());
        }
      } catch (e) {
        log('Error tracking  status: $e', name: 'RunAppTasks');
      }
    } catch (_) {
      log('Error requesting notification permission', name: 'RunAppTasks');
    }

    if (UniversalPlatform.isIOS) {
      await FirebaseMessaging.instance
          .setForegroundNotificationPresentationOptions(
        alert: true,
        badge: false,
        sound: true,
      );
    }
    sl<PushNotificationsProviders>().listen();
    sl<PushNotificationProvider>(instanceName: 'firebase_messaging')
        .onNotification
        .asBroadcastStream()
        .listen((e) async {
      log('onNotification: REC $e');

      // Show local notification when app is in foreground

      // Get the local notification service instance

      // Use the addNotification method which will both add to the stream and show the notification
      if (!UniversalPlatform.isIOS) {
        localNotificationService.addNotification(e);
      }
      try {
        final ref = ProviderScope.containerOf(appContext);

        ref.invalidate(showSwitchProvider);
        ref.invalidate(openAuctionsProvider(''));
        ref.invalidate(upComingAuctionsProvider);
      } catch (e) {
        log('Error showing local notification: $e', name: 'RunAppTasks');
      }
    });
    // sl<NotificationCenterProvider>().load();
    // sl<NotificationCenterProvider>().listen();

    // Initialize FCM token service if user is logged in
    if (Auth.check()) {
      try {
        final container = ProviderContainer();
        final fcmTokenService = container.read(fcmTokenProvider);
        await fcmTokenService.initialize();
        log('FCM token service initialized', name: 'RunAppTasks');
      } catch (e) {
        log('Error initializing FCM token service: $e', name: 'RunAppTasks');
      }
    }

    // final init = await FirebaseMessaging.instance.getInitialMessage();
    // if (init != null) {
    //   final localNotificationService = GetIt.I<LocalNotificationService>();
    //   Future.delayed(Duration(seconds: 4));
    //   localNotificationService.handelClickNotificatios(init.data);
    // }
    // FirebaseMessaging.onMessageOpenedApp.asBroadcastStream().listen((event) {
    //   localNotificationService.handelClickNotificatios(event.data);
    // });

    if (UniversalPlatform.isIOS) {
      FirebaseMessaging.onMessageOpenedApp.asBroadcastStream().listen((event) {
        localNotificationService.handelClickNotificatios(event.data);
      });
    }
    try {
      SplashPage.splashPageStreamController.stream
          .asBroadcastStream()
          .listen((event) async {
        try {
          var notificationAppLaunch = await localNotificationService
              .notificationsPlugin
              .getNotificationAppLaunchDetails();
          if (notificationAppLaunch != null) {
            final localNotificationService =
                GetIt.I<LocalNotificationService>();
            Future.delayed(Duration(seconds: 1));
            if (notificationAppLaunch.notificationResponse?.payload != null) {
              localNotificationService.handelClickNotificatios(jsonDecode(
                  notificationAppLaunch.notificationResponse!.payload!));
            }
          }
        } catch (_) {}

        final init = await FirebaseMessaging.instance.getInitialMessage();
        if (init != null) {
          final localNotificationService = GetIt.I<LocalNotificationService>();
          Future.delayed(Duration(seconds: 1));
          localNotificationService.handelClickNotificatios(init.data);
        }
      });
    } catch (e) {
      log("error splashPageStreamController $e");
    }

    // log("PAYTABS_PROFILE_ID ::${config('PAYTABS_PROFILE_ID')}");
  }

  @override
  void onError(Object error, StackTrace stack) {
    log('Error: $error', stackTrace: stack);
    FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
  }
}
