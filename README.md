# Zod Mobile App

<p align="center">
  <img src="assets/images/app_logo.svg" width="200" alt="Zod Logo">
</p>

<p align="center">
  <strong>A competitive auction mobile application built with Flutter</strong>
</p>

<p align="center">
  <a href="#features">Features</a> •
  <a href="#architecture">Architecture</a> •
  <a href="#getting-started">Getting Started</a> •
  <a href="#user-flow">User Flow</a> •
  <a href="#api-documentation">API</a> •
  <a href="#contributing">Contributing</a>
</p>

---

## 📱 About Zod

Zod is a competitive auction mobile application that allows users to participate in real-time auctions, manage their digital wallet, and compete for various prizes. Built with Flutter and powered by the Bond framework, <PERSON><PERSON> provides a seamless and engaging auction experience for users in Saudi Arabia.

### 🎯 Key Highlights

- **Real-time Auctions**: Live bidding with WebSocket integration
- **Digital Wallet**: Secure payment processing with PayTabs integration
- **Multi-language Support**: Arabic and English localization
- **Cross-platform**: iOS and Android support
- **Clean Architecture**: Built with Bond framework for scalability
- **Firebase Integration**: Analytics, messaging, and crash reporting

## ✨ Features

### 🏠 Core Features

#### **Authentication & User Management**

- Phone number-based authentication with OTP verification
- User profile management with avatar selection
- Complete profile setup flow
- Secure logout functionality

#### **Auction System**

- Browse upcoming and live auctions
- Real-time bidding with live updates
- Auction details with countdown timers
- Join auction with participation fees
- Winner announcements and prize claiming
- Auction history and transaction records

#### **Digital Wallet**

- View wallet balance (total, available, and hold points)
- Charge wallet with multiple payment methods
- Transaction history with detailed records
- Points-based system for auction participation

#### **Payment Integration**

- PayTabs payment gateway integration
- Credit/Debit card payments (Visa, MasterCard)
- Apple Pay support (iOS)
- Secure payment processing with 3D Secure
- VAT calculation and invoice generation

#### **Notifications**

- Push notifications for auction updates
- Local notifications for reminders
- In-app notification center
- Real-time auction status updates

#### **Additional Features**

- Multi-language support (Arabic/English)
- Dark/Light theme support
- Google Maps integration for address selection
- PDF invoice viewer
- App update management
- Analytics and crash reporting

### 🛠 Technical Features

- **State Management**: Riverpod for reactive state management
- **Navigation**: GoRouter for declarative routing
- **Networking**: Bond Network with Dio for API calls
- **Caching**: Bond Cache for offline support
- **Real-time**: WebSocket integration for live updates
- **Localization**: Flutter Intl for multi-language support
- **Testing**: Unit and widget tests included

## 🏗 Architecture

Zod follows Clean Architecture principles with the Bond framework:

### **Project Structure**

```
lib/
├── app/                    # App configuration and routing
├── core/                   # Core utilities and shared components
├── features/               # Feature modules
│   ├── auth/              # Authentication
│   ├── auction/           # Auction management
│   ├── wallet/            # Digital wallet
│   ├── payment/           # Payment processing
│   ├── home/              # Home dashboard
│   ├── more/              # Settings and profile
│   └── notification/      # Notification center
├── providers/             # Service providers
└── config/               # Configuration files
```

### **Feature Architecture**

Each feature follows a consistent structure:

```
feature/
├── data/
│   ├── api.dart          # API client
│   ├── models/           # Data models
│   └── enums/            # Enumerations
├── presentation/
│   ├── providers/        # State management
│   ├── views/            # UI components
│   └── pages/            # Screen widgets
├── services/             # Business logic
└── routes.dart           # Feature routing
```

### **Key Technologies**

- **Flutter**: Cross-platform mobile framework
- **Bond Framework**: Custom architecture framework
- **Riverpod**: State management solution
- **GoRouter**: Declarative routing
- **Firebase**: Backend services
- **PayTabs**: Payment gateway
- **WebSocket**: Real-time communication

## 🚀 Getting Started

### Prerequisites

- Flutter SDK (>=3.3.0)
- Dart SDK
- Android Studio / Xcode
- Firebase account
- PayTabs merchant account

### Installation

1. **Clone the repository**

   ```bash
   git clone https://github.com/onestudio-co/zod-mobile.git
   cd zod-mobile
   ```

2. **Install dependencies**

   ```bash
   flutter pub get
   ```

3. **Configure environment**

   ```bash
   cp env.example.json env.json
   # Edit env.json with your configuration
   ```

4. **Setup Firebase**

   ```bash
   # Configure Firebase for both environments
   ./firebase_config.bash
   ```

5. **Generate code**

   ```bash
   flutter packages pub run build_runner build
   ```

6. **Run the app**

   ```bash
   # Development
   flutter run --flavor staging

   # Production
   flutter run --flavor production
   ```

### Environment Configuration

Create `env.json` file with the following structure:

```json
{
  "API_BASE_URL": "https://api.zod.com/api/",
  "CONNECT_TIMEOUT": "100000",
  "SEND_TIMEOUT": "1000000",
  "RECEIVE_TIMEOUT": "1000000",
  "RECEIVE_DATA_WHEN_STATUS_ERROR": "true",
  "PAYTABS_PROFILE_ID": "your_profile_id",
  "PAYTABS_SERVER_KEY": "your_server_key",
  "PAYTABS_CLIENT_KEY": "your_client_key",
  "PAYTABS_MERCHANT_NAME": "Zod",
  "PAYTABS_MERCHANT_COUNTRY_CODE": "SA",
  "PAYTABS_APPLE_PAY_IDENTIFIER": "merchant.com.zod",
  "PUSHER_APP_KEY": "your_pusher_key",
  "PUSHER_APP_CLUSTER": "your_cluster",
  "PUSHER_HOST": "your_host",
  "PUSHER_SCHEME": "wss",
  "PUSHER_PORT": "443"
}
```

## 👤 User Flow

### **Onboarding & Authentication**

1. **Splash Screen**: App initialization and configuration loading
2. **Login**: Phone number entry with country code selection
3. **OTP Verification**: SMS verification code input
4. **Profile Setup**: Complete user profile with avatar selection

### **Main Navigation**

The app features a bottom navigation with four main sections:

#### **🏠 Home Tab**

- Featured auctions carousel
- Upcoming auctions list
- Winner announcements
- How it works section
- Quick access to wallet and notifications

#### **🔨 Auctions Tab**

- Browse all available auctions
- Filter by status (upcoming, live, ended)
- Search functionality
- Auction categories

#### **💰 Wallet Tab** (Authenticated users only)

- Current balance display (total, available, hold)
- Charge wallet functionality
- Transaction history
- Payment methods management

#### **⚙️ More Tab**

- User profile management
- Transaction history
- Privacy policy
- App settings
- Logout option

### **Auction Participation Flow**

1. **Browse Auctions**: View available auctions with details
2. **Auction Details**: See auction information, rules, and current status
3. **Join Auction**: Pay participation fee to join
4. **Live Bidding**: Participate in real-time bidding
5. **Winner Announcement**: View results and claim prizes
6. **Prize Claiming**: Provide shipping address for physical prizes

### **Wallet & Payment Flow**

1. **View Balance**: Check current wallet status
2. **Charge Wallet**: Select points amount to purchase
3. **Payment Method**: Choose between credit card or Apple Pay
4. **Payment Processing**: Secure payment via PayTabs
5. **Confirmation**: Receive payment confirmation and updated balance
6. **Invoice**: View and download payment invoice

## 📱 Screenshots

| Home Screen                   | Auctions                              | Wallet                            | Profile                             |
| ----------------------------- | ------------------------------------- | --------------------------------- | ----------------------------------- |
| ![Home](docs/images/home.png) | ![Auctions](docs/images/auctions.png) | ![Wallet](docs/images/wallet.png) | ![Profile](docs/images/profile.png) |

## 🎮 App Features in Detail

### **Real-time Auction System**

- **Live Bidding**: WebSocket-powered real-time bidding experience
- **Countdown Timers**: Precise auction end time tracking
- **Bid Increments**: Configurable minimum bid increments
- **Auto-refresh**: Automatic auction status updates
- **Winner Notifications**: Instant notifications for auction results

### **Smart Wallet Management**

- **Points System**: Virtual currency for auction participation
- **Balance Types**:
  - Total Points: All points owned by user
  - Available Points: Points available for bidding
  - Hold Points: Points locked in active auctions
- **Transaction History**: Detailed record of all wallet activities
- **Auto-refund**: Automatic refund for unsuccessful bids

### **Advanced Payment Features**

- **Multiple Payment Methods**: Credit cards, Apple Pay
- **Saved Cards**: Secure card tokenization for repeat payments
- **VAT Calculation**: Automatic 15% VAT calculation for Saudi Arabia
- **Invoice Generation**: PDF invoices for all transactions
- **Payment History**: Complete payment transaction records

### **Notification System**

- **Push Notifications**: Firebase Cloud Messaging integration
- **Local Notifications**: Auction reminders and alerts
- **Notification Center**: In-app notification management
- **Real-time Updates**: Live auction status notifications
- **Customizable Alerts**: User-configurable notification preferences

### **User Experience Features**

- **Onboarding**: Smooth user registration and profile setup
- **Avatar System**: Customizable user avatars
- **Address Management**: Multiple shipping addresses for prizes
- **Language Switching**: Seamless Arabic/English switching
- **Theme Support**: Light and dark theme options
- **Offline Support**: Cached data for offline viewing

## 🔧 Development

### **Build Variants**

- **Staging**: Development environment with test data
- **Production**: Live environment with real data

### **Code Generation**

```bash
# Generate models and serialization
flutter packages pub run build_runner build

# Watch for changes
flutter packages pub run build_runner watch
```

### **Localization**

```bash
# Generate localization files
flutter gen-l10n
```

### **Testing**

```bash
# Run unit tests
flutter test

# Run integration tests
flutter test integration_test/
```

## 📚 API Documentation

The app integrates with a RESTful API for all backend operations:

### **Base Configuration**

- **Base URL**: Configurable via environment
- **Authentication**: Bearer token
- **Content Type**: application/json
- **Timeout**: Configurable connection, send, and receive timeouts

### **Main Endpoints**

#### **Authentication**

- `POST /login` - User login with phone number
- `POST /send-otp` - Send OTP verification
- `POST /logout` - User logout
- `GET /me` - Get current user profile

#### **Auctions**

- `GET /auctions` - List all auctions
- `GET /auctions/{id}` - Get auction details
- `POST /auctions/{id}/join` - Join auction
- `POST /auctions/{id}/bid` - Place bid

#### **Wallet**

- `GET /wallet` - Get wallet balance
- `GET /wallet-transactions` - Get transaction history
- `POST /payment/initialize` - Initialize payment
- `POST /payment/process` - Process payment

#### **Notifications**

- `GET /notifications` - Get user notifications
- `POST /notifications/{id}/read` - Mark as read
- `POST /notifications/read-all` - Mark all as read

For detailed API documentation, see [API Documentation](docs/api/README.md).

## 🔧 Technical Implementation

### **State Management with Riverpod**

The app uses Riverpod for state management with the following provider types:

- **StateNotifierProvider**: For complex state management (auctions, wallet)
- **FutureProvider**: For async data fetching (API calls)
- **StreamProvider**: For real-time data (WebSocket connections)
- **Provider**: For simple dependencies (services, configurations)

Example provider implementation:

```dart
final walletProvider = StateNotifierProvider<WalletNotifier, WalletState>(
  (ref) => WalletNotifier(ref.read(walletApiProvider)),
);
```

### **WebSocket Integration**

Real-time features are powered by WebSocket connections:

- **Pusher Channels**: For real-time auction updates
- **Connection Management**: Automatic reconnection and error handling
- **Event Handling**: Structured event processing for different auction states
- **State Synchronization**: Real-time UI updates based on server events

### **Caching Strategy**

The app implements a multi-layer caching strategy:

- **Network Cache**: HTTP response caching with Bond Cache
- **Memory Cache**: In-memory caching for frequently accessed data
- **Persistent Cache**: Local storage for offline functionality
- **Cache Invalidation**: Smart cache invalidation based on data freshness

### **Error Handling**

Comprehensive error handling throughout the app:

- **Network Errors**: Retry mechanisms and offline fallbacks
- **Validation Errors**: Form validation with user-friendly messages
- **Payment Errors**: Detailed payment failure handling
- **Crash Reporting**: Firebase Crashlytics integration

## 🛠 Development Tools & Scripts

### **Code Generation**

```bash
# Generate all code (models, routes, localization)
flutter packages pub run build_runner build --delete-conflicting-outputs

# Watch for changes during development
flutter packages pub run build_runner watch
```

### **Localization Management**

```bash
# Generate localization files
flutter gen-l10n

# Extract translatable strings
flutter pub run intl_utils:generate
```

### **Asset Management**

```bash
# Generate app icons
flutter pub run flutter_launcher_icons:main -f flutter_launcher_icons-production.yaml
flutter pub run flutter_launcher_icons:main -f flutter_launcher_icons-staging.yaml

# Generate splash screens
flutter pub run flutter_native_splash:create --path=flutter_native_splash-production.yaml
flutter pub run flutter_native_splash:create --path=flutter_native_splash-staging.yaml
```

### **Firebase Configuration**

```bash
# Configure Firebase for all platforms
./firebase_config.bash

# Configure specific environment
flutterfire configure --project=flutter-bond-8ae02
```

## 🐛 Troubleshooting

### **Common Issues**

#### **Build Issues**

```bash
# Clean build cache
flutter clean
flutter pub get
flutter packages pub run build_runner build --delete-conflicting-outputs

# Reset iOS pods
cd ios && rm -rf Pods Podfile.lock && pod install
```

#### **Firebase Issues**

- Ensure `google-services.json` (Android) and `GoogleService-Info.plist` (iOS) are in correct locations
- Verify Firebase project configuration matches app bundle IDs
- Check Firebase console for proper app registration

#### **PayTabs Integration Issues**

- Verify PayTabs credentials in environment configuration
- Ensure merchant account is properly configured
- Check network connectivity for payment processing
- Validate payment amount and currency settings

#### **WebSocket Connection Issues**

- Check Pusher configuration and credentials
- Verify network connectivity and firewall settings
- Monitor connection status in debug logs
- Implement proper reconnection logic

### **Debug Commands**

```bash
# Run with verbose logging
flutter run --verbose

# Run specific flavor
flutter run --flavor staging --debug

# Analyze code quality
flutter analyze

# Check for outdated dependencies
flutter pub outdated
```

## 📊 Performance Optimization

### **App Performance**

- **Lazy Loading**: Implement lazy loading for large lists
- **Image Optimization**: Use cached network images with proper sizing
- **Memory Management**: Proper disposal of controllers and streams
- **Build Optimization**: Use const constructors and widgets where possible

### **Network Performance**

- **Request Caching**: Cache API responses to reduce network calls
- **Pagination**: Implement pagination for large data sets
- **Compression**: Enable GZIP compression for API responses
- **Connection Pooling**: Reuse HTTP connections for better performance

### **Database Performance**

- **Efficient Queries**: Optimize database queries and indexing
- **Batch Operations**: Use batch operations for multiple database writes
- **Connection Management**: Proper database connection lifecycle management

## 🔒 Security Best Practices

### **Data Security**

- **Encryption**: Encrypt sensitive data at rest and in transit
- **Token Management**: Secure storage and automatic refresh of auth tokens
- **Input Validation**: Validate all user inputs on client and server side
- **API Security**: Implement proper authentication and authorization

### **Payment Security**

- **PCI Compliance**: Follow PCI-DSS standards for payment processing
- **Tokenization**: Use payment tokenization to avoid storing card details
- **3D Secure**: Implement 3D Secure authentication for card payments
- **Fraud Detection**: Monitor for suspicious payment activities

### **App Security**

- **Code Obfuscation**: Obfuscate release builds to protect source code
- **Certificate Pinning**: Implement SSL certificate pinning
- **Root Detection**: Detect and handle rooted/jailbroken devices
- **Debug Protection**: Disable debugging in production builds

## 🔐 Security

### **Data Protection**

- Secure token storage using Flutter Secure Storage
- API communication over HTTPS
- Payment processing via PCI-compliant PayTabs
- User data encryption at rest

### **Authentication**

- Phone number-based authentication
- OTP verification for secure login
- JWT token-based session management
- Automatic token refresh

### **Payment Security**

- PayTabs PCI-DSS compliance
- 3D Secure authentication
- Tokenized card storage
- Encrypted payment data transmission

## 🌍 Localization

Zod supports multiple languages:

- **Arabic (ar)**: Primary language for Saudi market
- **English (en)**: Secondary language

### **Adding New Languages**

1. Create new `.arb` file in `lib/l10n/`
2. Add translations for all keys
3. Update `l10n.yaml` configuration
4. Run `flutter gen-l10n`

## 🚀 Deployment

### **Android**

```bash
# Build APK
flutter build apk --flavor production

# Build App Bundle
flutter build appbundle --flavor production
```

### **iOS**

```bash
# Build iOS
flutter build ios --flavor production
```

### **Firebase App Distribution**

The project includes automated distribution via Firebase App Distribution for beta testing.

## 📈 Analytics & Monitoring

### **Firebase Analytics**

- **User Engagement**: Track user interactions and app usage patterns
- **Custom Events**: Monitor auction participation and payment activities
- **Conversion Tracking**: Measure user journey from registration to purchase
- **Performance Monitoring**: Track app performance and crash rates

### **Crash Reporting**

- **Firebase Crashlytics**: Automatic crash detection and reporting
- **Error Tracking**: Monitor and fix runtime errors
- **Performance Issues**: Identify and resolve performance bottlenecks
- **User Impact Analysis**: Understand crash impact on user experience

### **Custom Analytics Events**

```dart
// Track auction participation
AppAnalytics.fire(AuctionJoinedEvent(auctionId: auction.id));

// Track payment completion
AppAnalytics.fire(PaymentCompletedEvent(
  amount: payment.amount,
  method: payment.method,
));
```

## 🧪 Testing Strategy

### **Unit Tests**

- **Business Logic**: Test core business logic and calculations
- **Data Models**: Validate model serialization and deserialization
- **Utilities**: Test helper functions and utilities
- **Providers**: Test state management logic

### **Widget Tests**

- **UI Components**: Test individual widgets and their behavior
- **User Interactions**: Simulate user taps, swipes, and inputs
- **State Changes**: Verify UI updates based on state changes
- **Navigation**: Test routing and navigation flows

### **Integration Tests**

- **End-to-End Flows**: Test complete user journeys
- **API Integration**: Test API communication and error handling
- **Payment Flow**: Test payment processing (with mock data)
- **Real-time Features**: Test WebSocket connections and updates

### **Running Tests**

```bash
# Run all tests
flutter test

# Run specific test file
flutter test test/features/auth/auth_test.dart

# Run tests with coverage
flutter test --coverage

# Run integration tests
flutter test integration_test/
```

## 🚀 CI/CD Pipeline

### **GitHub Actions**

The project includes automated CI/CD workflows:

- **Build Verification**: Automatic build verification on pull requests
- **Testing**: Run unit and widget tests on every commit
- **Code Quality**: Static analysis and linting checks
- **Security Scanning**: Dependency vulnerability scanning

### **Deployment Automation**

- **Firebase App Distribution**: Automatic beta distribution
- **App Store Connect**: Automated iOS app submission
- **Google Play Console**: Automated Android app publishing
- **Release Notes**: Automatic generation from commit messages

### **Quality Gates**

- All tests must pass
- Code coverage threshold (80%+)
- No critical security vulnerabilities
- Successful build on all target platforms

## 🌟 Future Roadmap

### **Planned Features**

- **Social Features**: User profiles, following, and social interactions
- **Advanced Auctions**: Dutch auctions, sealed bid auctions
- **Gamification**: Achievement system, leaderboards, badges
- **AI Integration**: Personalized auction recommendations
- **Multi-currency**: Support for multiple currencies and payment methods

### **Technical Improvements**

- **Offline Mode**: Enhanced offline functionality
- **Performance**: Further optimization for low-end devices
- **Accessibility**: Improved accessibility features
- **Testing**: Increased test coverage and automated testing
- **Documentation**: Enhanced developer documentation

### **Platform Expansion**

- **Web Platform**: Flutter web version of the app
- **Desktop**: Windows, macOS, and Linux desktop applications
- **Smart TV**: Android TV and Apple TV applications
- **Wearables**: Apple Watch and Wear OS companion apps

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guidelines](CONTRIBUTING.md) for details.

### **Development Workflow**

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass (`flutter test`)
6. Commit your changes (`git commit -m 'Add amazing feature'`)
7. Push to the branch (`git push origin feature/amazing-feature`)
8. Submit a pull request

### **Code Style**

- Follow Flutter/Dart style guidelines
- Use meaningful variable and function names
- Add documentation for public APIs
- Maintain consistent formatting with `dart format`
- Follow the existing project structure and patterns

### **Pull Request Guidelines**

- Provide a clear description of the changes
- Include screenshots for UI changes
- Add tests for new functionality
- Update documentation if needed
- Ensure CI/CD pipeline passes

## 📋 Project Dependencies

### **Core Dependencies**

```yaml
# Framework
flutter: sdk
flutter_localizations: sdk

# Bond Framework
bond_core: ^0.0.3
bond_cache: ^0.0.4+1
bond_network: ^0.0.6
bond_form: ^0.0.4+1
bond_form_riverpod: ^0.0.2+7
bond_app_analytics: ^0.1.0
bond_notifications: ^0.1.0

# State Management
flutter_riverpod: ^2.6.1
get_it: ^8.0.3

# Navigation
go_router: ^14.8.0

# Networking
dio: ^5.8.0+1
pretty_dio_logger: ^1.4.0

# UI Components
flutter_svg: ^2.0.17
google_fonts: ^6.2.1
oktoast: ^3.4.0
skeletonizer: ^1.4.3
carousel_slider: ^5.0.0

# Maps & Location
google_maps_flutter: ^2.5.3
location: ^5.0.3
geocoding: ^2.1.1

# Firebase
firebase_core: ^3.11.0
firebase_analytics: ^11.4.2
firebase_messaging: ^15.2.2
firebase_crashlytics: ^4.3.4
firebase_remote_config: ^5.4.0

# Payment
flutter_paytabs_bridge: ^2.7.0

# Storage
shared_preferences: ^2.5.2
flutter_secure_storage: ^9.2.4

# Utilities
package_info_plus: ^8.2.1
device_info_plus: ^11.3.0
url_launcher: ^6.3.1
```

## 📞 Support & Contact

### **Getting Help**

- **Documentation**: Check this README and project documentation
- **Issues**: Create an issue on GitHub for bugs or feature requests
- **Discussions**: Use GitHub Discussions for questions and community support
- **Email**: Contact the development team for urgent matters

### **Reporting Issues**

When reporting issues, please include:

1. **Environment Details**: Flutter version, device, OS version
2. **Steps to Reproduce**: Clear steps to reproduce the issue
3. **Expected Behavior**: What you expected to happen
4. **Actual Behavior**: What actually happened
5. **Screenshots**: Include screenshots if applicable
6. **Logs**: Relevant error logs or console output

### **Feature Requests**

For feature requests, please provide:

1. **Use Case**: Describe the problem you're trying to solve
2. **Proposed Solution**: Your suggested approach
3. **Alternatives**: Other solutions you've considered
4. **Additional Context**: Any other relevant information

## 📊 Project Statistics

- **Lines of Code**: ~50,000+ lines
- **Features**: 8 major feature modules
- **Supported Languages**: Arabic, English
- **Supported Platforms**: iOS, Android
- **Test Coverage**: 80%+
- **Dependencies**: 50+ packages

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:

- Create an issue on GitHub
- Contact the development team
- Check the documentation

## 🙏 Acknowledgments

- **Bond Framework**: For providing the architectural foundation
- **Flutter Team**: For the amazing cross-platform framework
- **PayTabs**: For secure payment processing
- **Firebase**: For backend services
- **Community**: For contributions and feedback

---

<p align="center">
  Made with ❤️ by the Zod Team
</p>
