import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';
import 'package:open_store/open_store.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/core/app_theme.dart';

import 'package:zod/core/resources/app_assets.dart';
import 'package:zod/core/widgets/app_button.dart';

class UpdateAppPage extends StatelessWidget {
  final String message;

  const UpdateAppPage({super.key, required this.message});

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false, // Prevent back navigation
      child: Scaffold(
        backgroundColor: Colors.white,
        body: Column(
          children: [
            // Image that starts from status bar and takes full width
            Image.asset(
              AppImagesAssets.forceUpdate,
              width: double.infinity,
              fit: BoxFit.cover,
            ),
            const SizedBox(height: 40),
            // Content positioned below the image
            Expanded(
              child: SafeArea(
                top:
                    false, // Don't apply safe area to top since image handles it
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        context.localizations.update_app,
                        textAlign: TextAlign.center,
                        style: context.textTheme.headlineMedium,
                      ),
                      const SizedBox(
                        height: 12,
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 13.0),
                        child: Text(
                          message,
                          textAlign: TextAlign.center,
                          style: context.textTheme.labelLarge?.slateGray,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            // Button fixed at bottom
            SafeArea(
              top: false,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: AppButton(
                  title: context.localizations.update_app_now,
                  onPressed: _onUpdate,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _onUpdate() {
    //TODO::CHANGE appStoreId
    OpenStore.instance.open(
      appStoreId: '6744919634',
      androidAppBundleId: 'app.zod.com',
    );
  }
}
