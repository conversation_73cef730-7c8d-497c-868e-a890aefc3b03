import 'package:bond_network/bond_network.dart';
import 'package:zod/features/onboarding/data/models/onboarding.dart';

typedef OnboardingApiResult = ListResponse<Onboarding>;

class OnboardingApi {
  final BondFire _bondFire;

  OnboardingApi(this._bondFire);

  Future<OnboardingApiResult> getOnboardingScreens() => _bondFire
      .get<OnboardingApiResult>('/onboarding')
      .cache(cachePolicy: CachePolicy.networkElseCache)
      .header(Api.headers())
      .factory(OnboardingApiResult.fromJson)
      .errorFactory(ServerError.fromJson)
      .execute();
}
