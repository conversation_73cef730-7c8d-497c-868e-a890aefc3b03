import 'package:bond_network/bond_network.dart';
import 'package:json_annotation/json_annotation.dart';

part 'onboarding.g.dart';

@JsonSerializable()
class Onboarding extends Model {
  final String title;
  final String description;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'image_url')
  final String? imageUrl;
  final int order;
  final String version;

  const Onboarding({
    required super.id,
    required this.title,
    required this.description,
    this.imageUrl,
    required this.order,
    required this.version,
  });

  factory Onboarding.fromJson(Map<String, dynamic> json) =>
      _$OnboardingFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$OnboardingToJson(this);
}
