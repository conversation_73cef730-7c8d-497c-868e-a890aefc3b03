import 'dart:developer';

import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zod/app/routes.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/features/payment/presentation/views/payment_method_selection_view.dart';
import 'package:zod/features/wallet/api.dart';
import 'package:zod/features/wallet/data/models/charge_wallet_auction.dart';
import 'package:zod/features/wallet/data/models/wallet_charge_dto.dart';

import 'states/charge_wallet_state.dart';

final chargeWalletProvider = StateNotifierProvider.family<ChargeWalletProvider,
    ChargeWalletState, ChargeWalletAuction>(
  (ref, chargeWalletAuction) {
    return ChargeWalletProvider(
      walletApi: sl<WalletApi>(),
      chargeWalletAuction: chargeWalletAuction,
    );
  },
);

//AutoDisposeFamilyAsyncNotifier
class ChargeWalletProvider extends StateNotifier<ChargeWalletState> {
  final WalletApi walletApi;
  final ChargeWalletAuction chargeWalletAuction;
  TextEditingController pointsTextController = TextEditingController(text: "0");

  ChargeWalletProvider(
      {required this.walletApi, required this.chargeWalletAuction})
      : super(ChargeWalletState(
            points: chargeWalletAuction.auction?.suggestionPoints?[1].toInt() ??
                chargeWalletAuction.fee,
            minChargePoint: _getMinChargePoint(chargeWalletAuction),
            flashPoints:
                chargeWalletAuction.auction?.suggestionPoints?[1].toInt() ?? 0,
            increment: chargeWalletAuction.increment,
            fee: chargeWalletAuction.fee)) {
    pointsTextController.text =
        "${chargeWalletAuction.auction?.suggestionPoints?[1].toInt() ?? chargeWalletAuction.fee}";
  }

  static int _getMinChargePoint(ChargeWalletAuction chargeWalletAuction) {
    if (chargeWalletAuction.auction != null &&
        chargeWalletAuction.auction!.isNotJoined) {
      // For joining auction: use entry fee (or discounted entry fee) as minimum
      return chargeWalletAuction.auction!.effectiveEntryFee ??
          chargeWalletAuction.fee;
    } else if (chargeWalletAuction.auction?.isLive ?? false) {
      // For live auctions (bidding): use increment
      return chargeWalletAuction.increment;
    } else {
      // For general wallet charging: use standard minimum (50 points)
      return 50;
    }
  }

  /// Get the minimum value and error message based on context
  (int minValue, String errorMessage) _getMinimumValidation() {
    if (chargeWalletAuction.auction != null &&
        chargeWalletAuction.auction!.isNotJoined) {
      // For joining auction: use entry fee (or discounted entry fee) as minimum
      final minValue = chargeWalletAuction.auction!.effectiveEntryFee ??
          chargeWalletAuction.fee;
      final errorMessage = appKey.currentContext!.localizations
          .minimum_purchase_points_participations_fees(minValue);
      return (minValue, errorMessage);
    } else {
      // For general wallet charging: use standard minimum (50 points)
      final minValue = state.min;
      final errorMessage = appKey.currentContext!.localizations
          .minimum_purchase_points(minValue);
      return (minValue, errorMessage);
    }
  }

  void increasePoints() {
    state = state.copyWith(points: state.points + state.increment, error: "");
    pointsTextController.text = state.points.toString();
    if (state.points > state.max) {
      state = state.copyWith(
        error: appKey.currentContext!.localizations
            .maximum_purchase_points(state.max),
      );
    }
  }

  void setPoints(int point) {
    state = state.copyWith(points: point, error: "");

    // Get minimum validation based on context
    final (minValue, errorMessage) = _getMinimumValidation();

    if (point < minValue) {
      state = state.copyWith(error: errorMessage);
    }

    if (point > state.max) {
      state = state.copyWith(
        error: appKey.currentContext!.localizations
            .maximum_purchase_points(state.max),
      );
    }
    pointsTextController.text = state.points.toString();
  }

  void decreasePoints() {
    if ((state.points - state.increment) < 0) {
      return;
    }

    // Get minimum validation based on context
    final (minValue, errorMessage) = _getMinimumValidation();

    if ((state.points - state.increment) < minValue) {
      state = state.copyWith(
          points: state.points - state.increment, error: errorMessage);
    } else {
      state = state.copyWith(points: state.points - state.increment, error: "");
    }
    pointsTextController.text = state.points.toString();
  }

  void chargeWallet(ref) async {
    // Calculate tax for display purposes (15% VAT)
    final basePrice = state.price.toDouble();
    final vatAmount = basePrice * 0.15;
    final totalAmount = basePrice + vatAmount;

    // Determine if this is an entry fee payment
    final isEntryFeePayment = chargeWalletAuction.auction != null &&
        chargeWalletAuction.auction!.isNotJoined;

    // Create wallet charge DTO
    // - Use base price (backend will add tax)
    // - Include tax details for display in payment summary
    WalletChargeDto walletChargeDto = WalletChargeDto(
      state.price, // Base amount without tax
      state.points, // Points being purchased
      auction: chargeWalletAuction.auction,
      auctionId: chargeWalletAuction.auctionId,
      vatAmount: vatAmount, // For display in payment summary
      totalAmount: totalAmount, // For display in payment summary
    );

    log("ChargeWalletProvider chargeWallet - Payment type: ${isEntryFeePayment ? 'Entry Fee' : 'Point Purchase'}");
    log("ChargeWalletProvider chargeWallet - Base price: ${state.price}");
    log("ChargeWalletProvider chargeWallet - Points: ${state.points}");
    log("ChargeWalletProvider chargeWallet - VAT amount (for display): $vatAmount");
    log("ChargeWalletProvider chargeWallet - Total amount (for display): $totalAmount");
    log("ChargeWalletProvider chargeWallet - Backend will calculate actual payment amounts");
    log("ChargeWalletProvider chargeWallet walletChargeDto: ${walletChargeDto.toJson()}");

    goRouter.push(PaymentMethodSelectionView.route, extra: walletChargeDto);
  }

  // Legacy direct charge method (without payment gateway)
  // void _directChargeWallet(ref) async {
  //   WalletChargeDto walletChargeDto =
  //       WalletChargeDto(state.price, state.points);
  //   state = state.copyWith(isLoading: true);
  //   try {
  //     final chargeUser = await walletApi.walletCharge(walletChargeDto);

  //     if (chargeUser.meta.success ?? false) {
  //       goRouter.pop();
  //       if (chargeWalletAuction.auction?.isNotJoined ?? false) {
  //         goRouter.push(JoinToAuctionView.route,
  //             extra: chargeWalletAuction.auction);
  //       }

  //       NotificationAlert.showLocalNotification(
  //         chargeUser.meta.message,
  //         type: ToastType.success,
  //         align: Alignment.topCenter,
  //       );

  //       ref.invalidate(meProvider);
  //     } else {
  //       NotificationAlert.showLocalNotification(
  //         chargeUser.meta.message,
  //         type: ToastType.error,
  //       );
  //     }
  //   } catch (e) {
  //     NotificationAlert.showLocalNotification(e.toString(),
  //         type: ToastType.error);
  //   } finally {
  //     state = state.copyWith(isLoading: false);
  //   }
  // }
}
