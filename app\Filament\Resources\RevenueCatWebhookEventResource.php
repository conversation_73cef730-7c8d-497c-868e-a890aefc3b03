<?php

namespace App\Filament\Resources;

use App\Filament\Actions\IsAdminAction;
use App\Filament\Resources\RevenueCatWebhookEventResource\Pages;
use App\Models\RevenueCatWebhookEvent;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class RevenueCatWebhookEventResource extends Resource
{
    protected static ?string $model = RevenueCatWebhookEvent::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static ?string $navigationGroup = 'Subscription Management';

    protected static ?string $navigationLabel = 'Webhook Events';

    protected static ?int $navigationSort = 6;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Event Information')
                    ->schema([
                        Forms\Components\TextInput::make('event_type')
                            ->required()
                            ->maxLength(255)
                            ->disabled(),
                        Forms\Components\TextInput::make('revenuecat_customer_id')
                            ->label('RevenueCat Customer ID')
                            ->maxLength(255)
                            ->disabled(),
                        Forms\Components\TextInput::make('revenuecat_original_app_user_id')
                            ->label('Original App User ID')
                            ->maxLength(255)
                            ->disabled(),
                        Forms\Components\TextInput::make('product_id')
                            ->maxLength(255)
                            ->disabled(),
                        Forms\Components\TextInput::make('entitlement_id')
                            ->maxLength(255)
                            ->disabled(),
                        Forms\Components\TextInput::make('store')
                            ->maxLength(255)
                            ->disabled(),
                        Forms\Components\TextInput::make('environment')
                            ->maxLength(255)
                            ->disabled(),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Processing Information')
                    ->schema([
                        Forms\Components\Select::make('user_id')
                            ->relationship('user', 'name')
                            ->disabled(),
                        Forms\Components\Select::make('subscription_id')
                            ->label('Subscription ID')
                            ->relationship('subscription', 'id')
                            ->searchable()
                            ->disabled(),
                        Forms\Components\Select::make('processing_status')
                            ->options([
                                'pending' => 'Pending',
                                'processed' => 'Processed',
                                'failed' => 'Failed',
                            ])
                            ->disabled(),
                        Forms\Components\Textarea::make('processing_error')
                            ->maxLength(65535)
                            ->disabled(),
                        Forms\Components\DateTimePicker::make('processed_at')
                            ->disabled(),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Webhook Payload')
                    ->schema([
                        Forms\Components\Textarea::make('webhook_payload')
                            ->label('Full Webhook Payload')
                            ->formatStateUsing(fn ($state) => json_encode($state, JSON_PRETTY_PRINT))
                            ->rows(15)
                            ->disabled(),
                    ])
                    ->collapsible(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID')
                    ->sortable(),
                Tables\Columns\TextColumn::make('event_type')
                    ->searchable()
                    ->sortable()
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'INITIAL_PURCHASE' => 'success',
                        'RENEWAL' => 'info',
                        'CANCELLATION' => 'warning',
                        'EXPIRATION' => 'danger',
                        'BILLING_ISSUE' => 'danger',
                        'PRODUCT_CHANGE' => 'primary',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('user.name')
                    ->searchable()
                    ->sortable()
                    ->placeholder('No User'),
                Tables\Columns\TextColumn::make('product_id')
                    ->searchable()
                    ->placeholder('N/A'),
                Tables\Columns\TextColumn::make('store')
                    ->searchable()
                    ->placeholder('N/A'),
                Tables\Columns\TextColumn::make('environment')
                    ->searchable()
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'PRODUCTION' => 'success',
                        'SANDBOX' => 'warning',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('processing_status')
                    ->searchable()
                    ->sortable()
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'processed' => 'success',
                        'pending' => 'warning',
                        'failed' => 'danger',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->label('Received At'),
                Tables\Columns\TextColumn::make('processed_at')
                    ->dateTime()
                    ->sortable()
                    ->placeholder('Not Processed'),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('event_type')
                    ->options([
                        'INITIAL_PURCHASE' => 'Initial Purchase',
                        'RENEWAL' => 'Renewal',
                        'CANCELLATION' => 'Cancellation',
                        'EXPIRATION' => 'Expiration',
                        'BILLING_ISSUE' => 'Billing Issue',
                        'PRODUCT_CHANGE' => 'Product Change',
                    ]),
                Tables\Filters\SelectFilter::make('processing_status')
                    ->options([
                        'pending' => 'Pending',
                        'processed' => 'Processed',
                        'failed' => 'Failed',
                    ]),
                Tables\Filters\SelectFilter::make('environment')
                    ->options([
                        'PRODUCTION' => 'Production',
                        'SANDBOX' => 'Sandbox',
                    ]),
                Tables\Filters\SelectFilter::make('store')
                    ->options([
                        'app_store' => 'Apple App Store',
                        'play_store' => 'Google Play Store',
                        'stripe' => 'Stripe',
                        'manual' => 'Manual/Dashboard',
                    ]),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\Action::make('reprocess')
                    ->label('Reprocess')
                    ->icon('heroicon-o-arrow-path')
                    ->color('warning')
                    ->visible(fn (RevenueCatWebhookEvent $record) => $record->processing_status === 'failed')
                    ->action(function (RevenueCatWebhookEvent $record) {
                        $webhookService = app(\App\Services\RevenueCatWebhookService::class);
                        
                        // Reset the event status
                        $record->update([
                            'processing_status' => 'pending',
                            'processing_error' => null,
                            'processed_at' => null,
                        ]);
                        
                        // Reprocess the event
                        $result = $webhookService->processWebhookEvent($record);
                        
                        if ($result['success']) {
                            $record->markAsProcessed($result['subscription_id'] ?? null);
                            
                            \Filament\Notifications\Notification::make()
                                ->success()
                                ->title('Event Reprocessed')
                                ->body('The webhook event was successfully reprocessed.')
                                ->send();
                        } else {
                            $record->markAsFailed($result['error'] ?? 'Unknown error');
                            
                            \Filament\Notifications\Notification::make()
                                ->danger()
                                ->title('Reprocessing Failed')
                                ->body($result['error'] ?? 'Unknown error occurred during reprocessing.')
                                ->send();
                        }
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListRevenueCatWebhookEvents::route('/'),
            'view' => Pages\ViewRevenueCatWebhookEvent::route('/{record}'),
        ];
    }

    public static function canViewAny(): bool
    {
        return IsAdminAction::handle();
    }

    public static function canCreate(): bool
    {
        return false; // Webhook events are created by the system
    }

    public static function canEdit($record): bool
    {
        return false; // Webhook events should not be edited
    }
} 