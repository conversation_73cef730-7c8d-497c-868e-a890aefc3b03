import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/core/app_theme.dart';
import 'package:zod/features/auction/data/models/auction.dart';

import 'package:zod/core/resources/app_colors.dart';
import 'package:zod/core/resources/app_icons.dart';
import 'package:zod/core/widgets/network_image_widget.dart';
import 'package:zod/features/auction/presentation/views/joined_view.dart';

class FooterParticipatorItemView extends StatelessWidget {
  const FooterParticipatorItemView({super.key, required this.auction});

  final Auction auction;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        SizedBox(
          width: (auction.avatarUrls.length * 14.0) + 20.0,
          height: 20,
          child: Stack(
            alignment: AlignmentDirectional.centerEnd,
            clipBehavior: Clip.none,
            children: [
              ...auction.avatarUrls.asMap().entries.map((entry) {
                int index = entry.key;
                String url = entry.value;
                return PositionedDirectional(
                  start: index * 14.0, // Overlap avatars
                  child: NetworkImageView(
                    url,
                    width: 20,
                    height: 20,
                    borderColor: AppColors.white,
                    radius: 11.21,
                  ),
                );
              }),
              PositionedDirectional(
                start: auction.avatarUrls.length * 14.0,
                child: Skeleton.ignore(
                  child: SvgPicture.asset(
                    auction.isAllSets
                        ? AppIcons.correct
                        : AppIcons.usersStacked,
                    width: 20,
                    height: 20,
                  ),
                ),
              ),
            ],
          ),
        ),
        SizedBox(width: (auction.isJoined ?? false) ? 3 : 8),
        auction.isJoined ?? false
            ? JoinedView()
            : Text(
                auction.isAllSets
                    ? context.localizations.all_seats_filled
                    : (auction.remainingParticipation ?? 0) > 5
                        ? context.localizations.dont_miss_out
                        : '${auction.remainingParticipation ?? 0} ${context.localizations.seats_left}',
                style: context.textTheme.labelSmall?.w400,
              ),
      ],
    );
  }
}
