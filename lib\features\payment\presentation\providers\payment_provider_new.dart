import 'dart:developer';

import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:zod/app/routes.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/core/notifications/notification_alert_view.dart';
import 'package:zod/features/auction/presentation/join_to_auction.dart';
import 'package:zod/features/auth/presentation/providers/me_provider.dart';
import 'package:zod/features/payment/data/api.dart';
import 'package:zod/features/payment/data/events/purchase_event_tracker.dart';
import 'package:zod/features/payment/data/models/payment_details.dart';
import 'package:zod/features/payment/presentation/views/payment_result_view.dart';
import 'package:zod/features/payment/services/payment_service.dart';
import 'package:zod/features/wallet/data/models/wallet_charge_dto.dart';
import 'package:zod/core/analytics/events/events.dart';

final paymentProvider =
    StateNotifierProvider.autoDispose<PaymentProvider, PaymentState>((ref) {
  return PaymentProvider(
    paymentService: sl<PaymentService>(),
    paymentApi: sl<PaymentApi>(),
    ref: ref,
  );
});

class PaymentState {
  final bool isLoading;
  final String? error;

  PaymentState({
    this.isLoading = false,
    this.error,
  });

  PaymentState copyWith({
    bool? isLoading,
    String? error,
  }) {
    return PaymentState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

class PaymentProvider extends StateNotifier<PaymentState> {
  final PaymentService paymentService;
  final PaymentApi paymentApi;
  final Ref ref;

  PaymentProvider({
    required this.paymentService,
    required this.paymentApi,
    required this.ref,
  }) : super(PaymentState());

  Future<void> processCardPayment({
    required BuildContext context,
    required WalletChargeDto chargeDto,
  }) async {
    state = state.copyWith(isLoading: true);

    try {
      // Determine payment type based on context
      final paymentType = PaymentType.fromAuctionContext(
        isAuctionRelated: chargeDto.auctionId != null,
        isEntryFee: chargeDto.auction?.isNotJoined == true,
        isBidding: chargeDto.auction?.isJoined == true,
      );

      log("processCardPayment ${paymentType.toString()}");
      log("processCardPayment isAuctionRelated ${chargeDto.auctionId != null}");
      log("processCardPayment isEntryFee ${chargeDto.auction?.isNotJoined == true}");
      log("processCardPayment isBidding ${chargeDto.auction?.isJoined == true}");

      // Payment service will handle initialization internally
      final totalAmount =
          chargeDto.totalAmount ?? chargeDto.amountPrice.toDouble();

      log("processCardPayment totalAmount: ${totalAmount}");

      // Track payment initiation
      trackPaymentInitiated(
        amount: totalAmount,
        currency: "SAR",
        paymentMethod: 'card',
        pointsPurchased: chargeDto.amountPoint,
        paymentType: paymentType,
        auctionId: chargeDto.auctionId?.toString(),
        auctionName: chargeDto.auction?.name,
      );

      // Start card payment with backend-calculated amount
      final result = await paymentService.startCardPayment(
        amount: totalAmount,
        currency: "SAR",
        cartDescription: "Purchase ${chargeDto.amountPoint} points",
        chargeDto: chargeDto,
      );

      // Handle payment result
      _handlePaymentResult(
        result,
        chargeDto.copyWith(
          totalAmount: totalAmount,
          paymentMethod: 'card',
        ),
      );
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());

      // Determine payment type for error tracking
      final paymentType = PaymentType.fromAuctionContext(
        isAuctionRelated: chargeDto.auctionId != null,
        isEntryFee: chargeDto.auction?.isNotJoined == true,
        isBidding: chargeDto.auction?.isJoined == true,
      );

      // Track payment failure
      trackPaymentFailed(
        amount: chargeDto.amountPrice.toDouble(),
        currency: "SAR",
        paymentMethod: 'card',
        pointsPurchased: chargeDto.amountPoint,
        errorMessage: e.toString(),
        paymentType: paymentType,
        auctionId: chargeDto.auctionId?.toString(),
        auctionName: chargeDto.auction?.name,
      );

      NotificationAlert.showLocalNotification(
        e.toString(),
        type: ToastType.error,
      );
    }
  }

  Future<void> processApplePayPayment({
    required BuildContext context,
    required WalletChargeDto chargeDto,
  }) async {
    state = state.copyWith(isLoading: true);

    try {
      // Determine payment type based on context
      final paymentType = PaymentType.fromAuctionContext(
        isAuctionRelated: chargeDto.auctionId != null,
        isEntryFee: chargeDto.auction?.isNotJoined == true,
        isBidding: chargeDto.auction?.isJoined == true,
      );

      // Payment service will handle initialization internally
      final totalAmount =
          chargeDto.totalAmount ?? chargeDto.amountPrice.toDouble();

      // Track payment initiation
      trackPaymentInitiated(
        amount: totalAmount,
        currency: "SAR",
        paymentMethod: 'apple_pay',
        pointsPurchased: chargeDto.amountPoint,
        paymentType: paymentType,
        auctionId: chargeDto.auctionId?.toString(),
        auctionName: chargeDto.auction?.name,
      );

      // Start Apple Pay payment with backend-calculated amount
      final result = await paymentService.startApplePayPayment(
        amount: totalAmount,
        currency: "SAR",
        cartDescription: "Purchase ${chargeDto.amountPoint} points",
        chargeDto: chargeDto,
      );

      // Handle payment result
      _handlePaymentResult(
        result,
        chargeDto.copyWith(
          totalAmount: totalAmount,
          paymentMethod: 'apple_pay',
        ),
      );
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());

      // Determine payment type for error tracking
      final paymentType = PaymentType.fromAuctionContext(
        isAuctionRelated: chargeDto.auctionId != null,
        isEntryFee: chargeDto.auction?.isNotJoined == true,
        isBidding: chargeDto.auction?.isJoined == true,
      );

      // Track payment failure
      trackPaymentFailed(
        amount: chargeDto.amountPrice.toDouble(),
        currency: "SAR",
        paymentMethod: 'apple_pay',
        pointsPurchased: chargeDto.amountPoint,
        errorMessage: e.toString(),
        paymentType: paymentType,
        auctionId: chargeDto.auctionId?.toString(),
        auctionName: chargeDto.auction?.name,
      );

      NotificationAlert.showLocalNotification(
        e.toString(),
        type: ToastType.error,
      );
    }
  }

  /// Process payment with a saved card using 3D secure tokenization that only requires CVV
  Future<void> process3DSecureTokenizedCardPayment({
    required BuildContext context,
    required WalletChargeDto chargeDto,
    required String cardToken,
    required String cardMask,
    required String cardType,
  }) async {
    state = state.copyWith(isLoading: true);

    try {
      // Determine payment type based on context
      final paymentType = PaymentType.fromAuctionContext(
        isAuctionRelated: chargeDto.auctionId != null,
        isEntryFee: chargeDto.auction?.isNotJoined == true,
        isBidding: chargeDto.auction?.isJoined == true,
      );

      // Payment service will handle initialization internally
      final totalAmount =
          chargeDto.totalAmount ?? chargeDto.amountPrice.toDouble();

      // Track payment initiation
      trackPaymentInitiated(
        amount: totalAmount,
        currency: "SAR",
        paymentMethod: 'saved_card',
        pointsPurchased: chargeDto.amountPoint,
        paymentType: paymentType,
        auctionId: chargeDto.auctionId?.toString(),
        auctionName: chargeDto.auction?.name,
      );

      // Start 3D secure tokenized card payment with backend-calculated amount
      final result = await paymentService.start3DSecureTokenizedCardPayment(
        amount: totalAmount,
        currency: "SAR",
        cartDescription: "Purchase ${chargeDto.amountPoint} points",
        chargeDto: chargeDto,
        cardToken: cardToken,
        cardMask: cardMask,
        cardType: cardType,
      );

      _handlePaymentResult(
        result,
        chargeDto.copyWith(
          totalAmount: totalAmount,
          paymentMethod: 'saved_card',
        ),
      );
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());

      // Determine payment type for error tracking
      final paymentType = PaymentType.fromAuctionContext(
        isAuctionRelated: chargeDto.auctionId != null,
        isEntryFee: chargeDto.auction?.isNotJoined == true,
        isBidding: chargeDto.auction?.isJoined == true,
      );

      // Track payment failure
      trackPaymentFailed(
        amount: chargeDto.amountPrice.toDouble(),
        currency: "SAR",
        paymentMethod: 'saved_card',
        pointsPurchased: chargeDto.amountPoint,
        errorMessage: e.toString(),
        paymentType: paymentType,
        auctionId: chargeDto.auctionId?.toString(),
        auctionName: chargeDto.auction?.name,
      );

      NotificationAlert.showLocalNotification(
        e.toString(),
        type: ToastType.error,
      );
    }
  }

  Future<void> _handlePaymentResult(
    Map<String, dynamic> result,
    WalletChargeDto chargeDto,
  ) async {
    // Format current date
    final dateFormat = DateFormat('E, d MMM yyyy');
    final currentDate = dateFormat.format(DateTime.now());

    if (result["status"] == "success") {
      final transactionDetails = result["data"];

      if (transactionDetails["isSuccess"]) {
        // Payment successful, update wallet
        try {
          // Extract card token information if available
          String? cardToken;
          String? cardMask;
          String? cardType;
          String? cardScheme;

          if (transactionDetails["token"] != null) {
            cardToken = transactionDetails["token"];
          }

          if (transactionDetails["paymentInfo"] != null) {
            final paymentInfo = transactionDetails["paymentInfo"];
            cardMask = paymentInfo["paymentDescription"];
            cardType = paymentInfo["cardType"];
            cardScheme = paymentInfo["cardScheme"];
          }

          // Update chargeDto with payment reference and card token info
          final updatedChargeDto = chargeDto.copyWith(
            paymentReference: transactionDetails["transactionReference"],
            transaction_id: transactionDetails["cartID"],
            cardToken: cardToken,
            cardMask: cardMask,
            cardType: cardType,
            cardScheme: cardScheme,
            transactionReference: transactionDetails["transactionReference"],
          );

          // Call API to process payment
          final chargeUser = await paymentApi.processPayment(updatedChargeDto);

          if (chargeUser.meta.success ?? false) {
            // Determine payment type for successful purchase tracking
            final paymentType = PaymentType.fromAuctionContext(
              isAuctionRelated: chargeDto.auctionId != null,
              isEntryFee: chargeDto.auction?.isNotJoined == true,
              isBidding: chargeDto.auction?.isJoined == true,
            );

            // Track successful purchase event
            trackPurchase(
              transactionId: updatedChargeDto.transaction_id ??
                  transactionDetails["cartID"] ??
                  'unknown',
              amount: chargeUser.meta.totalToPay ??
                  chargeDto.totalAmount ??
                  chargeDto.amountPrice.toDouble(),
              taxAmount:
                  chargeUser.meta.taxAmount ?? chargeDto.vatAmount ?? 0.0,
              currency: 'SAR',
              paymentMethod: chargeDto.paymentMethod ?? 'card',
              pointsPurchased: chargeDto.amountPoint,
              paymentType: paymentType,
              auctionId: chargeDto.auctionId?.toString(),
              auctionName: chargeDto.auction?.name,
            );

            ref.refresh(meProvider);

            if (chargeDto.auction != null) {
              goRouter.pop();
              goRouter.pop();

              if (chargeDto.auction?.isNotJoined ?? false) {
                goRouter.push(JoinToAuctionView.route,
                    extra: chargeDto.auction);
              }

              NotificationAlert.showLocalNotification(
                chargeUser.meta.message ??
                    "You have successfully purchased points.",
                type: ToastType.success,
                align: Alignment.topCenter,
              );
            } else {
              final paymentDetails = PaymentDetails.success(
                  paymentMethod: chargeDto.paymentMethod ?? 'Card',
                  date: currentDate,
                  points: chargeDto.amountPoint,
                  amount: chargeUser.meta.totalToPay ??
                      chargeDto.amountPrice.toDouble(),
                  invoiceNumber: chargeUser.meta.invoice_id ?? '',
                  vatNumber: '*********',
                  tax: chargeUser.meta.taxAmount ?? chargeDto.vatAmount,
                  type: chargeDto.auctionId != null
                      ? PaymentDetailsType.claim
                      : PaymentDetailsType.charge);
              goRouter.go(PaymentResultView.route, extra: paymentDetails);
              NotificationAlert.showLocalNotification(
                chargeUser.meta.message ??
                    "You have successfully purchased points.",
                type: ToastType.success,
                align: Alignment.topCenter,
              );
            }
          } else {
            // API error
            final errorMessage =
                chargeUser.meta.message ?? "Payment processing failed";

            // Determine payment type for failure tracking
            final paymentType = PaymentType.fromAuctionContext(
              isAuctionRelated: chargeDto.auctionId != null,
              isEntryFee: chargeDto.auction?.isNotJoined == true,
              isBidding: chargeDto.auction?.isJoined == true,
            );

            // Track payment failure
            trackPaymentFailed(
              amount: chargeDto.totalAmount ?? chargeDto.amountPrice.toDouble(),
              currency: "SAR",
              paymentMethod: chargeDto.paymentMethod ?? 'unknown',
              pointsPurchased: chargeDto.amountPoint,
              errorMessage: errorMessage,
              paymentType: paymentType,
              auctionId: chargeDto.auctionId?.toString(),
              auctionName: chargeDto.auction?.name,
              transactionId: chargeDto.transaction_id,
            );

            // Create payment details for failure
            final paymentDetails = PaymentDetails.failed(
              paymentMethod: chargeDto.paymentMethod ?? 'Card',
              date: currentDate,
              points: chargeDto.amountPoint,
              amount: chargeDto.amountPrice.toDouble(),
              errorMessage: errorMessage,
            );
            goRouter.pop();

            // Navigate to payment result page
            goRouter.push(PaymentResultView.route, extra: paymentDetails);
          }
        } catch (e) {
          // Error updating wallet
          final errorMessage = e.toString();

          // Determine payment type for failure tracking
          final paymentType = PaymentType.fromAuctionContext(
            isAuctionRelated: chargeDto.auctionId != null,
            isEntryFee: chargeDto.auction?.isNotJoined == true,
            isBidding: chargeDto.auction?.isJoined == true,
          );

          // Track payment failure
          trackPaymentFailed(
            amount: chargeDto.totalAmount ?? chargeDto.amountPrice.toDouble(),
            currency: "SAR",
            paymentMethod: chargeDto.paymentMethod ?? 'unknown',
            pointsPurchased: chargeDto.amountPoint,
            errorMessage: errorMessage,
            paymentType: paymentType,
            auctionId: chargeDto.auctionId?.toString(),
            auctionName: chargeDto.auction?.name,
            transactionId: chargeDto.transaction_id,
          );

          // Create payment details for failure
          final paymentDetails = PaymentDetails.failed(
            paymentMethod: chargeDto.paymentMethod ?? 'Card',
            date: currentDate,
            points: chargeDto.amountPoint,
            amount: chargeDto.amountPrice.toDouble(),
            errorMessage: errorMessage,
          );
          goRouter.pop();

          // Navigate to payment result page
          goRouter.push(PaymentResultView.route, extra: paymentDetails);
        }
      } else {
        // Payment failed

        final errorMessage = appContext.localizations.payment_failed;

        // Determine payment type for failure tracking
        final paymentType = PaymentType.fromAuctionContext(
          isAuctionRelated: chargeDto.auctionId != null,
          isEntryFee: chargeDto.auction?.isNotJoined == true,
          isBidding: chargeDto.auction?.isJoined == true,
        );

        // Track payment failure
        trackPaymentFailed(
          amount: chargeDto.totalAmount ?? chargeDto.amountPrice.toDouble(),
          currency: "SAR",
          paymentMethod: chargeDto.paymentMethod ?? 'unknown',
          pointsPurchased: chargeDto.amountPoint,
          errorMessage: errorMessage,
          paymentType: paymentType,
          auctionId: chargeDto.auctionId?.toString(),
          auctionName: chargeDto.auction?.name,
          transactionId: chargeDto.transaction_id,
        );

        // Create payment details for failure
        final paymentDetails = PaymentDetails.failed(
          paymentMethod: chargeDto.paymentMethod ?? 'Card',
          date: currentDate,
          points: chargeDto.amountPoint,
          amount: chargeDto.amountPrice.toDouble(),
          errorMessage: errorMessage,
        );
        goRouter.pop();

        // Navigate to payment result page
        goRouter.push(PaymentResultView.route, extra: paymentDetails);
      }
    } else if (result["status"] == "error") {
      // Payment error
      final errorMessage =
          result["message"] ?? appContext.localizations.payment_error;

      // Determine payment type for failure tracking
      final paymentType = PaymentType.fromAuctionContext(
        isAuctionRelated: chargeDto.auctionId != null,
        isEntryFee: chargeDto.auction?.isNotJoined == true,
        isBidding: chargeDto.auction?.isJoined == true,
      );

      // Track payment failure
      trackPaymentFailed(
        amount: chargeDto.totalAmount ?? chargeDto.amountPrice.toDouble(),
        currency: "SAR",
        paymentMethod: chargeDto.paymentMethod ?? 'unknown',
        pointsPurchased: chargeDto.amountPoint,
        errorMessage: errorMessage,
        paymentType: paymentType,
        auctionId: chargeDto.auctionId?.toString(),
        auctionName: chargeDto.auction?.name,
        transactionId: chargeDto.transaction_id,
      );

      // Create payment details for failure
      final paymentDetails = PaymentDetails.failed(
        paymentMethod: chargeDto.paymentMethod ?? 'Card',
        date: currentDate,
        points: chargeDto.amountPoint,
        amount: chargeDto.amountPrice.toDouble(),
        errorMessage: errorMessage,
      );
      goRouter.pop();

      // Navigate to payment result page
      goRouter.push(PaymentResultView.route, extra: paymentDetails);
    } else if (result["status"] == "event") {
      // Payment cancelled
      final errorMessage = appContext.localizations.payment_cancelled;

      // Determine payment type for failure tracking
      final paymentType = PaymentType.fromAuctionContext(
        isAuctionRelated: chargeDto.auctionId != null,
        isEntryFee: chargeDto.auction?.isNotJoined == true,
        isBidding: chargeDto.auction?.isJoined == true,
      );

      // Track payment failure (cancellation)
      trackPaymentFailed(
        amount: chargeDto.totalAmount ?? chargeDto.amountPrice.toDouble(),
        currency: "SAR",
        paymentMethod: chargeDto.paymentMethod ?? 'unknown',
        pointsPurchased: chargeDto.amountPoint,
        errorMessage: errorMessage,
        paymentType: paymentType,
        auctionId: chargeDto.auctionId?.toString(),
        auctionName: chargeDto.auction?.name,
        transactionId: chargeDto.transaction_id,
      );

      // Create payment details for failure
      final paymentDetails = PaymentDetails.failed(
        paymentMethod: chargeDto.paymentMethod ?? 'Card',
        date: currentDate,
        points: chargeDto.amountPoint,
        amount: chargeDto.amountPrice.toDouble(),
        errorMessage: errorMessage,
      );

      goRouter.pop();
      // Navigate to payment result page
      goRouter.push(PaymentResultView.route, extra: paymentDetails);
    }

    state = state.copyWith(isLoading: false);
  }
}
