<?php $__env->startSection('title', 'rydo'); ?>

<?php $__env->startSection('content'); ?>
    <!-- Hero Section -->
    <div class="container" style="position: relative; overflow: hidden;">
        <section class="hero-section">
            <h1>
                Refine your daily options
            </h1>

            <p> Discover the UAE like never before — smarter, faster, and easier. </p>
            
            <div class="search-container">
                <!-- Debug URL: <?php echo e(asset('assets/images/agent.png')); ?> -->
                <img class="robot-avatar" src="<?php echo e(asset('assets/images/agent.png')); ?>" alt="Rydo Agent">
                
                <!-- Initial textarea that will be replaced by chat interface -->
                <div id="initial-input-container">
                    <textarea id="searchTextarea" placeholder=""></textarea>
                    <button id="sendButton" class="send-button">
                        <img src="<?php echo e(asset('assets/images/sendbtn.svg')); ?>" alt="Send" class="send-icon">
                    </button>
                </div>
                
                <!-- Chat interface (initially hidden) -->
                <div id="chat-container" class="chat-container" style="display: none;">
                    <div class="chat-messages" id="chat-messages">
                        <!-- Messages will be added here dynamically -->
                    </div>
                    <div class="chat-input-container">
                        <textarea class="chat-input" id="chat-input" placeholder="Type your message..."></textarea>
                        <button class="send-button" id="chat-send-button">
                        <img src="<?php echo e(asset('assets/images/sendbtn.svg')); ?>" alt="Send" class="send-icon">
                        </button>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- Testimonials Section -->
    <section class="testimonials">
            <div class="testimonial-slider owl-carousel owl-theme">
                <div class="testimonial-item">
                    <div class="item-head">
                        <img src="<?php echo e(asset('assets/images/quote.svg')); ?>" alt="Quote" class="quote-icon">

                        <div class="stars">
                            <img src="<?php echo e(asset('assets/images/star.png')); ?>" alt="Star">
                            <img src="<?php echo e(asset('assets/images/star.png')); ?>" alt="Star">
                            <img src="<?php echo e(asset('assets/images/star.png')); ?>" alt="Star">
                            <img src="<?php echo e(asset('assets/images/star.png')); ?>" alt="Star">
                            <img src="<?php echo e(asset('assets/images/star.png')); ?>" alt="Star">
                        </div>
                    </div>

                    <p> Like texting friend, the chat instantly recommended stunning parks and events, saved me hours exploring UAE! </p>

                    <div class="clinet">
                        <img src="<?php echo e(asset('assets/images/avatars/av5.png')); ?>" alt="Client">
                        <div>
                            <p>Mariam Hassan</p>
                            <span>Dubai, UAE</span>
                        </div>
                    </div>
                </div>

                <div class="testimonial-item">
                    <div class="item-head">
                        <img src="<?php echo e(asset('assets/images/quote.svg')); ?>" alt="Quote" class="quote-icon">

                        <div class="stars">
                            <img src="<?php echo e(asset('assets/images/star.png')); ?>" alt="Star">
                            <img src="<?php echo e(asset('assets/images/star.png')); ?>" alt="Star">
                            <img src="<?php echo e(asset('assets/images/star.png')); ?>" alt="Star">
                            <img src="<?php echo e(asset('assets/images/star.png')); ?>" alt="Star">
                            <img src="<?php echo e(asset('assets/images/star.png')); ?>" alt="Star">
                        </div>
                    </div>

                    <p>Hidden cafés in Deira popped with personalized menus and Reels-style swiping, a delightful discovery every time.</p>

                    <div class="clinet">
                    <img src="<?php echo e(asset('assets/images/avatars/av1.png')); ?>" alt="Client">
                        <div>
                            <p>Ryan Campbell</p>
                            <span>Abu Dhabi, UAE</span>
                        </div>
                    </div>
                </div>

                <div class="testimonial-item">
                    <div class="item-head">
                        <img src="<?php echo e(asset('assets/images/quote.svg')); ?>" alt="Quote" class="quote-icon">

                        <div class="stars">
                            <img src="<?php echo e(asset('assets/images/star.png')); ?>" alt="Star">
                            <img src="<?php echo e(asset('assets/images/star.png')); ?>" alt="Star">
                            <img src="<?php echo e(asset('assets/images/star.png')); ?>" alt="Star">
                            <img src="<?php echo e(asset('assets/images/star.png')); ?>" alt="Star">
                            <img src="<?php echo e(asset('assets/images/star.png')); ?>" alt="Star">
                        </div>
                    </div>

                    <p>When I needed a pharmacy at midnight, the "open now" filter pinpointed one blocks away, lifesaver!</p>

                    <div class="clinet">
                    <img src="<?php echo e(asset('assets/images/avatars/av2.png')); ?>" alt="Client">
                        <div>
                            <p>Sofia Müller</p>
                            <span>Sharjah, UAE</span>
                        </div>
                    </div>
                </div>

                <div class="testimonial-item">
                    <div class="item-head">
                        <img src="<?php echo e(asset('assets/images/quote.svg')); ?>" alt="Quote" class="quote-icon">

                        <div class="stars">
                            <img src="<?php echo e(asset('assets/images/star.png')); ?>" alt="Star">
                            <img src="<?php echo e(asset('assets/images/star.png')); ?>" alt="Star">
                            <img src="<?php echo e(asset('assets/images/star.png')); ?>" alt="Star">
                            <img src="<?php echo e(asset('assets/images/star.png')); ?>" alt="Star">
                            <img src="<?php echo e(asset('assets/images/star.png')); ?>" alt="Star">
                        </div>
                    </div>

                    <p>Freelancing became easier once I discovered co-working cafés with strong Wi-Fi and power outlets nearby.</p>

                    <div class="clinet">
                    <img src="<?php echo e(asset('assets/images/avatars/av3.png')); ?>" alt="Client">
                        <div>
                            <p>Faisal Al-Rashid</p>
                            <span>Dubai, UAE</span>
                        </div>
                    </div>
                </div>

                <div class="testimonial-item">
                    <div class="item-head">
                        <img src="<?php echo e(asset('assets/images/quote.svg')); ?>" alt="Quote" class="quote-icon">

                        <div class="stars">
                            <img src="<?php echo e(asset('assets/images/star.png')); ?>" alt="Star">
                            <img src="<?php echo e(asset('assets/images/star.png')); ?>" alt="Star">
                            <img src="<?php echo e(asset('assets/images/star.png')); ?>" alt="Star">
                            <img src="<?php echo e(asset('assets/images/star.png')); ?>" alt="Star">
                            <img src="<?php echo e(asset('assets/images/star.png')); ?>" alt="Star">
                        </div>
                    </div>

                    <p>Exploring Dubai's hidden gems was effortless thanks to trip recommendations and local eatery suggestions, unforgettable journey.</p>

                    <div class="clinet">
                    <img src="<?php echo e(asset('assets/images/avatars/av4.png')); ?>" alt="Client">
                        <div>
                            <p>James Walker</p>
                            <span>London, UK</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    <!-- End of Testimonials Section -->

    <!-- Phone Modal -->
    <div class="modal fade custom-modal" id="phoneModal" tabindex="-1" aria-labelledby="phoneModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                    <img src="<?php echo e(asset('assets/images/close.svg')); ?>" alt="Close" width="24" height="25">
                </button>
                <div class="modal-body">
                    <p>Add your UAE number & unlock early access.</p>
                    <div class="form-group">
                        <div class="input-group">
                            <span class="input-group-text">
                                <img src="<?php echo e(asset('assets/images/uae-flag.png')); ?>" alt="UAE Flag">
                                +971
                            </span>
                            <div class="phone-input-container">
                                <input type="tel" class="form-control" id="phoneInput" placeholder="XXXXXXXX" aria-label="Phone number" maxlength="8" autocomplete="tel" inputmode="numeric" pattern="[0-9]*">
                            </div>
                        </div>
                        <div id="phoneError" class="text-danger mt-2" style="display: none;"></div>
                    </div>
                    <button type="button" class="count-me-in-btn" id="submitPhone">Count me in</button>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('styles'); ?>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="<?php echo e(asset('assets/images/robot.svg')); ?>">
    
    <style>
        .testimonial-slider .owl-dots {
            margin-top: 20px;
            text-align: center;
        }
    </style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const searchTextarea = document.getElementById('searchTextarea');
            const sendButton = document.getElementById('sendButton');
            const initialInputContainer = document.getElementById('initial-input-container');
            const chatContainer = document.getElementById('chat-container');
            const chatMessages = document.getElementById('chat-messages');
            const chatInput = document.getElementById('chat-input');
            const chatSendButton = document.getElementById('chat-send-button');
            const phoneModal = new bootstrap.Modal(document.getElementById('phoneModal'));
            const submitPhoneBtn = document.getElementById('submitPhone');
            const robotAvatar = document.querySelector('.robot-avatar');
            const phoneInput = document.getElementById('phoneInput');
            const phoneError = document.getElementById('phoneError');
            
            // Initialize Owl Carousel for testimonials
            $('.testimonial-slider').owlCarousel({
                loop: true,
                margin: 16,
                nav: false,
                dots: false,
                autoplay: true,
                autoplayTimeout: 4000,
                autoplayHoverPause: true,
                stagePadding: 240,
                smartSpeed: 500,
                responsive: {
                    0: {
                        items: 1,
                        stagePadding: 36,
                    },
                    768: {
                        items: 1,
                        stagePadding: 52,
                    },
                    992: {
                        items: 2
                    }
                }
            });
            
            // Track if user has completed phone submission
            let phoneSubmitted = false;
            // Store the submitted phone number
            let savedPhoneNumber = '';
            
            // Random messages for bot responses
            const randomMessages = [
                "Hi, I'm rydo. I'm preparing for my grand arrival in the UAE. Stay close — you'll be the first to receive your exclusive early access."
            ];
            
            // Message for when user closes modal without submitting
            const modalClosedMessage = "Hi, I'm rydo. I'm preparing for my grand arrival in the UAE.";
            
            // Track the last used message index
            let lastMessageIndex = -1;
            
            // Function to get a random message
            function getRandomMessage(includeAccessLink = false) {
                let randomIndex;
                
                // Keep generating a new random index until it's different from the last one
                do {
                    randomIndex = Math.floor(Math.random() * randomMessages.length);
                } while (randomIndex === lastMessageIndex && randomMessages.length > 1);
                
                // Update the last used index
                lastMessageIndex = randomIndex;
                
                let message = randomMessages[randomIndex];
                
                if (includeAccessLink) {
                    message += " You'll receive the access link shortly. 🚀🎉";
                }
                
                return message;
            }
            
            // Rotating placeholder texts
            const placeholderTexts = [
                // "Where can I get groceries after midnight near Downtown?",
                "Craving Italian — but not just any. The best.",
                "Looking for a workspace that doesn't feel like… work.",
                "Fun places in Dubai that locals actually love?"
            ];
            
            let currentPlaceholderIndex = 0;
            
            // Function to animate typing and deleting
            function animatePlaceholder() {
                // Don't animate if the textarea is focused
                if (document.activeElement === searchTextarea) {
                    return;
                }
                
                const placeholder = placeholderTexts[currentPlaceholderIndex];
                const prefix = "";
                const suffix = placeholder.substring(prefix.length);
                
                // First show the prefix
                searchTextarea.placeholder = prefix;
                
                // Then type out the suffix
                let currentSuffix = "";
                let i = 0;
                
                function typeSuffix() {
                    // Don't continue if the textarea is focused
                    if (document.activeElement === searchTextarea) {
                        return;
                    }
                    
                    if (i < suffix.length) {
                        currentSuffix += suffix.charAt(i);
                        searchTextarea.placeholder = prefix + currentSuffix;
                        i++;
                        setTimeout(typeSuffix, 50);
                    } else {
                        // When fully typed, wait before deleting
                        setTimeout(deleteSuffix, 2000);
                    }
                }
                
                function deleteSuffix() {
                    // Don't continue if the textarea is focused
                    if (document.activeElement === searchTextarea) {
                        return;
                    }
                    
                    if (currentSuffix.length > 0) {
                        currentSuffix = currentSuffix.substring(0, currentSuffix.length - 1);
                        searchTextarea.placeholder = prefix + currentSuffix;
                        setTimeout(deleteSuffix, 25);
                    } else {
                        // When fully deleted, move to next placeholder
                        currentPlaceholderIndex = (currentPlaceholderIndex + 1) % placeholderTexts.length;
                        setTimeout(animatePlaceholder, 500);
                    }
                }
                
                // Start typing the suffix
                setTimeout(typeSuffix, 500);
            }
            
            // Function to animate chat textarea placeholder
            function animateChatPlaceholder() {
                // Don't animate if the textarea is focused
                if (document.activeElement === chatInput) {
                    return;
                }
                
                const placeholder = placeholderTexts[currentPlaceholderIndex];
                const prefix = "";
                const suffix = placeholder.substring(prefix.length);
                
                // First show the prefix
                chatInput.placeholder = prefix;
                
                // Then type out the suffix
                let currentSuffix = "";
                let i = 0;
                
                function typeSuffix() {
                    // Don't continue if the textarea is focused
                    if (document.activeElement === chatInput) {
                        return;
                    }
                    
                    if (i < suffix.length) {
                        currentSuffix += suffix.charAt(i);
                        chatInput.placeholder = prefix + currentSuffix;
                        i++;
                        setTimeout(typeSuffix, 50);
                    } else {
                        // When fully typed, wait before deleting
                        setTimeout(deleteSuffix, 2000);
                    }
                }
                
                function deleteSuffix() {
                    // Don't continue if the textarea is focused
                    if (document.activeElement === chatInput) {
                        return;
                    }
                    
                    if (currentSuffix.length > 0) {
                        currentSuffix = currentSuffix.substring(0, currentSuffix.length - 1);
                        chatInput.placeholder = prefix + currentSuffix;
                        setTimeout(deleteSuffix, 25);
                    } else {
                        // When fully deleted, move to next placeholder
                        currentPlaceholderIndex = (currentPlaceholderIndex + 1) % placeholderTexts.length;
                        setTimeout(animateChatPlaceholder, 500);
                    }
                }
                
                // Start typing the suffix
                setTimeout(typeSuffix, 500);
            }
            
            // Start the animation
            animatePlaceholder();
            
            // Add input validation for phone number
            phoneInput.addEventListener('input', function(e) {
                // Remove any non-numeric characters
                this.value = this.value.replace(/[^0-9]/g, '');
                
                // Clear error message when user starts typing
                phoneError.style.display = 'none';
            });
            
            // Add Enter key handler for phone input
            phoneInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    submitPhone();
                }
            });
            
            // Add Enter key handler for search textarea
            searchTextarea.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault(); // Prevent default enter behavior
                    const message = searchTextarea.value.trim();
                    if (message !== '') {
                        // Add user message to chat
                        addMessage(message, 'user');
                        
                        // Hide initial input and show chat interface
                        initialInputContainer.style.display = 'none';
                        chatContainer.style.display = 'flex';
                        
                        // Don't start the chat placeholder animation yet
                        // It will be started after phone submission
                        
                        // Show bot response asking for phone number if not submitted yet
                        if (!phoneSubmitted) {
                            setTimeout(() => {
                                addMessage('Just need your UAE phone number to keep going 🚀', 'bot');

                                chatInput.disabled = true;
                            
                                // Show phone modal after bot response with a 1-second delay
                                setTimeout(() => {
                                    phoneModal.show();
                                    chatInput.disabled = false;
                                }, 1000);
                            }, 1000);
                        } else {
                            // If phone already submitted, start animation and respond normally
                            animateChatPlaceholder();
                            setTimeout(() => {
                                addMessage(getRandomMessage(false), 'bot');
                            }, 1000);
                        }
                    }
                }
            });
            
            // Add click handler for close button
            document.querySelector('.btn-close').addEventListener('click', function() {
                phoneModal.hide();
                // Reset phone input when modal is closed
                phoneInput.value = '';
                
                // If phone not submitted yet, show the thanks message
                if (!phoneSubmitted) {
                    // Mark phone as submitted to prevent modal from showing again
                    phoneSubmitted = true;
                    
                    // Hide chat input container instead of just disabling
                    document.querySelector('.chat-input-container').style.display = 'none';
                    
                    // Don't start the chat placeholder animation
                    // animateChatPlaceholder();
                    
                    // Show typing indicator
                    const typingIndicator = showTypingIndicator();
                    
                    // Get the initial message
                    const initialMessage = searchTextarea.value.trim();
                    
                    // Get CSRF token
                    const token = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
                    
                    // Save message to database without phone number
                    fetch('/api/messages', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': token,
                            'Accept': 'application/json'
                        },
                        body: JSON.stringify({
                            message: initialMessage
                        })
                    })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }
                        return response.json();
                    })
                    .then(data => {
                        // Remove typing indicator
                        removeTypingIndicator();
                        
                        // Add success message from bot without the early access part
                        addMessage(modalClosedMessage, 'bot');
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        // Remove typing indicator
                        removeTypingIndicator();
                        addMessage('Sorry, there was an error saving your message. Please try again.', 'bot');
                    });
                }
            });
            
            // Add event listener for modal hidden event (triggered when modal is closed by any means)
            document.getElementById('phoneModal').addEventListener('hidden.bs.modal', function() {
                // Reset phone input when modal is closed
                phoneInput.value = '';
                
                // If phone not submitted yet, show the thanks message
                if (!phoneSubmitted) {
                    // Mark phone as submitted to prevent modal from showing again
                    phoneSubmitted = true;
                    
                    // Hide chat input container instead of just disabling
                    document.querySelector('.chat-input-container').style.display = 'none';
                    
                    // Don't start the chat placeholder animation
                    // animateChatPlaceholder();
                    
                    // Show typing indicator
                    const typingIndicator = showTypingIndicator();
                    
                    // Get the initial message
                    const initialMessage = searchTextarea.value.trim();
                    
                    // Get CSRF token
                    const token = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
                    
                    // Save message to database without phone number
                    fetch('/api/messages', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': token,
                            'Accept': 'application/json'
                        },
                        body: JSON.stringify({
                            message: initialMessage
                        })
                    })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }
                        return response.json();
                    })
                    .then(data => {
                        // Remove typing indicator
                        removeTypingIndicator();
                        
                        // Add success message from bot without the early access part
                        addMessage(modalClosedMessage, 'bot');
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        // Remove typing indicator
                        removeTypingIndicator();
                        addMessage('Sorry, there was an error saving your message. Please try again.', 'bot');
                    });
                }
            });
            
            // Show modal when send button is clicked and textarea has content
            sendButton.addEventListener('click', function() {
                const message = searchTextarea.value.trim();
                if (message !== '') {
                    // Add user message to chat
                    addMessage(message, 'user');
                    
                    // Hide initial input and show chat interface
                    initialInputContainer.style.display = 'none';
                    chatContainer.style.display = 'flex';
                    
                    // Don't start the chat placeholder animation yet
                    // It will be started after phone submission
                    
                    // Show bot response asking for phone number if not submitted yet
                    if (!phoneSubmitted) {
                        setTimeout(() => {
                            addMessage('Just need your UAE phone number to keep going 🚀', 'bot');
                            
                            // Show phone modal after bot response with a 1-second delay
                            setTimeout(() => {
                                phoneModal.show();
                            }, 1500);
                        }, 1000);
                    } else {
                        // If phone already submitted, start animation and respond normally
                        animateChatPlaceholder();
                        setTimeout(() => {
                            addMessage(getRandomMessage(false), 'bot');
                        }, 1000);
                    }
                }
            });
            
            // Handle chat input submission
            chatSendButton.addEventListener('click', sendChatMessage);
            chatInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendChatMessage();
                }
            });
            
            function sendChatMessage() {
                const message = chatInput.value.trim();
                if (message !== '') {
                    // Add user message to chat
                    addMessage(message, 'user');
                    
                    // Clear input
                    chatInput.value = '';
                    
                    // If phone not submitted yet, show modal again
                    if (!phoneSubmitted) {
                        setTimeout(() => {
                            addMessage('Just need your UAE phone number to keep going 🚀', 'bot');
                            setTimeout(() => {
                                phoneModal.show();
                            }, 1500);
                        }, 1000);
                    } else {
                        // Show typing indicator
                        const typingIndicator = showTypingIndicator();
                        
                        // Save the message and show bot response
                        saveMessage(message)
                            .then(data => {
                                // Keep typing indicator for 1 second before showing response
                                setTimeout(() => {
                                    // Remove typing indicator
                                    removeTypingIndicator();
                                    
                                    // Add bot response
                                    addMessage(getRandomMessage(false), 'bot');
                                }, 1000);
                            })
                            .catch(error => {
                                console.error('Error:', error);
                                // Keep typing indicator for 1 second before showing error
                                setTimeout(() => {
                                    removeTypingIndicator();
                                    addMessage('Sorry, there was an error saving your message. Please try again.', 'bot');
                                }, 1000);
                            });
                    }
                }
            }
            
            function addMessage(text, sender) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${sender}-message`;
                
                const messageContent = document.createElement('div');
                messageContent.className = 'message-content';
                messageContent.textContent = text;
                
                messageDiv.appendChild(messageContent);
                chatMessages.appendChild(messageDiv);
                
                // Scroll to bottom
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }
            
            // Add typing indicator function
            function showTypingIndicator() {
                const typingDiv = document.createElement('div');
                typingDiv.className = 'message bot-message typing-indicator';
                typingDiv.id = 'typing-indicator';
                
                const typingContent = document.createElement('div');
                typingContent.className = 'message-content typing-dots';
                typingContent.innerHTML = '<span></span><span></span><span></span>';
                
                typingDiv.appendChild(typingContent);
                chatMessages.appendChild(typingDiv);
                
                // Scroll to bottom
                chatMessages.scrollTop = chatMessages.scrollHeight;
                
                return typingDiv;
            }
            
            function removeTypingIndicator() {
                const typingIndicator = document.getElementById('typing-indicator');
                if (typingIndicator) {
                    typingIndicator.remove();
                }
            }
            
            // Handle phone submission
            submitPhoneBtn.addEventListener('click', submitPhone);
            
            function submitPhone() {
                const phoneNumber = '5' + phoneInput.value.trim();
                const initialMessage = searchTextarea.value.trim();
                
                // Validate phone number
                if (phoneInput.value.trim() === '') {
                    phoneError.textContent = 'Phone number is required';
                    phoneError.style.display = 'block';
                    return;
                }
                
                if (phoneInput.value.trim().length < 8) {
                    phoneError.textContent = 'Phone number must be 8 digits after 5';
                    phoneError.style.display = 'block';
                    return;
                }
                
                // Mark phone as submitted and save the number
                phoneSubmitted = true;
                savedPhoneNumber = phoneNumber;
                
                // Close the modal
                phoneModal.hide();
                
                // Hide chat input container instead of just disabling
                document.querySelector('.chat-input-container').style.display = 'none';
                
                // Add phone number to chat
                setTimeout(() => {
                    addMessage('+971 ' + phoneNumber, 'user');
                    
                    // Show typing indicator
                    const typingIndicator = showTypingIndicator();
                    
                    // Get CSRF token
                    const token = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
                    
                    // Prepare request body
                    const requestBody = {
                        message: initialMessage
                    };
                    
                    // Only include mobile_number if it exists
                    if (phoneNumber) {
                        requestBody.mobile_number = phoneNumber;
                    }
                    
                    // Save both message and phone number to database
                    fetch('/api/messages', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': token,
                            'Accept': 'application/json'
                        },
                        body: JSON.stringify(requestBody)
                    })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }
                        return response.json();
                    })
                    .then(data => {
                        console.log('Success:', data);
                        if (data.success) {
                            // Keep typing indicator for 1 second before showing response
                            setTimeout(() => {
                                // Remove typing indicator
                                removeTypingIndicator();
                                
                                // Add success message from bot
                                addMessage("Hi, I'm rydo. I'm preparing for my grand arrival in the UAE. Stay close — you'll be the first to receive your exclusive early access. 🚀", 'bot');
                            }, 1000);
                        } else {
                            console.error('Error saving message:', data.error);
                            // Keep typing indicator for 1 second before showing error
                            setTimeout(() => {
                                removeTypingIndicator();
                                addMessage('Sorry, there was an error saving your information. Please try again.', 'bot');
                            }, 1000);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        // Keep typing indicator for 1 second before showing error
                        setTimeout(() => {
                            removeTypingIndicator();
                            addMessage('Sorry, there was an error saving your information. Please try again.', 'bot');
                        }, 1000);
                    });
                }, 300);
            }
            
            // Function to save a message to the database
            function saveMessage(message) {
                // Get CSRF token
                const token = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
                
                // Prepare request body
                const requestBody = {
                    message: message
                };
                
                // Only include mobile_number if it exists
                if (savedPhoneNumber) {
                    requestBody.mobile_number = savedPhoneNumber;
                }
                
                // Save message to database
                return fetch('/api/messages', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': token,
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(requestBody)
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                });
            }
            
            // Add focus and blur event listeners to stop/resume animation
            searchTextarea.addEventListener('focus', function() {
                // Set a static placeholder when focused
                const placeholder = placeholderTexts[currentPlaceholderIndex];
                searchTextarea.placeholder = placeholder;
            });
            
            searchTextarea.addEventListener('blur', function() {
                // Only resume animation if phone has been submitted
                if (phoneSubmitted) {
                    animatePlaceholder();
                }
            });
            
            chatInput.addEventListener('focus', function() {
                // Set a static placeholder when focused
                const placeholder = placeholderTexts[currentPlaceholderIndex];
                chatInput.placeholder = placeholder;
            });
            
            chatInput.addEventListener('blur', function() {
                // Only resume animation if phone has been submitted and chat is not disabled
                if (phoneSubmitted && !chatInput.disabled) {
                    animateChatPlaceholder();
                }
            });
            
            // Add navigation button functionality
            $('.prev-btn').on('click', function() {
                $('.testimonial-slider').trigger('prev.owl.carousel');
            });
            
            $('.next-btn').on('click', function() {
                $('.testimonial-slider').trigger('next.owl.carousel');
            });
        });
    </script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app_v3', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\GitHub\One Studio\rydo-backend\resources\views/index_v3.blade.php ENDPATH**/ ?>