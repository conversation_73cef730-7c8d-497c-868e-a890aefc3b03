import 'dart:async';
import 'package:bond_core/bond_core.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zod/core/utils/timezone_utils.dart';
import 'package:zod/features/auction/data/api.dart';

final auctionStartTimerProvider = StateNotifierProvider.family<
    AuctionStartTimerNotifier, AuctionStartTimerState, AuctionStartTimerParams>(
  (ref, params) => AuctionStartTimerNotifier(params.targetTime, params.auctionId),
);

// Define a record type for the auction start timer parameters
typedef AuctionStartTimerParams = ({DateTime targetTime, int auctionId});

class AuctionStartTimerState {
  final Duration remainingTime;
  final bool isVisible;
  final bool hasTriggeredStartEvent;

  AuctionStartTimerState({
    required this.remainingTime,
    required this.isVisible,
    this.hasTriggeredStartEvent = false,
  });

  AuctionStartTimerState copyWith({
    Duration? remainingTime,
    bool? isVisible,
    bool? hasTriggeredStartEvent,
  }) {
    return AuctionStartTimerState(
      remainingTime: remainingTime ?? this.remainingTime,
      isVisible: isVisible ?? this.isVisible,
      hasTriggeredStartEvent: hasTriggeredStartEvent ?? this.hasTriggeredStartEvent,
    );
  }

  bool get isTimerEnded =>
      remainingTime.isNegative ||
      remainingTime.inSeconds == 0 ||
      hasTriggeredStartEvent;

  String get formatDuration {
    if (remainingTime.isNegative) return "00:00:00";
    final hours = remainingTime.inHours.toString().padLeft(2, '0');
    final minutes = (remainingTime.inMinutes % 60).toString().padLeft(2, '0');
    final seconds = (remainingTime.inSeconds % 60).toString().padLeft(2, '0');
    if (hours == "00") return "00:$minutes:$seconds";
    return " $hours:$minutes:$seconds";
  }
}

class AuctionStartTimerNotifier extends StateNotifier<AuctionStartTimerState> {
  late Timer _timer;
  final DateTime _targetTime;
  final int _auctionId;

  AuctionStartTimerNotifier(this._targetTime, this._auctionId)
      : super(AuctionStartTimerState(
          remainingTime: const Duration(),
          isVisible: true,
          hasTriggeredStartEvent: false,
        )) {
    _startTimer();
  }

  void _startTimer() {
    final now = TimezoneUtils.getCurrentLocalTime();

    // Check if the target time is in the past
    if (_targetTime.isBefore(now)) {
      state = state.copyWith(
        remainingTime: const Duration(),
        isVisible: false,
        hasTriggeredStartEvent: true,
      );

      // If the timer has already ended, trigger the start event
      _triggerAuctionStarted(_auctionId);
      return;
    }

    final remaining = _targetTime.difference(now);
    // Keep the timer visible when there are minutes or seconds remaining
    final isVisible = remaining.inSeconds > 0;

    // Check if the timer has already ended
    final hasEnded = remaining.isNegative || remaining.inSeconds == 0;

    state = AuctionStartTimerState(
      remainingTime: remaining,
      isVisible: isVisible,
      hasTriggeredStartEvent: hasEnded,
    );

    if (isVisible) {
      _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
        final now = TimezoneUtils.getCurrentLocalTime();
        final remaining = _targetTime.difference(now);

        if (remaining.isNegative || remaining.inSeconds == 0) {
          _timer.cancel();

          // If the timer has ended and we haven't triggered the start event yet, trigger it now
          if (!state.hasTriggeredStartEvent) {
            _triggerAuctionStarted(_auctionId);

            // Update state to mark that we've triggered the start event
            state = state.copyWith(
                remainingTime: const Duration(),
                isVisible: false,
                hasTriggeredStartEvent: true);
          } else {
            // Just update the timer state without triggering the event again
            state = state.copyWith(
                remainingTime: const Duration(), isVisible: false);
          }
        } else {
          state = state.copyWith(remainingTime: remaining);
        }
      });
    } else if (hasEnded && !state.hasTriggeredStartEvent) {
      // If the timer has already ended when initializing and we haven't triggered the event yet, trigger it now
      _triggerAuctionStarted(_auctionId);

      // Update state to mark that we've triggered the start event
      state = state.copyWith(
          remainingTime: const Duration(),
          isVisible: false,
          hasTriggeredStartEvent: true);
    }
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  /// Trigger the auction started event when the timer finishes
  Future<void> _triggerAuctionStarted(int auctionId) async {
    try {
      final api = sl<AuctionApi>();
      await api.triggerAuctionStarted(auctionId);
    } catch (e) {
      // Error handled silently
    }
  }

  String formatDuration(Duration duration) {
    if (duration.isNegative) return "00:00:00";
    final hours = duration.inHours.toString().padLeft(2, '0');
    final minutes = (duration.inMinutes % 60).toString().padLeft(2, '0');
    final seconds = (duration.inSeconds % 60).toString().padLeft(2, '0');
    return "$hours:$minutes:$seconds";
  }
}
