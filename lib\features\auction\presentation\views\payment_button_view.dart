import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/core/app_widgets.dart';
import 'package:zod/features/auction/data/models/claim_prize.dart';
import 'package:zod/features/auction/presentation/providers/claim_prize_provider.dart';
import 'package:zod/features/auction/presentation/providers/timer_provider.dart';
import 'package:zod/features/home/<USER>/models/address.dart';

import '../../../../app/routes.dart';
import '../../../payment/presentation/views/payment_method_selection_view.dart';
import "../../../wallet/data/models/wallet_charge_dto.dart";

class PaymentButtonView extends ConsumerWidget {
  final int auctionId;
  final ClaimPrize claimPrize;
  final Address? selectedAddress;
  final CountdownTimerState timerState;

  const PaymentButtonView({
    super.key,
    required this.auctionId,
    required this.claimPrize,
    required this.timerState,
    this.selectedAddress,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Check if the timer has ended (remaining time is zero or negative)
    final bool isTimerEnded = timerState.isTimerEnded || !timerState.isVisible;
    log("bottom is Timer ended ${!isTimerEnded}");
    log("bottom is Timer ended address ${selectedAddress != null}");
    log("bottom Timer remainingTime ${timerState.remainingTime}");

    // Watch the claim prize state to get payment processing status
    final claimPrizeState = ref.watch(claimPrizeProvider(auctionId));
    final isProcessingPayment = claimPrizeState.isProcessingPayment;

    // Button is enabled only if:
    // 1. An address is selected
    // 2. The timer has not ended
    final bool isButtonEnabled = selectedAddress != null && !isTimerEnded;

    return SizedBox(
      width: double.infinity,
      child: AppButton(
        title: context.localizations.apple_pay,
        onPressed: () {
          if (!isProcessingPayment &&
              selectedAddress != null &&
              !isTimerEnded) {
            // For claim prize payments:
            // - Use remainingToPay as base amount (backend will add tax)
            // - Include tax details for display purposes
            // - Points should be 0 since this is not a point purchase
            WalletChargeDto walletChargeDto = WalletChargeDto(
              claimPrize.remainingToPay, // Base amount without tax
              0, // No points being purchased for prize claim
              auctionId: auctionId,
              showOrderSummary: false,
              vatAmount: claimPrize.taxAmount, // For display in payment summary
              totalAmount:
                  claimPrize.totalToPay, // For display in payment summary
            );

            log("PaymentButtonView: Creating WalletChargeDto for prize claim");
            log("  - Base amount (remainingToPay): ${claimPrize.remainingToPay}");
            log("  - Points: 0 (prize claim, not point purchase)");
            log("  - VAT amount (for display): ${claimPrize.taxAmount}");
            log("  - Total amount (for display): ${claimPrize.totalToPay}");
            log("  - Backend will calculate payment amounts correctly");

            goRouter.push(PaymentMethodSelectionView.route,
                extra: walletChargeDto);

            // Process payment using the StateNotifier
            // ref.read(claimPrizeProvider(auctionId).notifier).processPayment();
          }
        },
        loading: isProcessingPayment,
        enabled: isButtonEnabled,
      ),
    );
  }
}
