import 'dart:developer';
import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:zod/core/app_theme.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/core/resources/app_icons.dart';
import 'package:zod/features/payment/data/api.dart';
import 'package:zod/features/payment/data/models/saved_card.dart';
import 'package:zod/features/payment/presentation/providers/payment_provider_new.dart';
import 'package:zod/features/wallet/data/models/wallet_charge_dto.dart';

final savedCardsProvider =
    FutureProvider.autoDispose<List<SavedCard>>((ref) async {
  final paymentApi = sl<PaymentApi>();
  final response = await paymentApi.getSavedCards();

  return response.data;
});

class SavedCardsView extends ConsumerWidget {
  final WalletChargeDto chargeDto;

  const SavedCardsView({super.key, required this.chargeDto});

  static const String route = '/saved_cards';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final savedCardsAsync = ref.watch(savedCardsProvider);

    return savedCardsAsync.when(
      data: (cards) {
        if (cards.isEmpty) {
          return SizedBox();
        }

        return Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              context.localizations.saved_cards,
              style: context.textTheme.bodyLarge?.w700,
            ),
            SizedBox(height: 8),
            ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 8),
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: cards.length,
              itemBuilder: (context, index) {
                final card = cards[index];
                return _buildCardItem(context, ref, card);
              },
            ),
          ],
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) {
        log('Error loading saved cards: $error', name: 'SavedCardsView');
        return Center(
          child: Text(
            'Error loading saved cards', // TODO: Add this to localization files
            style: context.textTheme.bodyLarge,
          ),
        );
      },
    );
  }

  Widget _buildCardItem(BuildContext context, WidgetRef ref, SavedCard card) {
    final cardTypeIcon = _getCardTypeIcon(card.cardScheme);
    final paymentState = ref.watch(paymentProvider);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: paymentState.isLoading
            ? null
            : () => _processPaymentWithCard(context, ref, card),
        child: Row(
          children: [
            cardTypeIcon,
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Directionality(
                    // Use LTR direction for card numbers regardless of app language
                    textDirection: TextDirection.ltr,
                    child: Text(
                      card.cardMask ?? 'Card',
                      style: context.textTheme.titleMedium,
                    ),
                  ),
                  if (card.cardScheme != null)
                    Directionality(
                      // Use LTR direction for card scheme regardless of app language
                      textDirection: TextDirection.ltr,
                      child: Text(
                        card.cardScheme!,
                        style: context.textTheme.bodySmall,
                      ),
                    ),
                  // if (card.isDefault)
                  //   Container(
                  //     margin: const EdgeInsets.only(top: 4),
                  //     padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  //     decoration: BoxDecoration(
                  //       color: AppColors.primaryColor.withOpacity(0.1),
                  //       borderRadius: BorderRadius.circular(4),
                  //     ),
                  //     child: T,
                  //   ),
                ],
              ),
            ),
            if (paymentState.isLoading)
              const SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(strokeWidth: 2),
              )
            else
              Icon(Icons.arrow_forward_ios, size: 16),
          ],
        ),
      ),
    );
  }

  Widget _getCardTypeIcon(String? cardType) {
    Widget iconData;
    switch (cardType?.toLowerCase()) {
      case 'visa':
        iconData = SvgPicture.asset(AppIcons.visa, height: 20);

        break;
      case 'mastercard':
        iconData = SvgPicture.asset(AppIcons.masterCard, height: 20);

        break;

      default:
        iconData = SvgPicture.asset(AppIcons.visa, height: 20);
    }

    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
      ),
      child: iconData,
    );
  }

  void _processPaymentWithCard(
      BuildContext context, WidgetRef ref, SavedCard card) {
    if (card.cardToken == null ||
        card.cardMask == null ||
        card.cardType == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'This card cannot be used for payment. Please use another card.', // TODO: Add this to localization files
          ),
        ),
      );
      return;
    }

    // Use the new 3D secure tokenized card payment method that only requires CVV
    ref.read(paymentProvider.notifier).process3DSecureTokenizedCardPayment(
          context: context,
          chargeDto: chargeDto,
          cardToken: card.cardToken!,
          cardMask: card.cardMask!,
          cardType: card.cardType!,
        );
  }
}
