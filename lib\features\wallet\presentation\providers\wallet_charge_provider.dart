import 'dart:developer';

import 'package:bond_core/bond_core.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zod/app/routes.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/features/payment/presentation/views/payment_method_selection_view.dart';
import 'package:zod/features/wallet/api.dart';
import 'package:zod/features/wallet/data/models/suggested_points.dart';
import 'package:zod/features/wallet/data/models/wallet_charge_dto.dart';
import 'package:zod/features/wallet/presentation/providers/states/charge_wallet_state.dart';

final walletChargeProvider =
    StateNotifierProvider<WalletChargeProvider, ChargeWalletState>(
  (ref) {
    return WalletChargeProvider(
      walletApi: sl<WalletApi>(),
    );
  },
);

class WalletChargeProvider extends StateNotifier<ChargeWalletState> {
  final WalletApi walletApi;
  SuggestedPoints? _suggestedPoints;

  WalletChargeProvider({required this.walletApi})
      : super(ChargeWalletState(
          points: 100, // Default value
          fee: 0,
          increment: 50, // Default increment
          minChargePoint: 50, // Minimum charge amount is 50
        ));

  Future<void> loadSuggestedPoints() async {
    try {
      final result = await walletApi.suggestedPoints();
      _suggestedPoints = result.data;

      // Find default option or use the first one
      final defaultOption = _suggestedPoints!.options.firstWhere(
        (option) => option.isDefault,
        orElse: () => _suggestedPoints!.options.first,
      );

      state = state.copyWith(
        points: defaultOption.points,
        increment: 50, // Default increment for adjusting points
        minChargePoint: _suggestedPoints?.minPoints ?? 50,
        error: "",
      );
    } catch (e) {
      state = state.copyWith(
        error: e.toString(),
      );
    }
  }

  void increasePoints() {
    state = state.copyWith(points: state.points + state.increment, error: "");
    if (state.points > state.max) {
      state = state.copyWith(
        error: appKey.currentContext!.localizations
            .maximum_purchase_points(state.max),
      );
    }
  }

  void setPoints(int point) {
    state = state.copyWith(points: point, error: "");
    if (point < state.min) {
      state = state.copyWith(
        error: appKey.currentContext!.localizations
            .minimum_purchase_points(state.min),
      );
    }
    if (point > state.max) {
      state = state.copyWith(
        error: appKey.currentContext!.localizations
            .maximum_purchase_points(state.max),
      );
    }
  }

  void decreasePoints() {
    // If decrementing would go below the minimum charge point, show error
    if ((state.points - state.increment) < state.minChargePoint) {
      state = state.copyWith(
        error: appKey.currentContext!.localizations
            .minimum_purchase_points(state.minChargePoint),
      );
      return;
    }

    state = state.copyWith(points: state.points - state.increment, error: "");
  }

  void chargeWallet() async {
    // Calculate tax for display purposes (15% VAT)
    final basePrice = state.price.toDouble();
    final vatAmount = basePrice * 0.15;
    final totalAmount = basePrice + vatAmount;

    // Create wallet charge DTO
    // - Use base price (backend will add tax)
    // - Include tax details for display in payment summary
    WalletChargeDto walletChargeDto = WalletChargeDto(
      state.price, // Base amount without tax
      state.points, // Points being purchased
      vatAmount: vatAmount, // For display in payment summary
      totalAmount: totalAmount, // For display in payment summary
    );

    log("WalletChargeProvider chargeWallet - Payment type: Regular Point Purchase");
    log("WalletChargeProvider chargeWallet - Base price: ${state.price}");
    log("WalletChargeProvider chargeWallet - Points: ${state.points}");
    log("WalletChargeProvider chargeWallet - VAT amount (for display): $vatAmount");
    log("WalletChargeProvider chargeWallet - Total amount (for display): $totalAmount");
    log("WalletChargeProvider chargeWallet - Backend will calculate actual payment amounts");
    log("WalletChargeProvider chargeWallet walletChargeDto: ${walletChargeDto.toJson()}");

    goRouter.push(PaymentMethodSelectionView.route, extra: walletChargeDto);
  }
}
