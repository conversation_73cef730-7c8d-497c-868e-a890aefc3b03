import 'dart:async';

import 'package:bond_core/bond_core.dart';
import 'package:bond_network/bond_network.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zod/app/routes.dart';
import 'package:zod/features/auction/presentation/providers/open_auctions_provider.dart';
import 'package:zod/features/auction/presentation/providers/switch_joined_open_auction_provider.dart';
import 'package:zod/features/auth/auth.dart';

import 'package:zod/features/auth/data/api.dart';
import 'package:zod/features/home/<USER>/providers/coming_auctions_provider.dart';
import 'package:zod/features/home/<USER>/providers/winner_auctions_provider.dart';

import 'me_provider.dart';

final logoutProvider =
    AsyncNotifierProvider.autoDispose<LogoutController, SuccessResponse?>(
  () => LogoutController(sl()),
);

class LogoutController extends AutoDisposeAsyncNotifier<SuccessResponse?> {
  final AuthApi _authApi;

  LogoutController(this._authApi);

  @override
  Future<SuccessResponse?> build() => Future.value(null);

  Future<void> logout() async {
    state = const AsyncLoading();
    try {
      state = await AsyncValue.guard(() {
        return _authApi.logout().then((value) async {
          await Auth.clear();
          return value;
        });
      });
    } catch (e) {
      await Auth.clear();
    }
    try {
      ref.invalidate(meProvider);
      ref.invalidate(upComingAuctionsProvider);
      ref.invalidate(winnerAuctionsProvider);
      ref.invalidate(openAuctionsProvider(''));
      ref.read(showSwitchProvider.notifier).state = true;
    } catch (_) {}

    goRouter.go('/home');
  }
}
