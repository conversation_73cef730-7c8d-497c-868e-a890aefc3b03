import 'dart:developer';
import 'dart:ui';

import 'package:bond_cache/bond_cache.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zod/core/analytics/events/events.dart';
import 'package:zod/core/app_analytics.dart';

class LocalNotifier extends Notifier<Locale> {
  @override
  Locale build() {
    // Default to Arabic if no language is set
    const defaultLocale = Locale('ar');

    try {
      // Get the cached language value with a default
      final String cachedLanguage = Cache.get(
        'language',
        defaultValue: defaultLocale.languageCode,
      );

      log('Retrieved language from cache: $cachedLanguage',
          name: 'LocalNotifier');

      // Create locale based on the cached language
      if (cachedLanguage == 'en') {
        return const Locale('en');
      } else if (cachedLanguage == 'ar') {
        return defaultLocale;
      } else {
        // For any unexpected value, use default and update cache
        log('Unknown language in cache: $cachedLanguage, using default: ar',
            name: 'LocalNotifier');
        Cache.put('language', defaultLocale.languageCode);
        return defaultLocale;
      }
    } catch (e) {
      // If any error occurs, fallback to Arabic
      log('Error reading language from cache: $e', name: 'LocalNotifier');
      Cache.put('language', defaultLocale.languageCode);
      return defaultLocale;
    }
  }

  void update(Locale locale) {
    try {
      // Store the language code as a string
      final languageCode = locale.languageCode;
      Cache.put('language', languageCode);

      // Update the state
      state = locale;

      // Track app language event
      AppAnalytics.fire(AppLanguageEvent(language: languageCode));
      log('Language updated and saved to cache: $languageCode',
          name: 'LocalNotifier');
    } catch (e) {
      log('Error updating language: $e', name: 'LocalNotifier');
      // Still update the state even if cache fails
      state = locale;
    }
  }
}
