import 'package:bond_core/bond_core.dart';
import 'package:bond_form/bond_form.dart';
import 'package:bond_form_riverpod/bond_form_riverpod.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/features/auction/data/api.dart';
import 'package:zod/features/auction/data/models/extra_time_reason.dart';
import 'package:zod/features/auction/presentation/providers/extra_time_reason_provider.dart';
import 'package:zod/features/auth/data/errors/validation_error.dart';

class ExtraTimeFormController
    extends AutoDisposeFamilyFormStateNotifier<String, ValidationError, int> {
  final AuctionApi _api;
  ExtraTimeFormController(this._api) : super();

  int get auctionId => arg;

  @override
  Map<String, FormFieldState> fields() {
    return {
      'description': TextFieldState(
        '',
        label: appContext.localizations.describe_issue_optional,
      ),
      'issue_reason': AsyncDropDownFieldState<ExtraTimeReason?>(
        null,
        label: appContext.localizations.choose_a_reason,
        items: ref.read(extraTimeReasonProvider.future),
      ),
      'hours': AsyncDropDownFieldState<int?>(
        null,
        label: appContext.localizations.select_hours,
        items: ref.read(extraTimeHoursProvider.future),
      ),
    };
  }

  @override
  Future<String> onSubmit() async {
    final description = state.textFieldValue('description');
    final issueReason =
        state.asyncDropDownValue<ExtraTimeReason?>('issue_reason')?.name;
    final requestedHours = state.asyncDropDownValue<int?>('hours');

    final body = {
      'description': description,
      'issue_reason': issueReason,
      'requested_hours': requestedHours,
      'auction_id': auctionId,
    };

    final response = await _api.requestExtraTimeReason(body);
    return response['message'];
  }
}

final extraTimeFormProvider = NotifierProvider.autoDispose.family<
    ExtraTimeFormController, BondFormState<String, ValidationError>, int>(() {
  return ExtraTimeFormController(sl());
});
