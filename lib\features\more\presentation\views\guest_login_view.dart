import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:zod/app/routes.dart';
import 'package:zod/core/localizations/app_localizations_extension.dart';
import 'package:zod/core/resources/app_colors.dart';
import 'package:zod/core/resources/app_icons.dart';
import 'package:zod/core/widgets/app_button.dart';

class GuestLoginView extends StatelessWidget {
  const GuestLoginView({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
          color: AppColors.lavenderMist,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: AppColors.softPeriwinkle)),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SvgPicture.asset(AppIcons.zodAvatar),
              SizedBox(width: 8),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    context.localizations.my_account,
                    style: context.textTheme.labelMedium,
                  ),
                  Text(
                    context.localizations.log_in_to_start_bidding,
                    style: context.textTheme.bodySmall,
                  ),
                ],
              ),
            ],
          ),
          SizedBox(width: 8),
          SizedBox(
            width: 90,
            child: AppButton(
              style: context.textTheme.labelLarge
                  ?.copyWith(color: AppColors.softWhite),
              title: context.localizations.login_page_login_button,
              onPressed: () {
                goRouterBackToAfterLogin = '/more';
                goRouter.push('/login');
              },
            ),
          ),
        ],
      ),
    );
  }
}
