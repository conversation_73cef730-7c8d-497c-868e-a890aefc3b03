import 'package:bond_app_analytics/bond_app_analytics.dart';

class PaymentInitiatedEvent extends AnalyticsEvent {
  final double amount;
  final String currency;
  final String paymentMethod;
  final int pointsPurchased;
  final String? auctionId;
  final String? auctionName;
  final String paymentType; // point_purchase, entry_fee, bid_place, etc.
  final int userId;

  PaymentInitiatedEvent({
    required this.amount,
    required this.currency,
    required this.paymentMethod,
    required this.pointsPurchased,
    this.auctionId,
    this.auctionName,
    required this.paymentType,
    required this.userId,
  });

  @override
  String get key => 'payment_initiated';

  @override
  Map<String, dynamic> get params => {
        'amount': amount,
        'currency': currency,
        'payment_method': paymentMethod,
        'payment_type': paymentType,
        'points_to_purchase': pointsPurchased,
        'auction_id': auctionId,
        'auction_name': auctionName,
        'user_id': userId,
        'timestamp': DateTime.now().toIso8601String(),
      };
}
