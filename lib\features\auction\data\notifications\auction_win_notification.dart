import 'dart:convert';

import 'package:bond_core/bond_core.dart';
import 'package:bond_notifications/bond_notifications.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zod/app/routes.dart';
import 'package:zod/features/auction/presentation/providers/auction_provider.dart';
import 'package:zod/features/auth/presentation/providers/me_provider.dart';
import 'package:zod/features/home/<USER>/providers/winner_auctions_provider.dart';

import '../../../home/<USER>/providers/coming_auctions_provider.dart';

class AuctionWinPushNotification extends PushNotification
    with ActionablePushNotification {
  AuctionWinPushNotification();

  @override
  List<String> get code => ['auction_win'];
  @override
  void onNotification(NotificationData data) {
    final parsedData = jsonDecode(data['data'].toString());
    final auctionId = parsedData['auction_id'];
    final id = int.tryParse(auctionId.toString());
    final ref = ProviderScope.containerOf(appContext);
    if (id != null) {
      ref.invalidate(auctionProvider(id));
      ref.invalidate(upComingAuctionsProvider);
      ref.invalidate(winnerAuctionsProvider);
      ref.invalidate(meProvider);
    }
  }

  @override
  void onNotificationTapped(NotificationData data) {
    if (data.containsKey("auction_id")) {
      final auctionId = data["auction_id"];
      goRouter.push('/auction/$auctionId');
      return;
    }
    final parsedData = jsonDecode(data['data'].toString());
    final auctionId = parsedData['auction_id'];
    final id = int.tryParse(auctionId.toString());
    goRouter.push('/auction/$id');
  }
}
