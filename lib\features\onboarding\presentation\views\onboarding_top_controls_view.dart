import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:zod/core/localizations/app_localizations_extension.dart';
import 'package:zod/core/resources/app_colors.dart';
import 'package:zod/core/resources/app_icons.dart';

class OnboardingTopControlsView extends ConsumerWidget {
  final VoidCallback onSkip;
  final Locale locale;

  const OnboardingTopControlsView({
    super.key,
    required this.onSkip,
    required this.locale,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Positioned(
      top: MediaQuery.of(context).viewPadding.top + 24,
      left: 17,
      right: 17,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Language selector (hidden)
          Visibility(
            visible: false,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 4),
              decoration: BoxDecoration(
                color: AppColors.white,
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: AppColors.lightGray,
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    locale.languageCode == 'ar'
                        ? context.localizations.arabic
                        : context.localizations.english,
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(width: 8),
                  SvgPicture.asset(AppIcons.globe),
                ],
              ),
            ),
          ),

          // Skip button
          GestureDetector(
            onTap: onSkip,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  context.localizations.skip,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                SizedBox(width: 8),
                Transform.flip(
                  flipX: Directionality.of(context) == TextDirection.rtl,
                  child: SvgPicture.asset(AppIcons.arrowSkip),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
