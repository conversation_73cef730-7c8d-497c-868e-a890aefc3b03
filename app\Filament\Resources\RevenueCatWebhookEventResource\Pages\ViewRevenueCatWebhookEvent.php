<?php

namespace App\Filament\Resources\RevenueCatWebhookEventResource\Pages;

use App\Filament\Resources\RevenueCatWebhookEventResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewRevenueCatWebhookEvent extends ViewRecord
{
    protected static string $resource = RevenueCatWebhookEventResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make()
                ->visible(false), // Disable editing
        ];
    }
} 