import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';
import 'package:zod/core/app_theme.dart';

import '../../features/auth/auth.dart';
import '../resources/app_colors.dart';
import 'network_image_widget.dart';

class PersonalView extends StatelessWidget {
  const PersonalView({super.key});

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: Auth.check(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              NetworkImageView(
                Auth.user().image ?? '',
                borderRadius: BorderRadius.circular(28),
                width: 56,
                height: 56,
              ),
              <PERSON><PERSON><PERSON><PERSON>(width: 9),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(Auth.user().name ?? '',
                      style: context.textTheme.titleSmall?.w700),
                  <PERSON><PERSON><PERSON><PERSON>(height: 4),
                  Directionality(
                    textDirection: TextDirection.ltr,
                    child: Text(Auth.user().fullPhone,
                        style: context.textTheme.bodyMedium),
                  ),
                ],
              ),
            ],
          ),
          Divider(color: AppColors.lightGray, thickness: 1)
        ],
      ),
    );
  }
}
