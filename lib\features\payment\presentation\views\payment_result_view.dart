import 'dart:developer';

import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:zod/app/routes.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/core/app_theme.dart';
import 'package:zod/core/resources/app_colors.dart';
import 'package:zod/core/resources/app_icons.dart';
import 'package:zod/core/widgets/app_button.dart';
import 'package:zod/core/widgets/title_app_bar.dart';
import 'package:zod/features/payment/data/models/payment_details.dart';

class PaymentResultView extends StatelessWidget {
  final PaymentDetails paymentDetails;

  const PaymentResultView({super.key, required this.paymentDetails});

  static const String route = '/payment_result';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: TitleAppBar(
        title: '',
        showImageProfile: false,
        hideBack: true,
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Column(
                  children: [
                    const SizedBox(height: 20),
                    // Result icon and message
                    _buildResultHeader(context),
                    const SizedBox(height: 24),
                    // Payment details
                    _buildPaymentDetails(context),
                  ],
                ),
              ),
            ),
          ),

          const SizedBox(height: 32),
          // Action button
          _buildActionButton(context),
          const SizedBox(height: 80),
        ],
      ),
    );
  }

  Widget _buildResultHeader(BuildContext context) {
    log(paymentDetails.errorMessage ?? '', stackTrace: StackTrace.current);
    return Column(
      children: [
        // Success or failure icon
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
          ),
          child: SvgPicture.asset(
            paymentDetails.isSuccess
                ? AppIcons.paymentSuccess
                : AppIcons.paymentFailed,
          ),
        ),
        const SizedBox(height: 16),
        // Result title
        Text(
          paymentDetails.isSuccess
              ? context.localizations.payment_success
              : context.localizations.payment_failed,
          style: context.textTheme.titleLarge?.w700,
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),
        // Result description
        Text(
          paymentDetails.isSuccess
              ? context.localizations.payment_success_description
              : paymentDetails.errorMessage ??
                  context.localizations.payment_failed_description,
          style: context.textTheme.bodyMedium,
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildPaymentDetails(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
          // color: Colors.white,
          // borderRadius: BorderRadius.circular(12),
          // boxShadow: [
          //   BoxShadow(
          //     color: Colors.black.withOpacity(0.05),
          //     blurRadius: 10,
          //     offset: const Offset(0, 2),
          //   ),
          // ],
          ),
      child: Column(
        children: [
          if (paymentDetails.isSuccess) _buildAmountSection(context),
          _buildDetailItem(
            context,
            title: context.localizations.payment_status,
            value: paymentDetails.status,
            isStatus: true,
          ),
          _buildDetailItem(context,
              title: context.localizations.payment_method,
              value: paymentDetails.paymentMethod,
              showApplePay:
                  paymentDetails.paymentMethod.toLowerCase().contains('apple'),
              showCard: true),
          _buildDetailItem(
            context,
            title: context.localizations.date,
            value: paymentDetails.date,
          ),
          if (paymentDetails.isSuccess && paymentDetails.invoiceNumber != null)
            _buildDetailItem(
              context,
              title: context.localizations.invoice_no,
              value: paymentDetails.invoiceNumber!,
            ),

          //view  tax
          if (paymentDetails.isSuccess && paymentDetails.tax != null)
            _buildDetailItem(
              context,
              title: context.localizations.tax15,
              value: paymentDetails.tax!.toString(),
              showTax: true,
            ),
          if (paymentDetails.isSuccess && paymentDetails.vatNumber != null)
            _buildDetailItem(
              context,
              title: context.localizations.vat_no,
              value: paymentDetails.vatNumber!,
            ),
        ],
      ),
    );
  }

  Widget _buildAmountSection(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: AppColors.lightGray,
            width: 1,
            style: BorderStyle.solid,
          ),
        ),
      ),
      child: Row(
        children: [
          Text(
            context.localizations.amount,
            style: context.textTheme.bodyMedium,
          ),
          Spacer(),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (paymentDetails.type != PaymentDetailsType.claim)
                Row(
                  children: [
                    Text(
                      paymentDetails.points.toString(),
                      style: context.textTheme.labelLarge?.medium,
                    ),
                    const SizedBox(width: 4),
                    SvgPicture.asset(AppIcons.coin, height: 15),
                  ],
                ),
              const SizedBox(width: 16),
              Row(
                children: [
                  Text(
                    paymentDetails.amount.toString(),
                    style: context.textTheme.labelLarge?.medium,
                  ),
                  const SizedBox(width: 4),
                  SvgPicture.asset(AppIcons.riyal, height: 15),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDetailItem(
    BuildContext context, {
    required String title,
    required String value,
    bool isStatus = false,
    bool showApplePay = false,
    bool showCard = false,
    bool showTax = false,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: context.textTheme.bodyMedium,
          ),
          if (isStatus)
            _buildStatusBadge(context, value)
          else if (showApplePay)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                border: Border.all(color: AppColors.lightGray),
                borderRadius: BorderRadius.circular(4),
              ),
              child: SvgPicture.asset(
                AppIcons.applePay,
                height: 10,
              ),
            )
          else if (showCard)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                border: Border.all(color: AppColors.lightGray),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Row(
                children: [
                  SvgPicture.asset(
                    AppIcons.visa,
                    height: 10,
                    color: AppColors.darkSteel,
                  ),
                  const SizedBox(width: 4),
                  SvgPicture.asset(
                    AppIcons.masterCard,
                    height: 10,
                  ),
                ],
              ),
            )
          else if (showTax)
            Row(
              children: [
                SvgPicture.asset(AppIcons.riyal, height: 15),
                SizedBox(width: 5),
                Text(
                  value,
                  style: context.textTheme.bodyMedium?.medium,
                ),
              ],
            )
          else
            Text(
              value,
              style: context.textTheme.bodyMedium?.medium,
            ),
        ],
      ),
    );
  }

  Widget _buildStatusBadge(BuildContext context, String status) {
    final isSuccess = status.toLowerCase() == 'paid';

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: isSuccess ? AppColors.lightGreen : AppColors.lightRed,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isSuccess
              ? AppColors.green.withOpacity(0.3)
              : AppColors.red.withOpacity(0.3),
        ),
      ),
      child: Text(
        status,
        style: context.textTheme.labelSmall?.copyWith(
          color: isSuccess ? AppColors.darkGreen : AppColors.darkRed,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildActionButton(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 60),
      child: AppButton(
        title: paymentDetails.isSuccess
            ? context.localizations.back_to_home
            : context.localizations.back_to_payment,
        backgroundColor: AppColors.disabled,
        textColor: AppColors.steelGray,
        borderColor: AppColors.softSteel,
        onPressed: () {
          if (paymentDetails.isSuccess) {
            // Navigate to home
            goRouter.go('/home');
          } else {
            // Go back to payment
            goRouter.pop();
          }
        },
      ),
    );
  }
}
