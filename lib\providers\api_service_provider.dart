import 'package:bond_core/bond_core.dart';
import 'package:bond_network/bond_network.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'package:pretty_dio_logger/pretty_dio_logger.dart';
import 'package:bond_cache/bond_cache.dart';
import 'package:zod/app/routes.dart';

import '../config/configs.dart';
import '../features/auth/auth.dart';

class ApiServiceProvider extends ServiceProvider {
  @override
  Future<void> register(GetIt it) async {
    final baseOptions = BaseOptions(
      connectTimeout: Duration(seconds: config('CONNECT_TIMEOUT')),
      sendTimeout: Duration(seconds: config('SEND_TIMEOUT')),
      receiveTimeout: Duration(seconds: config('RECEIVE_TIMEOUT')),
      receiveDataWhenStatusError: config('RECEIVE_DATA_WHEN_STATUS_ERROR'),
      baseUrl: config('API_BASE_URL'),
    );
    Api.extraHeaders = () {
      return {
        if (Auth.check()) 'Authorization': 'Bearer ${Auth.token()}',
        'Accept-Language': Cache.get('language', defaultValue: 'ar')
      };
    };

    baseOptions.headers = Api.headers();
    final dio = Dio(baseOptions);
    if (kDebugMode) {
      dio.interceptors.add(
        PrettyDioLogger(
            requestHeader: true,
            requestBody: true,
            responseBody: true,
            responseHeader: false,
            error: true,
            compact: true,
            maxWidth: 90),
      );
    }
    // LOGOUT IF  Status: 401
    dio.interceptors.add(InterceptorsWrapper(onError: (e, handler) async {
      if (e.response?.statusCode == 401) {
        await Auth.clear();
        goRouter.go('/home');
      }
      handler.next(e);
    }));
    it.registerLazySingleton(() => dio);
    it.registerLazySingleton(() => BondFire());
    it.registerLazySingleton(() => ApiClient(it()));
  }
}
