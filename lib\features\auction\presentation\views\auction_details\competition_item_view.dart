import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:zod/core/resources/app_icons.dart';

class CompetitionItemView extends StatelessWidget {
  const CompetitionItemView(
      {super.key, required this.title, this.value, this.child});
  final String title;
  final String? value;
  final Widget? child;
  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Flexible(
          flex: 3,
          child: Text(title, style: context.textTheme.labelLarge),
        ),
        SizedBox(width: 65),
        Flexible(
          child: Row(
            children: [
              SvgPicture.asset(AppIcons.coin),
              SizedBox(width: 3),
              child ?? Text(value ?? "", style: context.textTheme.labelLarge),
            ],
          ),
        ),
      ],
    );
  }
}
