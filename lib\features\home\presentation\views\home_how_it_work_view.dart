import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/core/app_theme.dart';

import '../providers/how_it_work_provider.dart';
import 'home_how_it_work_shimmer.dart';
import 'how_it_work_item_view.dart';

class HomeHowItWorkView extends ConsumerWidget {
  const HomeHowItWorkView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final howItWorksState = ref.watch(howItWorkProvider);
    return howItWorksState.when(
        data: (howItWorks) {
          final works = howItWorks;

          return works.isEmpty
              ? SizedBox()
              : Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: 32),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 16),
                      child: Text(
                        context.localizations.how_it_works,
                        style: context.textTheme.titleSmall?.w700,
                      ),
                    ),
                    <PERSON><PERSON><PERSON><PERSON>(height: 14),
                    Column(
                      children: List.generate(
                        works.length,
                        (index) {
                          final work = works[index];
                          return HowItWorkItemView(
                            work: work,
                            paddingBottom: index == works.length - 1 ? 4 : 17,
                          );
                        },
                      ),
                    )
                  ],
                );
        },
        error: (e, s) => Center(child: Text(e.toString())),
        loading: () => HomeHowItWorkShimmer());
  }
}
