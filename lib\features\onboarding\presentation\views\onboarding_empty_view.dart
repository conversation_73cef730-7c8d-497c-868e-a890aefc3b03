import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zod/core/localizations/app_localizations_extension.dart';
import 'package:zod/core/resources/app_colors.dart';

class OnboardingEmptyView extends ConsumerWidget {
  final VoidCallback onSkip;

  const OnboardingEmptyView({
    super.key,
    required this.onSkip,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      backgroundColor: AppColors.white,
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.info_outline,
                size: 64,
                color: AppColors.lightSlateGray,
              ),
              const SizedBox(height: 16),
              Text(
                context.localizations.no_onboarding_content,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textColor,
                ),
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: onSkip,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryColor,
                  foregroundColor: AppColors.white,
                ),
                child: Text(context.localizations.continue_to_app),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
