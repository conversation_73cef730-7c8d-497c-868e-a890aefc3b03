import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:zod/core/resources/app_colors.dart';
import 'package:zod/core/resources/app_icons.dart';

class HomeMaroofView extends StatelessWidget {
  const HomeMaroofView({super.key});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _launchURL('https://maroof.sa/businesses/details/360805'),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // First divider
            Flexible(
              child: Divider(
                thickness: 1,
                endIndent: 12,
                indent: 30,
                color: AppColors.lightGray,
              ),
            ),
            // ZodHome icon
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 12),
              child: SvgPicture.asset(
                AppIcons.zodHome,
              ),
            ),
            // Second divider
            Flexible(
              child: Divider(
                thickness: 1,
                endIndent: 30,
                indent: 12,
                color: AppColors.lightGray,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _launchURL(String url) async {
    final Uri uri = Uri.parse(url);
    if (!await launchUrl(uri, mode: LaunchMode.externalApplication)) {
      throw Exception('Could not launch $url');
    }
  }
}
