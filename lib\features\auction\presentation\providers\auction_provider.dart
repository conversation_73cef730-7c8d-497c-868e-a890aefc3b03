// ignore_for_file: prefer_const_constructors_in_immutables

import 'dart:async';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:get_it/get_it.dart';
import 'package:zod/app/errors/server_error.dart';
import 'package:zod/app/routes.dart';
import 'package:zod/core/analytics/events/interest_event.dart';
import 'package:zod/core/app_analytics.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/core/notifications/notification_alert_view.dart';
import 'package:zod/features/auction/data/api.dart';
import 'package:zod/features/auction/data/auction_websocket_service.dart';
import 'package:zod/features/auction/data/events/reminder_toggle_events.dart';
import 'package:zod/features/auction/presentation/providers/auction_last_hour_provider.dart';
import 'package:zod/features/auction/presentation/providers/hurry_up_visibility_provider.dart';
import 'package:zod/features/auction/data/states/auction_state.dart';
import 'package:zod/features/auth/auth.dart';
import 'package:zod/features/auth/presentation/providers/me_provider.dart';
import 'package:zod/features/home/<USER>/providers/winner_auctions_provider.dart';

// Provider for image index in carousel
final imageIndexProvider = StateProvider<int>((ref) => 0);

// Provider for AuctionApi
final auctionApiProvider = Provider<AuctionApi>((ref) {
  return GetIt.I<AuctionApi>();
});

// Provider for AuctionWebSocketService
final auctionWebSocketServiceProvider =
    Provider<AuctionWebSocketService>((ref) {
  return GetIt.I<AuctionWebSocketService>();
});

StreamController<Map<String, dynamic>> auctionStreamControllerRefresh =
    StreamController<Map<String, dynamic>>.broadcast();

// The merged provider that combines auction details and bid functionality
final auctionProvider = StateNotifierProvider.family
    .autoDispose<AuctionNotifier, AuctionState, int>(
  (ref, auctionId) => AuctionNotifier(
    auctionId,
    ref.watch(auctionApiProvider),
    ref.watch(auctionWebSocketServiceProvider),
    ref,
  ),
);

class AuctionNotifier extends StateNotifier<AuctionState> {
  final int _auctionId;
  final AuctionApi _auctionApi;
  final AuctionWebSocketService _webSocketService;
  final Ref _ref;

  StreamSubscription<AuctionApiJoin>? listenerToBids;
  StreamSubscription<AuctionApiJoin>? listenerToUserJoined;
  StreamSubscription<AuctionApiJoin>? listenerToAuctionStarted;
  StreamSubscription<AuctionApiJoin>? listenerToAuctionEnded;
  StreamSubscription<AuctionApiJoin>? listenerToAuctionLastHour;
  StreamSubscription<Map<String, dynamic>>?
      listenerAuctionStreamControllerRefresh;
  AuctionNotifier(
    this._auctionId,
    this._auctionApi,
    this._webSocketService,
    this._ref,
  ) : super(AuctionState.initial()) {
    initialize();
  }

  Future<void> initialize() async {
    log("Initializing AuctionNotifier for auction $_auctionId");

    // Load auction details
    await loadAuction();

    try {
      // Initialize WebSocket and subscribe to auction events
      await _webSocketService.initialize();
      await subscribeToAuction();

      // Load initial bids
      // await loadBids();
    } catch (e) {
      log('Error initializing auction provider: $e');
    }
  }

  Future<void> loadAuction({loading = true}) async {
    try {
      state = state.copyWith(isLoadingAuction: loading);
      final response = await _auctionApi.auction(_auctionId);
      log("AUCTION .. ${response.data.id}");
      state = state.copyWith(
        auction: response.data,
        auctionMeta: response.meta,
        bidAmount: response.data.suggestionBids?.firstOrNull?.toInt() ?? 0,
        latestBid: response.data.bids?.firstOrNull,
        breakUntil: response.meta.breakUntil ??
            DateTime.now().subtract(const Duration(days: 130)),
        isLoadingAuction: false,
      );

      AppAnalytics.fire(InterestEvent(
          interestType: response.data.category?.name ?? "n/a",
          userId: Auth.check() ? Auth.user().id : null));
      //
    } catch (e, s) {
      log('Error loading auction: $e');
      log('Error loading auction: $s');
      state = state.copyWith(
        isLoadingAuction: false,
        auctionError: e.toString(),
      );
    }
  }

  Future<void> subscribeToAuction() async {
    listenerAuctionStreamControllerRefresh = auctionStreamControllerRefresh
        .stream
        .asBroadcastStream()
        .listen((auct) {
      if (!_webSocketService.isConnected) {
        Future.delayed(Duration(seconds: 1));
        if ((state.auction?.isLive ?? false) ||
            (state.auction?.isUpComing ?? false)) {
          loadAuction(loading: false);
        }
      }
    });
    await _webSocketService.subscribeToAuction(_auctionId);

    // if (state.auction?.isJoined == false) {
    listenerToUserJoined =
        _webSocketService.listenToUserJoined(_auctionId).listen((auct) {
      log('Received join user update: ${auct.data}');
      Future.delayed(Duration(milliseconds: 500));
      loadAuction(loading: false);
    });
    //   return;
    // }

    // Listen for real-time bid updates
    listenerToBids = _webSocketService.listenToBids(_auctionId).listen((auct) {
      log('Received bid update: ${auct.data}');

      if (state.auction?.isLive ?? false) {
        state = state.copyWith(
            auction: auct.data.copyWith(isJoined: state.auction?.isJoined),
            latestBid: auct.data.bids?.firstOrNull,
            auctionMeta: auct.meta);
        state = state.copyWith(
          latestBid: auct.data.bids?.firstOrNull,
        );
      }
      if (auct.data.isClosed ?? false) {
        loadAuction(loading: false);
      }
    });
    listenerToUserJoined =
        _webSocketService.listenToUserJoined(_auctionId).listen((auct) {
      log('Received join user update: ${auct.data}');

      state = state.copyWith(
        auction: auct.data.copyWith(isJoined: state.auction?.isJoined),
        latestBid: auct.data.bids?.firstOrNull,
      );
    });

    // Listen for auction started event
    listenerToAuctionStarted =
        _webSocketService.listenToAuctionStarted(_auctionId).listen((auction) {
      log('Auction ended: ${auction.data.id}');
      state = state.copyWith(
          auction: auction.data.copyWith(isJoined: state.auction?.isJoined),
          auctionMeta: auction.meta);
    });

    // Listen for auction ended event
    listenerToAuctionEnded =
        _webSocketService.listenToAuctionEnded(_auctionId).listen((auction) {
      if (state.auction?.isLive ?? false) {
        _ref.invalidate(winnerAuctionsProvider);
        loadAuction(loading: false);
      }
    });

    // Listen for auction last hour event
    listenerToAuctionLastHour = _webSocketService
        .listenToLastHourTimeAuction(_auctionId)
        .listen((auction) {
      log('PUSHER EVENT - Auction last hour: ${auction.data.id}');
      log('PUSHER EVENT - Auction last hour time: ${auction.data.endedAt}');
      log('PUSHER EVENT - Auction last hour remaining seconds: ${auction.data.endedAt?.difference(DateTime.now()).inSeconds}');
      log('PUSHER EVENT - Auction last hour status: ${auction.data.status}');
      log('PUSHER EVENT - Auction last hour meta: ${auction.meta}');
      log('PUSHER EVENT - Current user joined: ${auction.data.hasCurrentUserJoined}');

      // IMPORTANT: Make sure to preserve the isJoined flag from the current state if it's true
      // This ensures we don't lose the joined status when updating the auction data
      final bool preserveJoinedStatus = state.auction?.isJoined == true;

      // Create an updated auction with the isJoined flag preserved
      final updatedAuction = auction.data.copyWith(
        isJoined: preserveJoinedStatus ? true : auction.data.isJoined,
        // If the auction has participatorIds, preserve them
        participatorIds:
            state.auction?.participatorIds ?? auction.data.participatorIds,
      );

      log('PUSHER EVENT - Updated auction isJoined: ${updatedAuction.isJoined}');
      log('PUSHER EVENT - Updated auction hasCurrentUserJoined: ${updatedAuction.hasCurrentUserJoined}');

      // Update the state with the new auction data
      if (!mounted) return;
      state = state.copyWith(
        auction: updatedAuction,
        auctionMeta: auction.meta,
        latestBid: auction.data.bids?.firstOrNull,
      );

      // IMPORTANT: Set the auctionLastHourProvider to true to indicate that the auction_last_hour event has been received
      // This is critical for showing the hurry up notification
      log('PUSHER EVENT - Setting auctionLastHourProvider to true');
      _ref.read(auctionLastHourProvider(updatedAuction).notifier).state = true;

      // Invalidate the hurryUpVisibilityProvider to force it to re-evaluate
      // This will ensure the hurry up view is shown when the auction enters its last hour
      log('PUSHER EVENT - Invalidating hurryUpVisibilityProvider');
      _ref.invalidate(hurryUpVisibilityProvider(updatedAuction));

      // Force a rebuild of the UI by invalidating the auctionProvider
      log('PUSHER EVENT - Invalidating auctionProvider');
      auctionStreamControllerRefresh.add({'refresh': true});
    });
  }

  // Future<void> loadBids() async {
  //   log("Loading bids for auction $_auctionId");
  //   try {
  //     state = state.copyWith(isLoadingBids: true);
  //     final response = await _auctionApi.getBids(_auctionId);
  //     state = state.copyWith(
  //       bids: response.data,
  //       isLoadingBids: false,
  //       bidMeta: response.meta,
  //     );
  //   } catch (e) {
  //     state = state.copyWith(
  //       isLoadingBids: false,
  //       bidError: e.toString(),
  //     );
  //   }
  // }

  // Future<void> loadMoreBids() async {
  //   if (state.nextBidUrl == null || state.isLoadingMoreBids) return;

  //   try {
  //     state = state.copyWith(isLoadingMoreBids: true);
  //     final response =
  //         await _auctionApi.getBids(_auctionId, nextUrl: state.nextBidUrl);
  //     state = state.copyWith(
  //       bids: [...state.bids, ...response.data],
  //       isLoadingMoreBids: false,
  //       bidMeta: response.meta,
  //     );
  //   } catch (e) {
  //     state = state.copyWith(
  //       isLoadingMoreBids: false,
  //       bidError: e.toString(),
  //     );
  //   }
  // }

  void setBidAmount(int amount) {
    if (!mounted) return;
    state = state.copyWith(bidAmount: amount);
  }

  Future<void> placeBid(ref) async {
    try {
      state = state.copyWith(isPlacingBid: true, bidError: null);

      final response = await _auctionApi.placeBid(_auctionId, state.bidAmount);
      final walletMeta = response.meta;

      state = state.copyWith(
        isPlacingBid: false,
        auctionMeta: walletMeta,
        auction: state.auction?.copyWith(
          isWinner: response.data.isWinner,
          isJoined: true,
          winnerId: response.data.winnerId,
          totalBidPoints: response.data.totalBidPoints,
          bids: response.data.bids,
          status: response.data.status,
          endedAt: response.data.endedAt,
        ),
        breakUntil: walletMeta.breakUntil ??
            DateTime.now().subtract(const Duration(days: 130)),
        latestBid: response.data.bids?.firstOrNull,
      );

      if (walletMeta.breakUntil == null) {
        NotificationAlert.showLocalNotification(
          walletMeta.message ?? "",
          type: ToastType.success,
          align: Alignment.bottomCenter,
        );
      }

      try {
        ref.invalidate(meProvider);
      } catch (_) {}
    } catch (e, s) {
      log(s.toString());

      // Check if the notifier is still mounted before updating state in the catch block
      if (!mounted) return;

      if (e is ServerError) {
        log("Server error encountered");
        log("Error message: ${e.message}");
        log(e.toString());

        state = state.copyWith(
            isPlacingBid: false, bidSuccess: false, breakUntil: e.breakUntil);

        if (e.breakUntil == null) {
          NotificationAlert.showLocalNotification(
            e.message,
            type: ToastType.error,
            align: Alignment.bottomCenter,
          );
        }
      } else {
        loadAuction(loading: false);
        //goRouter.;//
        //get localization text without context

        // AppLocalizations.of(context)!.
        if (goRouter.configuration.navigatorKey.currentContext != null) {
          NotificationAlert.showLocalNotification(
            goRouter.configuration.navigatorKey.currentContext!.localizations
                .connectionLostPleaseTryAgain,
            type: ToastType.error,
            align: Alignment.bottomCenter,
          );
          state = state.copyWith(
            isPlacingBid: false,
            bidSuccess: false,
          );
        } else {
          state = state.copyWith(
            isPlacingBid: false,
            bidError: goRouter.configuration.navigatorKey.currentContext!
                .localizations.connectionLostPleaseTryAgain,
            bidSuccess: false,
          );
        }
      }
    }
  }

  // Clear errors
  void clearErrors() {
    if (!mounted) return;
    state = state.copyWith(
      bidError: "",
      auctionError: "",
    );
  }

  // Refresh auction details
  Future<void> refreshAuction() async {
    if (!mounted) return;
    await loadAuction();
  }

  @override
  void dispose() {
    _webSocketService.unsubscribeFromAuction(_auctionId);
    listenerToBids?.cancel();
    listenerToAuctionStarted?.cancel();
    listenerToAuctionEnded?.cancel();
    listenerToAuctionLastHour?.cancel();
    listenerAuctionStreamControllerRefresh?.cancel();
    listenerToUserJoined?.cancel();
    super.dispose();
  }

  /// Toggle the reminder state
  Future<void> toggleReminder() async {
    state = state.copyWith(isLoadingReminder: true);

    // Set loading state
    // state = const AsyncValue.loading();

    try {
      // Call the API to toggle the reminder
      final response = await _auctionApi.toggleReminder(_auctionId);

      // Update the state with the new reminder status
      var msg = response['message'] ?? "-";

      NotificationAlert.showLocalNotification(
        msg,
        type: ToastType.success,
        align: Alignment.topCenter,
      );

      // Determine the new reminder status
      final newReminderStatus = !(state.auction?.isReminderEnabled ?? false);

      // Track the appropriate analytics event
      if (newReminderStatus) {
        trackNotificationOn(_auctionId, state.auction?.name ?? '');
        log('Notification ON event tracked for auction $_auctionId',
            name: 'AuctionProvider');
      } else {
        trackNotificationOff(_auctionId, state.auction?.name ?? '');
        log('Notification OFF event tracked for auction $_auctionId',
            name: 'AuctionProvider');
      }

      state = state.copyWith(
          isLoadingReminder: false,
          auction:
              state.auction?.copyWith(isReminderEnabled: newReminderStatus));
    } catch (e) {
      state = state.copyWith(isLoadingReminder: false);

      NotificationAlert.showLocalNotification(
        e.toString(),
        type: ToastType.error,
        align: Alignment.topCenter,
      );
      // Set error state
    }
  }
}
