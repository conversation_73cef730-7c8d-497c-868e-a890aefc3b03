import 'package:bond_core/bond_core.dart';
import 'package:bond_form/bond_form.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';

import 'package:zod/core/app_localizations.dart';
import 'package:zod/core/app_theme.dart';
import 'package:zod/core/resources/app_colors.dart';
import 'package:zod/core/resources/app_icons.dart';
import 'package:zod/features/auction/presentation/providers/add_address_form_provider.dart';
import 'package:zod/features/auth/presentation/providers/phone_number_validation_provider.dart';

class AddressFormView extends ConsumerStatefulWidget {
  // Add focusNode parameter
  final FocusNode? focusNode;

  const AddressFormView({super.key, this.focusNode});

  @override
  ConsumerState<AddressFormView> createState() => _AddressFormViewState();
}

class _AddressFormViewState extends ConsumerState<AddressFormView> {
  // Text controllers for form fields
  late TextEditingController addressController;
  late TextEditingController streetController;
  late TextEditingController phoneController;

  @override
  void initState() {
    super.initState();
    // Initialize controllers
    addressController = TextEditingController();
    streetController = TextEditingController();
    phoneController = TextEditingController();
  }

  @override
  void dispose() {
    // Dispose controllers
    addressController.dispose();
    streetController.dispose();
    phoneController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final formState = ref.watch(addAddressFormProvider);
    final formController = ref.read(addAddressFormProvider.notifier);

    // Update controllers when form state changes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final addressValue = formState.textFieldValue('address') ?? '';
      final streetValue = formState.textFieldValue('house_no_street') ?? '';
      final phoneValue = formState.textFieldValue('phone_number') ?? '';

      // Only update if the values are different to avoid infinite loops
      if (addressController.text != addressValue) {
        addressController.text = addressValue;
      }
      if (streetController.text != streetValue) {
        streetController.text = streetValue;
      }
      if (phoneController.text != phoneValue) {
        // Remove the +966 prefix for display
        final displayPhone = phoneValue.startsWith('+966')
            ? phoneValue.substring(4)
            : phoneValue;
        if (phoneController.text != displayPhone) {
          phoneController.text = displayPhone;
        }
      }
    });

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Center(
            child: Container(
              width: 30,
              height: 3,
              decoration: BoxDecoration(
                color: AppColors.black,
                borderRadius: BorderRadius.circular(100),
              ),
            ),
          ),
          const SizedBox(height: 10),
          Text(
            context.localizations.add_address,
            style: context.textTheme.titleSmall?.w700,
          ),
          const SizedBox(height: 12),
          // Address field (Building Name)
          Text(
            formState.label('address'),
            style: context.textTheme.bodySmall,
          ),
          const SizedBox(height: 8),
          TextField(
            controller: addressController,
            // Use the parent's focusNode for the first field
            focusNode: widget.focusNode,
            onChanged: (value) => formController.updateText('address', value),
            // Scroll to top when this field gets focus
            onTap: () {
              if (widget.focusNode != null && !widget.focusNode!.hasFocus) {
                widget.focusNode!.requestFocus();
              }
            },
            decoration: InputDecoration(
              hintText: context.localizations.enter_building_name,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey.shade300),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey.shade300),
              ),
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            ),
          ),
          const SizedBox(height: 12),

          // House No/Street field
          Text(
            context.localizations.street,
            style: context.textTheme.bodySmall,
          ),
          const SizedBox(height: 8),
          TextField(
            controller: streetController,
            onChanged: (value) =>
                formController.updateText('house_no_street', value),
            // Scroll to top when this field gets focus
            onTap: () {
              if (widget.focusNode != null && !widget.focusNode!.hasFocus) {
                widget.focusNode!.requestFocus();
              }
            },
            decoration: InputDecoration(
              hintText: context.localizations.enter_street_name,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey.shade300),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey.shade300),
              ),
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            ),
          ),
          const SizedBox(height: 12),

          // Phone Number field
          Text(
            formState.label('phone_number'),
            style: context.textTheme.bodySmall,
          ),
          const SizedBox(height: 8),
          Directionality(
            textDirection: TextDirection.ltr,
            child: TextField(
              maxLength: 9,
              style: context.textTheme.bodyMedium,
              keyboardType: TextInputType.phone,
              controller: phoneController,
              // Scroll to top when this field gets focus
              onTap: () {
                if (widget.focusNode != null && !widget.focusNode!.hasFocus) {
                  widget.focusNode!.requestFocus();
                }
              },
              decoration: InputDecoration(
                labelStyle: appTextTheme.bodySmall,
                errorText: formState.error('phone_number'),
                hintText: '000 000 000',
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide:
                      const BorderSide(width: 1, color: AppColors.borderColor),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide:
                      const BorderSide(width: 1, color: AppColors.borderColor),
                ),
                hintStyle: context.textTheme.bodyMedium,
                prefixIcon: Padding(
                  padding: const EdgeInsetsDirectional.only(start: 16, end: 8),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      SvgPicture.asset(AppIcons.saFlag),
                      const SizedBox(width: 4),
                      Text('+966', style: context.textTheme.bodyMedium),
                    ],
                  ),
                ),
                prefixIconConstraints:
                    const BoxConstraints(minWidth: 0, minHeight: 0),
              ),
              onChanged: (value) {
                ref.read(phoneNumberValidationProvider.notifier).state = false;

                formController.updateText('phone_number', '+966$value');

                if (value.length == 9) {
                  ref.read(phoneNumberValidationProvider.notifier).state = true;
                }
              },
            ),
          ),
          const SizedBox(height: 30),
        ],
      ),
    );
  }
}
