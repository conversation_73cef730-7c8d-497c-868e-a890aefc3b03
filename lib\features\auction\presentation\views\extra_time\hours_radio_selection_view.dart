import 'package:bond_form/bond_form.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/core/resources/app_colors.dart';

class HoursRadioSelectionView extends ConsumerWidget {
  final AsyncValue<List<DropDownItemState<int>>> hoursItems;
  final int? selectedValue;
  final Function(int?) onChanged;

  const HoursRadioSelectionView({
    super.key,
    required this.hoursItems,
    required this.selectedValue,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return hoursItems.when(
      data: (items) {
        // Calculate the number of items per row based on screen width
        final screenWidth = MediaQuery.of(context).size.width;
        final itemWidth = 112.0; // Width of each item
        final horizontalPadding = 32.0; // Left and right padding (16 each side)
        final availableWidth = screenWidth - horizontalPadding;
        final itemsPerRow = (availableWidth / itemWidth).floor();
        final spacing =
            (availableWidth - (itemWidth * itemsPerRow)) / (itemsPerRow - 1);

        return GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            crossAxisSpacing: spacing,
            mainAxisSpacing: spacing,
            mainAxisExtent: 42, // Fixed height of 46 pixels
          ),
          itemCount: items.length,
          itemBuilder: (context, index) {
            final item = items[index];
            final isSelected = selectedValue == item.value;
            return GestureDetector(
              onTap: () => onChanged(item.value),
              child: Container(
                decoration: BoxDecoration(
                  color: isSelected ? AppColors.lavenderMist : Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: isSelected
                        ? AppColors.lavenderMist
                        : AppColors.lightGray,
                    width: isSelected ? 2 : 1,
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text('${item.value}',
                        style: Theme.of(context).textTheme.bodySmall),
                    const SizedBox(width: 4),
                    Text(
                        item.value > 1
                            ? context.localizations.hours
                            : context.localizations.hour,
                        style: Theme.of(context).textTheme.bodySmall),
                  ],
                ),
              ),
            );
          },
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Text('Error loading hours: $error'),
    );
  }
}
