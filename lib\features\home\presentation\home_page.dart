import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zod/core/resources/app_assets.dart';
import 'package:zod/features/auth/presentation/providers/me_provider.dart';
import 'package:zod/features/home/<USER>/views/home_coming_auctions_view.dart';
import 'package:zod/features/home/<USER>/views/home_how_it_work_view.dart';
import 'package:zod/features/home/<USER>/views/home_maroof_view.dart';
import 'package:zod/features/home/<USER>/views/home_winner_auctions_view.dart';
import 'package:zod/features/home/<USER>/views/home_winner_review_auctions_view.dart';

import 'views/home_user_header_view.dart';

class HomePage extends ConsumerStatefulWidget {
  const HomePage({super.key});

  static const String route = '/home';

  @override
  ConsumerState<HomePage> createState() => _HomePageState();
}

class _HomePageState extends ConsumerState<HomePage> {
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final statusBarHeight = MediaQuery.of(context).padding.top;
    ref.watch(meProvider);
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: SystemUiOverlayStyle.light.copyWith(
          statusBarColor: Color.fromRGBO(239, 240, 255, 255),
          statusBarBrightness: Brightness.light,
          statusBarIconBrightness: Brightness.dark),
      child: Scaffold(
        extendBodyBehindAppBar: true,
        body: Stack(
          children: [
            _ParallaxBackground(
              scrollController: _scrollController,
            ),
            SingleChildScrollView(
              controller: _scrollController,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: statusBarHeight),
                  SizedBox(height: 19),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 16),
                        child: HomeUserHeaderView(),
                      ),
                      SizedBox(height: 32),
                      HomeComingAuctionsView(),
                      HomeWinnerAuctionsView(),
                      HomeWinnerReviewAuctionsView(),
                      HomeHowItWorkView(),
                      HomeMaroofView(),
                      SizedBox(height: 16),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _ParallaxBackground extends StatelessWidget {
  final ScrollController scrollController;

  const _ParallaxBackground({required this.scrollController});

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: scrollController,
      builder: (context, child) {
        final offset =
            scrollController.hasClients ? scrollController.offset * 0.6 : 0.0;
        return Transform.translate(
          offset: Offset(0, -offset),
          child: Image.asset(
            AppImagesAssets.homePattern,
            fit: BoxFit.cover,
            width: double.infinity,
            height: 301,
          ),
        );
      },
    );
  }
}
