import 'dart:developer' as dev;
import 'package:intl/intl.dart';
import 'package:bond_cache/bond_cache.dart';
import 'package:zod/features/auction/data/models/auction.dart';

/// Extension to provide localized date formatting for Auction objects
extension AuctionDateFormatter on Auction {
  /// Returns the start date formatted according to the current locale
  /// Example: "Monday, January 15" in English or "الاثنين، ١٥ يناير" in Arabic
  String getLocalizedStartDate() {
    if (startAt == null) {
      return "";
    }

    try {
      // Get the current locale from cache
      final locale = Cache.get<String>('language', defaultValue: 'ar');

      // Create a DateFormat with the appropriate locale and pattern
      DateFormat dateFormat;
      if (locale == 'ar') {
        // For Arabic, use a format that works well with Arabic script
        dateFormat = DateFormat('EEEE، d MMMM', locale);
      } else {
        dateFormat = DateFormat('EEEE, MMMM d', locale);
      }

      // Format the date
      final formattedDate = dateFormat.format(startAt!);
      return formattedDate;
    } catch (e) {
      // If parsing fails, log the error and return an empty string
      dev.log('Error formatting date: $e', name: 'AuctionDateFormatter');
      return "";
    }
  }
}
