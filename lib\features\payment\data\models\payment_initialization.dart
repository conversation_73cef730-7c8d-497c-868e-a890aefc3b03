import 'package:equatable/equatable.dart';

class PaymentInitialization extends Equatable {
  final bool success;
  final String transactionId;
  final Map<String, dynamic> paymentData;
  final double? totalToPay;
  final String? tax;

  const PaymentInitialization({
    required this.success,
    required this.transactionId,
    required this.paymentData,
    this.totalToPay,
    this.tax,
  });

  factory PaymentInitialization.fromJson(Map<String, dynamic> json) {
    return PaymentInitialization(
      success: json['success'] ?? false,
      transactionId: json['transaction_id'] ?? '',
      paymentData: json['payment_data'] ?? {},
      totalToPay: double.tryParse("${json['total_to_pay']}"),
      tax: "${json['tax']}",
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'transaction_id': transactionId,
      'payment_data': paymentData,
      'total_to_pay': totalToPay,
      'tax': tax,
    };
  }

  @override
  List<Object?> get props => [success, transactionId, paymentData];
}
