import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../widgets/onboarding_screen_view.dart';
import '../../data/models/onboarding.dart';

class OnboardingImageView extends ConsumerWidget {
  final PageController pageController;
  final List<Onboarding> screens;
  final Function(int) onPageChanged;

  const OnboardingImageView({
    super.key,
    required this.pageController,
    required this.screens,
    required this.onPageChanged,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      height: MediaQuery.of(context).size.height * 0.7,
      child: PageView.builder(
        controller: pageController,
        onPageChanged: onPageChanged,
        itemCount: screens.length,
        itemBuilder: (context, index) {
          return OnboardingScreenView(screen: screens[index]);
        },
      ),
    );
  }
}
