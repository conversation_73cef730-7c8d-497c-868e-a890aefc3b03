import 'package:bond_core/bond_core.dart';
import 'package:go_router/go_router.dart';
import 'package:zod/features/auth/routes.dart';
import 'package:zod/features/invoice/routes.dart';
import 'package:zod/features/main/routes.dart';
import 'package:zod/features/notification/routes.dart';
import 'package:zod/features/onboarding/routes.dart';
import 'package:zod/features/payment/routes.dart';
import 'package:zod/features/update_app/routes.dart';

import '../features/auction/routes.dart';
import '../features/more/routes.dart';
import '../features/wallet/routes.dart';

final goRouter = GoRouter(
  debugLogDiagnostics: true,
  initialLocation: '/',
  navigatorKey: appKey,
  routes: [
    ...mainRoutes,
    ...onboardingRoutes,
    ...moreRoutes,
    ...auctionRoutes,
    ...authRoutes,
    ...updateAppRoutes,
    ...notificationRoutes,
    ...walletRoutes,
    ...paymentRoutes,
    ...invoiceRoutes,
  ],
);

String goRouterBackToAfterLogin = '';
