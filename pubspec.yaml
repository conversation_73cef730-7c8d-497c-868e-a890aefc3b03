name: zod
description: Zod project.
publish_to: 'none'
version: 1.0.21+49

environment:
  sdk: ">=3.3.0 <4.0.0"

dependencies:
  flutter:
    sdk: flutter

  flutter_localizations:
    sdk: flutter


  # Bond packages
  bond_core: ^0.0.3
  bond_cache: ^0.0.4+1
  bond_network: ^0.0.6
  bond_form: ^0.0.4+1
  bond_form_riverpod: ^0.0.2+7
  bond_app_analytics: ^0.1.0
  bond_notifications: ^0.1.0


  get_it: ^8.0.3
  open_store: ^0.5.0


  # network
  dio: ^5.8.0+1
  pretty_dio_logger: ^1.4.0

  # serialization
  json_serializable: ^6.9.3
  json_annotation: ^4.9.0

  #ui
  flutter_svg: ^2.0.17
  google_fonts: ^6.2.1
  oktoast: ^3.4.0
  skeletonizer: ^1.4.3
  carousel_slider: ^5.0.0
  super_tooltip: ^2.0.9
  google_maps_flutter: ^2.5.3
  location: ^5.0.3
  permission_handler: ^11.0.1
  geocoding: ^2.1.1
  custom_info_window: ^1.0.1

  # state management
  flutter_riverpod: ^2.6.1
  equatable: ^2.0.7

  # navigation
  go_router: ^14.8.0

  # utils
  flutter_native_splash: ^2.4.4
  device_info_plus: ^11.3.0
  faker: ^2.2.0
  flutter_html: ^3.0.0
  html: ^0.15.0
  shimmer: ^3.0.0
  pinput:
    git:
      url: https://github.com/obeidareda37/Flutter_Pinput.git
      ref: master
  simple_shadow: ^0.3.1
  # platforms packages
  universal_platform: ^1.1.0
  shared_preferences: ^2.5.2
  flutter_secure_storage: ^9.2.4
  package_info_plus: ^8.2.1
  flutter_local_notifications: ^19.1.0
  in_app_review: ^2.0.10
  share_plus: ^10.1.4
  cached_network_image: ^3.4.1
  cached_network_svg_image: ^1.2.0

  # Firebase
  firebase_analytics: ^11.4.2
  firebase_core: ^3.11.0
  firebase_messaging: ^15.2.2
  firebase_crashlytics: ^4.3.4
  firebase_remote_config: ^5.4.0

  # WebSockets
  pusher_channels_flutter:

  # Payment
  flutter_paytabs_bridge: ^2.7.0

  # PDF Viewer
  flutter_pdfview: ^1.3.2
  path_provider: ^2.1.2
  http: ^1.2.1

  # localization
  intl: ^0.19.0
  timeago: ^3.7.0
  url_launcher: ^6.3.1
  popover: ^0.3.1

  # Meta SDK
  flutter_meta_sdk: ^1.0.5

  # UXCam
  flutter_uxcam: ^2.6.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  build_runner: ^2.4.14
  flutter_launcher_icons: ^0.14.3

flutter:
  uses-material-design: true
  generate: true

  assets:
    - assets/icons/
    - assets/images/