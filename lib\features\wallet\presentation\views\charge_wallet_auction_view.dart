import 'dart:developer';

import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/core/app_theme.dart';
import 'package:zod/core/app_widgets.dart';
import 'package:zod/core/resources/app_colors.dart';
import 'package:zod/core/resources/app_icons.dart';
import 'package:zod/features/app/app_providers.dart';
import 'package:zod/features/auth/auth.dart';
import 'package:zod/features/wallet/data/models/charge_wallet_auction.dart';

import '../providers/charge_wallet_provider.dart';
import '../providers/points_per_price_provider.dart';

class ChargeWalletViewAuction extends ConsumerWidget {
  final ChargeWalletAuction chargeWalletAuction;

  ChargeWalletViewAuction({
    super.key,
    required this.chargeWalletAuction,
  });

  static const String route = '/charge_wallet/:id/:fee/:increment';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    log("ChargeWalletViewAuction chargeWalletAuction: ${chargeWalletAuction.auction?.toJson()}");

    final walletState = ref.watch(chargeWalletProvider(chargeWalletAuction));

    final wallet = ref.watch(pointsPerPriceProvider);

    return SingleChildScrollView(
      child: wallet.when(
        data: (data) {
          return Container(
            padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom),
            color: AppColors.white,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 1.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // SizedBox(height: 10),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Column(
                      children: [
                        if (chargeWalletAuction.auction?.isJoined ?? false)
                          Text.rich(
                            TextSpan(
                              text: context.localizations.yourCurrentBalance,
                              style: context.textTheme.titleSmall!.medium,
                              children: <InlineSpan>[
                                TextSpan(
                                  text: " ",
                                ),
                                WidgetSpan(
                                    child: Transform.translate(
                                  offset: Offset(0, -5),
                                  child: SvgPicture.asset(
                                    AppIcons.coin,
                                    alignment: Alignment.topCenter,
                                  ),
                                )),
                                TextSpan(
                                  text: " ${Auth.user().availablePoint}",
                                  style: context.textTheme.titleSmall!.w700,
                                ),
                              ],
                            ),
                          ),
                        if ((chargeWalletAuction.auction?.isNotJoined ?? false))
                          Text(context.localizations.buy_points,
                              style: context.textTheme.titleMedium!),
                        if ((chargeWalletAuction.auction?.isNotJoined ?? false))
                          SizedBox(height: 5),
                        if ((chargeWalletAuction.auction?.isNotJoined ?? false))
                          Text(
                              context.localizations
                                  .the_fee_is_non_refundable_but_you_can,
                              textAlign: TextAlign.center,
                              style: context.textTheme.labelMedium!),
                        SizedBox(height: 20),
                      ],
                    ),
                  ),
                  Container(
                    color: AppColors.softWhite,
                    padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        ...(chargeWalletAuction.auction?.suggestionPoints ?? [])
                            .map((s) => Expanded(
                                child: GestureDetector(
                                    behavior: HitTestBehavior.translucent,
                                    onTap: () => ref
                                        .read(chargeWalletProvider(
                                                chargeWalletAuction)
                                            .notifier)
                                        .setPoints(s.toInt()),
                                    child: _pointOption(context, s.toInt(),
                                        isFlash: walletState.flashPoints ==
                                            s.toInt(),
                                        highlight:
                                            walletState.points == s.toInt())))),
                      ],
                    ),
                  ),
                  SizedBox(height: 20),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      IconButton(
                        //(walletState.error ?? "").isEmpty ||
                        icon:
                            ((walletState.points > walletState.minChargePoint))
                                ? SvgPicture.asset(AppIcons.walletMinus)
                                : SvgPicture.asset(AppIcons.walletMinusDis),
                        onPressed: ((walletState.points >
                                walletState
                                    .minChargePoint)) // ((walletState.error ?? "").isEmpty)
                            ? ref
                                .read(chargeWalletProvider(chargeWalletAuction)
                                    .notifier)
                                .decreasePoints
                            : null,
                      ),
                      // Expanded(
                      //   child: Container(
                      //     padding: EdgeInsets.symmetric(
                      //         horizontal: 30, vertical: 13),
                      //     decoration: BoxDecoration(
                      //       borderRadius: BorderRadius.circular(10),
                      //       border: Border.all(
                      //         color: (walletState.error ?? "").isEmpty
                      //             ? AppColors.lightGray
                      //             : AppColors.crimsonRed,
                      //         width: 1.5,
                      //       ),
                      //     ),
                      //     alignment: Alignment.center,
                      //     child: Row(
                      //       mainAxisAlignment: MainAxisAlignment.center,
                      //       children: [
                      //         SvgPicture.asset(AppIcons.coin),
                      //         Text(
                      //           " ${walletState.points}",
                      //           style: context.textTheme.bodyMedium!.w700,
                      //         ),
                      //       ],
                      //     ),
                      //   ),
                      // ),

                      Expanded(
                        child: Container(
                          // padding: EdgeInsets.symmetric(
                          //     horizontal: 30, vertical: 13),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10),
                            // border: Border.all(
                            //   color: (walletState.error ?? "").isEmpty
                            //       ? AppColors.lightGray
                            //       : AppColors.crimsonRed,
                            //   width: 1.5,
                            // ),
                          ),
                          alignment: Alignment.center,
                          child: TextFormField(
                            // initialValue: walletState.points.toString(),
                            maxLength: 6,
                            controller: ref
                                .read(chargeWalletProvider(chargeWalletAuction)
                                    .notifier)
                                .pointsTextController,
                            textAlign: TextAlign.center,
                            style: context.textTheme.bodyMedium!.w700,
                            decoration: InputDecoration(
                              counter: SizedBox(),
                              prefixIcon: Transform.translate(
                                offset: Offset(
                                    ref
                                            .read(localProvider)
                                            .languageCode
                                            .toLowerCase()
                                            .contains("en")
                                        ? 20
                                        : -20,
                                    0),
                                child: SvgPicture.asset(
                                  AppIcons.coin,
                                  alignment: Alignment.topCenter,
                                ),
                              ),
                              prefixIconConstraints: BoxConstraints(
                                minWidth: 20,
                                minHeight: 20,
                              ),

                              labelStyle: context.textTheme.bodyMedium!.w700,
                              //text style
                              hintText: context.localizations.points,
                              hintStyle: context.textTheme.bodyMedium!.w700
                                  .copyWith(color: AppColors.lightSlateGray),

                              filled: true,
                              fillColor: AppColors.white,
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(10),
                                borderSide: BorderSide(
                                  color: (walletState.error ?? "").isEmpty
                                      ? AppColors.lightGray
                                      : AppColors.crimsonRed,
                                  width: 1.5,
                                ),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(10),
                                borderSide: BorderSide(
                                  color: (walletState.error ?? "").isEmpty
                                      ? AppColors.lightGray
                                      : AppColors.crimsonRed,
                                  width: 1.5,
                                ),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(10),
                                borderSide: BorderSide(
                                  color: (walletState.error ?? "").isEmpty
                                      ? AppColors.lightGray
                                      : AppColors.crimsonRed,
                                  width: 1.5,
                                ),
                              ),
                            ),
                            keyboardType: TextInputType.number,
                            onChanged: (value) {
                              ref
                                  .read(
                                      chargeWalletProvider(chargeWalletAuction)
                                          .notifier)
                                  .setPoints(int.parse(value));
                            },
                          ),
                        ),
                      ),

                      IconButton(
                        icon: ((walletState.points < walletState.max))
                            ? SvgPicture.asset(AppIcons.walletPlus)
                            : SvgPicture.asset(AppIcons.walletPlusDis),
                        onPressed: ((walletState.points < walletState.max))
                            ? ref
                                .read(chargeWalletProvider(chargeWalletAuction)
                                    .notifier)
                                .increasePoints
                            : null,
                      ),
                    ],
                  ),
                  if (walletState.error?.isNotEmpty ?? false)
                    Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: Row(
                          children: [
                            SvgPicture.asset(AppIcons.wrong),
                            SizedBox(
                              width: 8,
                            ),
                            Expanded(
                              child: Text(walletState.error ?? "",
                                  style: context.textTheme.labelMedium!
                                      .copyWith(color: AppColors.crimsonRed)),
                            ),
                          ],
                        )),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Column(
                      children: [
                        SizedBox(height: 20),
                        AppButton(
                          title: "",
                          loading: walletState.isLoading,
                          customChild: Text.rich(TextSpan(
                            text: context.localizations.buy_pointsfor,
                            style: !(walletState.error?.isEmpty ?? true)
                                ? context.textTheme.labelLarge
                                    ?.copyWith(color: AppColors.lightSlateGray)
                                : context.textTheme.labelLarge!.white,
                            children: <InlineSpan>[
                              WidgetSpan(
                                  child: Transform.translate(
                                      offset: Offset(0, -5),
                                      child: SvgPicture.asset(
                                        AppIcons.riyal,
                                        color: AppColors.white,
                                      ))),
                              if ((walletState.error ?? "").isEmpty)
                                TextSpan(
                                  text: " ${walletState.price} ",
                                ),
                            ],
                          )),
                          onPressed: () => ref
                              .read(chargeWalletProvider(chargeWalletAuction)
                                  .notifier)
                              .chargeWallet(ref),
                          enabled: (walletState.error ?? "").isEmpty,
                        ),
                        SizedBox(height: 10),
                        Container(
                          padding: EdgeInsets.all(10),
                          decoration: BoxDecoration(
                            color: Colors.yellow[100],
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              if (chargeWalletAuction.auction?.isJoined ==
                                  false)
                                SvgPicture.asset(AppIcons.coin),
                              SizedBox(width: 5), //
                              Text(
                                chargeWalletAuction.auction?.isJoined == true
                                    ? walletState.participationTextJoind
                                    : walletState.bidText,
                                style: context.textTheme.labelMedium,
                              ),
                            ],
                          ),
                        ),
                        SizedBox(height: 10),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        },
        error: (e, s) {
          log("error ${e.toString()}", stackTrace: s);
          return Center(child: Text(e.toString()));
        },
        loading: () => Center(child: CircularProgressIndicator()),
      ),
    );
  }

  Widget _pointOption(BuildContext context, int points,
      {bool highlight = false, isFlash = false}) {
    return Stack(
      // fit: StackFit.expand,
      clipBehavior: Clip.none,
      children: [
        Container(
          margin: EdgeInsets.symmetric(horizontal: 4),
          padding: EdgeInsets.symmetric(horizontal: 20, vertical: 10),
          decoration: BoxDecoration(
            color: highlight ? AppColors.periwinkle : AppColors.lavenderMist,
            borderRadius: BorderRadius.circular(10),
            border: Border.all(
              color: highlight ? AppColors.primaryColor : AppColors.periwinkle,
              width: 1,
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SvgPicture.asset(AppIcons.coin),
              SizedBox(width: 5), //steelGray
              Text(
                "$points",
                style: context.textTheme.labelLarge!
                    .copyWith(color: AppColors.steelGray),
                // style: TextStyle(fontWeight: FontWeight.bold)
              ),
            ],
          ),
        ),
        if (isFlash)
          Positioned.directional(
            textDirection: Directionality.of(context),
            child: SvgPicture.asset(
              AppIcons.flash,
              width: 15,
              // height: 10,
            ),
            end: 20,
            top: -5,
          )
      ],
    );
  }
}
