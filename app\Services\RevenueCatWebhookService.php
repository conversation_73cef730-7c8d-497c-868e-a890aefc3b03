<?php

namespace App\Services;

use Illuminate\Http\Request;

use App\Enums\{
    PaymentStatus,
    SubscriptionPlan,
    SubscriptionStatus
};
use App\Models\{
    PaymentHistory,
    RevenueCatWebhookEvent,
    Subscription,
    User
};
use Illuminate\Support\Facades\{
    DB,
    Log
};

class RevenueCatWebhookService
{
    /**
     * Verify webhook signature from RevenueCat
     *
     * @param Request $request
     * @return bool
     */
    public function verifyWebhookSignature(Request $request): bool
    {
        // Allow manual triggers from dashboard to bypass signature verification
        if ($request->hasHeader('X-Manual-Trigger')) {
            return true;
        }

        // $secret = config('services.revenuecat.webhook_secret');
        // if (empty($secret)) {
        //     Log::warning('RevenueCat webhook secret not configured');
        //     return false;
        // }

        // $authHeader = $request->header('Authorization');
        // if ($authHeader !== 'Bearer ' . $secret) { // Ensure the Authorization header is valid
        //     Log::warning('Invalid RevenueCat webhook Authorization header', ['header' => $authHeader]);
        //     return false;
        // }

        return true;
    }

    /**
     * Process a webhook event
     *
     * @param RevenueCatWebhookEvent $webhookEvent
     * @return array
     */
    public function processWebhookEvent(RevenueCatWebhookEvent $webhookEvent): array
    {
        try {
            $payload = $webhookEvent->webhook_payload;
            $event = $payload['event'] ?? [];
            $eventType = $event['type'] ?? '';

            // Find or create user
            $user = $this->findOrCreateUser($event);
            if (!$user) {
                return [
                    'success' => false,
                    'error' => 'Could not find or create user'
                ];
            }

            // Update webhook event with user
            $webhookEvent->update(['user_id' => $user->id]);

            // Process based on event type
            return match ($eventType) {
                'INITIAL_PURCHASE' => $this->handleInitialPurchase($user, $event),
                'RENEWAL' => $this->handleRenewal($user, $event),
                'CANCELLATION' => $this->handleCancellation($user, $event),
                'EXPIRATION' => $this->handleExpiration($user, $event),
                'BILLING_ISSUE' => $this->handleBillingIssue($user, $event),
                'PRODUCT_CHANGE' => $this->handleProductChange($user, $event),
                default => [
                    'success' => false,
                    'error' => "Unsupported event type: {$eventType}"
                ]
            };

        } catch (\Exception $e) {
            Log::error('Error processing RevenueCat webhook event', [
                'webhook_event_id' => $webhookEvent->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Find or create user from RevenueCat event
     *
     * @param array $event
     * @return User|null
     */
    private function findOrCreateUser(array $event): ?User
    {
        $appUserId = $event['app_user_id'] ?? null;

        if (empty($appUserId)) {
            Log::warning('RevenueCat webhook event missing app_user_id', ['event' => $event]);
            return null;
        }
        
        // Try to find user by RevenueCat app_user_id
        // You may need to adjust this logic based on how you store RevenueCat user IDs
        // Option 1: If app_user_id is the actual user ID
        if (is_numeric($appUserId)) {
            $user = User::find($appUserId);
            if ($user) {
                return $user;
            }
        }
        
        // Option 2: If you store RevenueCat app_user_id in a separate field
        // $user = User::where('revenuecat_app_user_id', $appUserId)->first();
        // if ($user) {
        //     return $user;
        // }
        
        // Option 3: Look for existing subscription with this app_user_id
        $subscription = Subscription::where('revenuecat_customer_id', $appUserId)->first();
        if ($subscription && $subscription->user) {
            return $subscription->user;
        }
        
        Log::warning('Could not find user for RevenueCat app_user_id', [
            'app_user_id' => $appUserId,
            'event_type' => $event['type'] ?? 'Unknown'
        ]);
        
        return null;
    }

    /**
     * Handle initial purchase event
     *
     * @param User $user
     * @param array $event
     * @return array
     */
    private function handleInitialPurchase(User $user, array $event): array
    {
        return DB::transaction(function () use ($user, $event) {
            $subscription = $this->createOrUpdateSubscription($user, $event, SubscriptionStatus::ACTIVE);
            
            Log::info('RevenueCat initial purchase processed', [
                'user_id' => $user->id,
                'subscription_id' => $subscription->id,
                'product_id' => $event['product_id'] ?? null,
            ]);

            return [
                'success' => true,
                'subscription_id' => $subscription->id
            ];
        });
    }

    /**
     * Handle renewal event
     *
     * @param User $user
     * @param array $event
     * @return array
     */
    private function handleRenewal(User $user, array $event): array
    {
        return DB::transaction(function () use ($user, $event) {
            $subscription = $this->createOrUpdateSubscription($user, $event, SubscriptionStatus::ACTIVE);
            
            Log::info('RevenueCat renewal processed', [
                'user_id' => $user->id,
                'subscription_id' => $subscription->id,
                'product_id' => $event['product_id'] ?? null,
            ]);

            return [
                'success' => true,
                'subscription_id' => $subscription->id
            ];
        });
    }

    /**
     * Handle cancellation event
     *
     * @param User $user
     * @param array $event
     * @return array
     */
    private function handleCancellation(User $user, array $event): array
    {
        return DB::transaction(function () use ($user, $event) {
            $subscription = $this->findExistingSubscription($user, $event);

            if ($subscription) {
                $subscription->update([
                    'status' => SubscriptionStatus::CANCELED,
                    'cancellation_date' => now(),
                    'auto_renewal' => false,
                ]);

                Log::info('RevenueCat cancellation processed', [
                    'user_id' => $user->id,
                    'subscription_id' => $subscription->id,
                ]);

                return [
                    'success' => true,
                    'subscription_id' => $subscription->id
                ];
            }

            return [
                'success' => false,
                'error' => 'Subscription not found for cancellation'
            ];
        });
    }

    /**
     * Handle expiration event
     *
     * @param User $user
     * @param array $event
     * @return array
     */
    private function handleExpiration(User $user, array $event): array
    {
        return DB::transaction(function () use ($user, $event) {
            $subscription = $this->findExistingSubscription($user, $event);

            if ($subscription) {
                $subscription->update([
                    'status' => SubscriptionStatus::EXPIRED,
                    'auto_renewal' => false,
                ]);

                Log::info('RevenueCat expiration processed', [
                    'user_id' => $user->id,
                    'subscription_id' => $subscription->id,
                ]);

                return [
                    'success' => true,
                    'subscription_id' => $subscription->id
                ];
            }

            return [
                'success' => false,
                'error' => 'Subscription not found for expiration'
            ];
        });
    }

    /**
     * Handle billing issue event
     *
     * @param User $user
     * @param array $event
     * @return array
     */
    private function handleBillingIssue(User $user, array $event): array
    {
        return DB::transaction(function () use ($user, $event) {
            $subscription = $this->findExistingSubscription($user, $event);

            if ($subscription) {
                // Keep subscription active but log the billing issue
                // RevenueCat will handle retries and eventual expiration
                Log::warning('RevenueCat billing issue', [
                    'user_id' => $user->id,
                    'subscription_id' => $subscription->id,
                    'product_id' => $event['product_id'] ?? null,
                ]);

                return [
                    'success' => true,
                    'subscription_id' => $subscription->id
                ];
            }

            return [
                'success' => false,
                'error' => 'Subscription not found for billing issue'
            ];
        });
    }

    /**
     * Handle product change event
     *
     * @param User $user
     * @param array $event
     * @return array
     */
    private function handleProductChange(User $user, array $event): array
    {
        return DB::transaction(function () use ($user, $event) {
            $subscription = $this->createOrUpdateSubscription($user, $event, SubscriptionStatus::ACTIVE);

            Log::info('RevenueCat product change processed', [
                'user_id' => $user->id,
                'subscription_id' => $subscription->id,
                'new_product_id' => $event['product_id'] ?? null,
            ]);

            return [
                'success' => true,
                'subscription_id' => $subscription->id
            ];
        });
    }

    /**
     * Create or update subscription from RevenueCat event
     *
     * @param User $user
     * @param array $event
     * @param SubscriptionStatus $status
     * @return Subscription
     */
    private function createOrUpdateSubscription(User $user, array $event, SubscriptionStatus $status): Subscription
    {
        $appUserId = $event['app_user_id'] ?? null;
        $productId = $event['product_id'] ?? null;
        $entitlementId = $event['entitlement_id'] ?? null;

        // Map RevenueCat product ID to our subscription plan
        $plan = $this->mapProductIdToPlan($productId);

        // Try to find existing subscription by RevenueCat app_user_id
        $subscription = Subscription::where('user_id', $user->id)
            ->where('revenuecat_customer_id', $appUserId)
            ->first();

        $subscriptionData = [
            // Basic subscription fields
            'user_id' => $user->id,
            'plan' => $plan,
            'status' => $status,
            'start_date' => now(),
            'end_date' => isset($event['expiration_at_ms'])
                ? \Carbon\Carbon::createFromTimestampMs($event['expiration_at_ms'])
                : now()->addMonth(),
            'purchase_date' => isset($event['purchased_at_ms'])
                ? \Carbon\Carbon::createFromTimestampMs($event['purchased_at_ms'])
                : now(),
            'auto_renewal' => !($event['is_trial_period'] ?? false),
            'points' => $this->getPointsForPlan($plan),
            
            // Platform fields (mapped from RevenueCat data)
            'platform_type' => $this->mapRevenueCatStoreToPlatform($event['store'] ?? null),
            'product_id' => $productId, // Store the actual product_id from RevenueCat
            'transaction_id' => $event['transaction_id'] ?? null,
            'original_transaction_id' => $event['original_transaction_id'] ?? null,

            // Existing RevenueCat specific fields
            'revenuecat_customer_id' => (string) $appUserId,
            'revenuecat_original_app_user_id' => $event['original_app_user_id'] ?? $appUserId,
            'revenuecat_entitlement_id' => $entitlementId,
            'revenuecat_product_identifier' => $productId,
            'revenuecat_purchased_at' => isset($event['purchased_at_ms'])
                ? \Carbon\Carbon::createFromTimestampMs($event['purchased_at_ms'])
                : now(),
            'revenuecat_expires_at' => isset($event['expiration_at_ms'])
                ? \Carbon\Carbon::createFromTimestampMs($event['expiration_at_ms'])
                : now()->addMonth(),
            'revenuecat_store' => $event['store'] ?? null,
            'revenuecat_environment' => $event['environment'] ?? null,
            'revenuecat_is_trial_period' => $event['is_trial_period'] ?? false,
            'revenuecat_webhook_data' => $event,

            // New comprehensive RevenueCat fields from event
            'revenuecat_event_timestamp_ms' => $event['event_timestamp_ms'] ?? null,
            'revenuecat_period_type' => $event['period_type'] ?? null,
            'revenuecat_entitlement_ids' => $event['entitlement_ids'] ?? null,
            'revenuecat_presented_offering_id' => $event['presented_offering_id'] ?? null,
            'revenuecat_is_family_share' => $event['is_family_share'] ?? false,
            'revenuecat_country_code' => $event['country_code'] ?? null,
            'revenuecat_aliases' => $event['aliases'] ?? null,
            'revenuecat_currency' => $event['currency'] ?? null,
            'revenuecat_is_trial_conversion' => $event['is_trial_conversion'] ?? false,
            'revenuecat_price' => $event['price'] ?? null,
            'revenuecat_price_in_purchased_currency' => $event['price_in_purchased_currency'] ?? null,
            'revenuecat_subscriber_attributes' => $event['subscriber_attributes'] ?? null,
            'revenuecat_takehome_percentage' => $event['takehome_percentage'] ?? null,
            'revenuecat_offer_code' => $event['offer_code'] ?? null,
            'revenuecat_tax_percentage' => $event['tax_percentage'] ?? null,
            'revenuecat_commission_percentage' => $event['commission_percentage'] ?? null,
            'revenuecat_metadata' => $event['metadata'] ?? null,
            'revenuecat_renewal_number' => $event['renewal_number'] ?? null,
            'revenuecat_event_type' => $event['type'] ?? null,
            'revenuecat_event_id' => $event['id'] ?? null,
            'revenuecat_app_id' => $event['app_id'] ?? null,
        ];

        if ($subscription) {
            $subscription->update($subscriptionData);
        } else {
            $subscription = Subscription::create($subscriptionData);
        }

        // Create payment history record for purchase events
        if (in_array($status, [SubscriptionStatus::ACTIVE])) {
            $this->createPaymentHistoryRecord($user, $subscription, $event);
        }

        return $subscription;
    }

    /**
     * Find existing subscription for user and event
     *
     * @param User $user
     * @param array $event
     * @return Subscription|null
     */
    private function findExistingSubscription(User $user, array $event): ?Subscription
    {
        $appUserId = $event['app_user_id'] ?? null;

        // First try to find by RevenueCat customer ID (app_user_id)
        if ($appUserId) {
            $subscription = Subscription::where('user_id', $user->id)
                ->where('revenuecat_customer_id', $appUserId)
                ->latest()
                ->first();

            if ($subscription) {
                return $subscription;
            }
        }

        // Fallback to finding by original app user ID
        $originalAppUserId = $event['original_app_user_id'] ?? null;
        if ($originalAppUserId) {
            $subscription = Subscription::where('user_id', $user->id)
                ->where('revenuecat_original_app_user_id', $originalAppUserId)
                ->latest()
                ->first();

            if ($subscription) {
                return $subscription;
            }
        }

        // Final fallback to finding by product ID and transaction ID
        $productId = $event['product_id'] ?? null;
        $transactionId = $event['transaction_id'] ?? null;

        if ($productId && $transactionId) {
            return Subscription::where('user_id', $user->id)
                ->where('revenuecat_product_identifier', $productId)
                ->where('transaction_id', $transactionId)
                ->latest()
                ->first();
        }

        return null;
    }

    /**
     * Map RevenueCat product ID to our subscription plan
     *
     * @param string|null $productId
     * @return SubscriptionPlan
     */
    private function mapProductIdToPlan(?string $productId): SubscriptionPlan
    {
        // Map your RevenueCat product IDs to subscription plans
        return match ($productId) {
            'rydo_premium_yearly', 'rydo_premium_monthly', 'rydo_premium_3_months', 'com.rydo.premium.yearly', 'com.rydo.premium.monthly' => SubscriptionPlan::RYDO_PLUS,
            default => SubscriptionPlan::RYDO_PLUS, // Default to RYDO_PLUS
        };
    }

    /**
     * Create payment history record for RevenueCat transactions
     *
     * @param User $user
     * @param Subscription $subscription
     * @param array $event
     * @return void
     */
    private function createPaymentHistoryRecord(User $user, Subscription $subscription, array $event): void
    {
        try {
            $amount = $event['price'] ?? 0;
            $currency = $event['currency'] ?? 'USD';
            $transactionId = $event['transaction_id'] ?? $event['original_transaction_id'] ?? null;
            $store = $event['store'] ?? '';
            $eventType = $event['type'] ?? 'Unknown';

            // Check if payment history already exists for this transaction
            $existingPayment = PaymentHistory::where('transaction_id', $transactionId)
                ->where('user_id', $user->id)
                ->first();

            if ($existingPayment) {
                Log::info('Payment history already exists for transaction', [
                    'transaction_id' => $transactionId,
                    'existing_payment_id' => $existingPayment->id,
                ]);
                return;
            }

            PaymentHistory::create([
                'user_id' => $user->id,
                'subscription_id' => $subscription->id,
                'amount' => abs($amount), // Ensure positive amount
                'currency' => strtoupper($currency),
                'payment_method' => $this->mapRevenueCatStoreToPaymentMethod($store),
                'transaction_id' => $transactionId,
                'status' => PaymentStatus::COMPLETED,
                'notes' => "RevenueCat {$eventType} - {$store}",
            ]);

            Log::info('Payment history record created', [
                'user_id' => $user->id,
                'subscription_id' => $subscription->id,
                'transaction_id' => $transactionId,
                'amount' => $amount,
                'currency' => $currency,
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to create payment history record', [
                'user_id' => $user->id,
                'subscription_id' => $subscription->id,
                'error' => $e->getMessage(),
                'event_type' => $event['type'] ?? 'Unknown',
            ]);
        }
    }

    /**
     * Map RevenueCat store types to user-friendly payment method names
     *
     * @param string $store
     * @return string
     */
    private function mapRevenueCatStoreToPaymentMethod(string $store): string
    {
        return match (strtoupper($store)) {
            'APP_STORE' => 'Apple Pay',
            'PLAY_STORE' => 'Google Play',
            default => ucfirst(strtolower($store)) ?: 'Unknown',
        };
    }

    /**
     * Get points for a subscription plan
     *
     * @param SubscriptionPlan $plan
     * @return int
     */
    private function getPointsForPlan(SubscriptionPlan $plan): int
    {
        return match ($plan) {
            SubscriptionPlan::RYDO_PLUS => 1000,
            SubscriptionPlan::BASIC => 500,
            default => 1000,
        };
    }

    /**
     * Map RevenueCat store types to platform types
     *
     * @param string|null $store
     * @return string|null
     */
    private function mapRevenueCatStoreToPlatform(?string $store): ?string
    {
        return match (strtoupper($store ?? '')) {
            'APP_STORE' => 'Apple Pay',
            'PLAY_STORE' => 'Google Pay',
            default => $store,
        };
    }
}