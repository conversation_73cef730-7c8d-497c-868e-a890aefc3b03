import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/core/theme/app_text_theme.dart';
import 'package:zod/features/auth/auth.dart';
import 'package:zod/features/home/<USER>/providers/coming_auctions_provider.dart';

import 'home_coming_auction_shimmer.dart';
import 'home_coming_auctions_item_view.dart';

class HomeComingAuctionsView extends ConsumerWidget {
  const HomeComingAuctionsView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final upcomingState = ref.watch(upComingAuctionsProvider);
    return upcomingState.when(
      data: (upcomingAuctions) {
        final auctions = upcomingAuctions.data.data;
        return Padding(
          padding: EdgeInsetsDirectional.only(
              start: 16, end: auctions.length == 1 ? 16 : 0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                Auth.check()
                    ? '${context.localizations.your_next_auction} 🔥'
                    : '${context.localizations.coming_soon} 🔥',
                style: context.textTheme.titleSmall?.w700,
              ),
              SizedBox(height: 8),
              SizedBox(
                height: 358,
                child: ListView.separated(
                  shrinkWrap: true,
                  itemCount: auctions.length,
                  scrollDirection: Axis.horizontal,
                  physics: BouncingScrollPhysics(),
                  padding: EdgeInsets.zero, // Remove default padding
                  separatorBuilder: (_, __) => SizedBox(width: 8),
                  itemBuilder: (context, index) {
                    final auction = auctions[index];
                    return HomeComingAuctionsItemView(
                      auction: auction,
                      totalItems: auctions.length,
                      index: index,
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
      error: (e, s) => Center(child: Text(e.toString())),
      loading: () => HomeComingAuctionShimmer(),
    );
  }
}
