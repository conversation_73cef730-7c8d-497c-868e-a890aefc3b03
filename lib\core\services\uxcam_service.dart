import 'dart:developer';
import 'package:flutter_uxcam/flutter_uxcam.dart';

/// Service class to manage UXCam functionality
class UXCamService {
  static const String _logName = 'UXCamService';

  /// Tag a user session with additional information
  static Future<void> tagUserSession({
    String? userId,
    String? userName,
    Map<String, dynamic>? additionalProperties,
  }) async {
    try {
      if (userId != null) {
        await FlutterUxcam.setUserIdentity(userId);
        log('User identity set: $userId', name: _logName);
      }

      if (userName != null) {
        await FlutterUxcam.setUserProperty('user_name', userName);
        log('User name property set: $userName', name: _logName);
      }

      if (additionalProperties != null) {
        for (final entry in additionalProperties.entries) {
          await FlutterUxcam.setUserProperty(entry.key, entry.value.toString());
          log(
            'User property set: ${entry.key} = ${entry.value}',
            name: _logName,
          );
        }
      }
    } catch (e) {
      log('Error tagging user session: $e', name: _logName);
    }
  }

  /// Log a custom event
  static Future<void> logEvent(
    String eventName, [
    Map<String, dynamic>? properties,
  ]) async {
    try {
      if (properties != null && properties.isNotEmpty) {
        await FlutterUxcam.logEventWithProperties(eventName, properties);
        log(
          'Event logged with properties: $eventName - $properties',
          name: _logName,
        );
      } else {
        await FlutterUxcam.logEvent(eventName);
        log('Event logged: $eventName', name: _logName);
      }
    } catch (e) {
      log('Error logging event: $e', name: _logName);
    }
  }

  // =============================================================================
  // MOST IMPORTANT EVENTS FOR AUCTION APP
  // =============================================================================

  /// Track app lifecycle events
  static Future<void> trackAppOpened({Map<String, dynamic>? properties}) async {
    await logEvent('app_opened', {
      'timestamp': DateTime.now().toIso8601String(),
      ...?properties,
    });
  }

  static Future<void> trackSessionStarted(
      {Map<String, dynamic>? properties}) async {
    await logEvent('session_started', {
      'timestamp': DateTime.now().toIso8601String(),
      ...?properties,
    });
  }

  /// Track critical auction events
  static Future<void> trackAuctionViewed({
    required String auctionId,
    String? auctionName,
    String? auctionStatus,
    double? entryFee,
    double? prizeValue,
    int? participantCount,
    Map<String, dynamic>? additionalProperties,
  }) async {
    await logEvent('auction_viewed', {
      'auction_id': auctionId,
      'auction_name': auctionName,
      'auction_status': auctionStatus,
      'entry_fee': entryFee,
      'prize_value': prizeValue,
      'participant_count': participantCount,
      'timestamp': DateTime.now().toIso8601String(),
      ...?additionalProperties,
    });
  }

  static Future<void> trackAuctionJoined({
    required String auctionId,
    String? userId,
    double? entryFee,
    Map<String, dynamic>? additionalProperties,
  }) async {
    await logEvent('auction_joined', {
      'auction_id': auctionId,
      'user_id': userId,
      'entry_fee': entryFee,
      'timestamp': DateTime.now().toIso8601String(),
      ...?additionalProperties,
    });
  }

  static Future<void> trackBidPlaced({
    required String auctionId,
    String? userId,
    double? bidAmount,
    String? currency,
    Map<String, dynamic>? additionalProperties,
  }) async {
    await logEvent('bid_placed', {
      'auction_id': auctionId,
      'user_id': userId,
      'bid_amount': bidAmount,
      'currency': currency ?? 'SAR',
      'timestamp': DateTime.now().toIso8601String(),
      ...?additionalProperties,
    });
  }

  static Future<void> trackAuctionWon({
    required String auctionId,
    String? userId,
    double? winningBid,
    double? prizeValue,
    Map<String, dynamic>? additionalProperties,
  }) async {
    await logEvent('auction_won', {
      'auction_id': auctionId,
      'user_id': userId,
      'winning_bid': winningBid,
      'prize_value': prizeValue,
      'timestamp': DateTime.now().toIso8601String(),
      ...?additionalProperties,
    });
  }

  static Future<void> trackAuctionLost({
    required String auctionId,
    String? userId,
    double? finalBid,
    Map<String, dynamic>? additionalProperties,
  }) async {
    await logEvent('auction_lost', {
      'auction_id': auctionId,
      'user_id': userId,
      'final_bid': finalBid,
      'timestamp': DateTime.now().toIso8601String(),
      ...?additionalProperties,
    });
  }

  /// Track critical payment events
  static Future<void> trackPaymentStarted({
    required double amount,
    String? paymentMethod,
    String? transactionId,
    String? purpose, // 'wallet_charge', 'auction_entry', etc.
    Map<String, dynamic>? additionalProperties,
  }) async {
    await logEvent('payment_started', {
      'amount': amount,
      'payment_method': paymentMethod,
      'transaction_id': transactionId,
      'purpose': purpose,
      'currency': 'SAR',
      'timestamp': DateTime.now().toIso8601String(),
      ...?additionalProperties,
    });
  }

  static Future<void> trackPaymentCompleted({
    required double amount,
    String? paymentMethod,
    String? transactionId,
    String? purpose,
    Map<String, dynamic>? additionalProperties,
  }) async {
    await logEvent('payment_completed', {
      'amount': amount,
      'payment_method': paymentMethod,
      'transaction_id': transactionId,
      'purpose': purpose,
      'currency': 'SAR',
      'timestamp': DateTime.now().toIso8601String(),
      ...?additionalProperties,
    });
  }

  static Future<void> trackPaymentFailed({
    required double amount,
    String? paymentMethod,
    String? transactionId,
    String? errorCode,
    String? errorMessage,
    String? purpose,
    Map<String, dynamic>? additionalProperties,
  }) async {
    await logEvent('payment_failed', {
      'amount': amount,
      'payment_method': paymentMethod,
      'transaction_id': transactionId,
      'error_code': errorCode,
      'error_message': errorMessage,
      'purpose': purpose,
      'currency': 'SAR',
      'timestamp': DateTime.now().toIso8601String(),
      ...?additionalProperties,
    });
  }

  /// Track key navigation events
  static Future<void> trackHomeViewed(
      {Map<String, dynamic>? properties}) async {
    await logEvent('home_viewed', {
      'timestamp': DateTime.now().toIso8601String(),
      ...?properties,
    });
  }

  static Future<void> trackWalletViewed({
    double? balance,
    Map<String, dynamic>? properties,
  }) async {
    await logEvent('wallet_viewed', {
      'balance': balance,
      'timestamp': DateTime.now().toIso8601String(),
      ...?properties,
    });
  }

  static Future<void> trackTabNavigation({
    required String fromTab,
    required String toTab,
    Map<String, dynamic>? properties,
  }) async {
    await logEvent('tab_navigation', {
      'from_tab': fromTab,
      'to_tab': toTab,
      'timestamp': DateTime.now().toIso8601String(),
      ...?properties,
    });
  }

  /// Track authentication events
  static Future<void> trackUserLogin({
    String? userId,
    String? loginMethod,
    bool? hasCompleteProfile,
    double? totalPoints,
    Map<String, dynamic>? additionalProperties,
  }) async {
    await logEvent('user_login', {
      'user_id': userId,
      'login_method': loginMethod ?? 'phone',
      'has_complete_profile': hasCompleteProfile,
      'total_points': totalPoints,
      'timestamp': DateTime.now().toIso8601String(),
      ...?additionalProperties,
    });
  }

  static Future<void> trackUserLogout({
    String? userId,
    Map<String, dynamic>? properties,
  }) async {
    await logEvent('user_logout', {
      'user_id': userId,
      'timestamp': DateTime.now().toIso8601String(),
      ...?properties,
    });
  }

  /// Track critical errors
  static Future<void> trackError({
    required String errorType,
    String? errorMessage,
    String? stackTrace,
    String? userId,
    String? screenName,
    Map<String, dynamic>? additionalContext,
  }) async {
    await logEvent('app_error', {
      'error_type': errorType,
      'error_message': errorMessage,
      'stack_trace': stackTrace?.substring(0, 500), // Limit stack trace length
      'user_id': userId,
      'screen_name': screenName,
      'timestamp': DateTime.now().toIso8601String(),
      ...?additionalContext,
    });
  }

  /// Track wallet events
  static Future<void> trackWalletChargeStarted({
    required double amount,
    String? paymentMethod,
    Map<String, dynamic>? properties,
  }) async {
    await logEvent('wallet_charge_started', {
      'amount': amount,
      'payment_method': paymentMethod,
      'currency': 'SAR',
      'timestamp': DateTime.now().toIso8601String(),
      ...?properties,
    });
  }

  static Future<void> trackInsufficientBalance({
    required double requiredAmount,
    required double availableBalance,
    String? action, // 'bid_attempt', 'auction_join', etc.
    Map<String, dynamic>? properties,
  }) async {
    await logEvent('insufficient_balance', {
      'required_amount': requiredAmount,
      'available_balance': availableBalance,
      'action': action,
      'timestamp': DateTime.now().toIso8601String(),
      ...?properties,
    });
  }

  // =============================================================================
  // EXISTING METHODS
  // =============================================================================

  /// Tag a specific screen
  static Future<void> tagScreen(String screenName) async {
    try {
      await FlutterUxcam.tagScreenName(screenName);
      log('Screen tagged: $screenName', name: _logName);
    } catch (e) {
      log('Error tagging screen: $e', name: _logName);
    }
  }

  /// Hide/show sensitive views
  static Future<void> setSensitiveView(bool hide) async {
    try {
      if (hide) {
        await FlutterUxcam.setAutomaticScreenNameTagging(false);
        log('Sensitive view hidden', name: _logName);
      } else {
        await FlutterUxcam.setAutomaticScreenNameTagging(true);
        log('Sensitive view shown', name: _logName);
      }
    } catch (e) {
      log('Error setting sensitive view: $e', name: _logName);
    }
  }

  /// Pause/resume session recording
  static Future<void> pauseSession() async {
    try {
      await FlutterUxcam.pauseScreenRecording();
      log('Session paused', name: _logName);
    } catch (e) {
      log('Error pausing session: $e', name: _logName);
    }
  }

  static Future<void> resumeSession() async {
    try {
      await FlutterUxcam.resumeScreenRecording();
      log('Session resumed', name: _logName);
    } catch (e) {
      log('Error resuming session: $e', name: _logName);
    }
  }

  /// Stop current session and start a new one
  static Future<void> restartSession() async {
    try {
      await FlutterUxcam.stopApplicationAndUploadData();
      log('Session restarted', name: _logName);
    } catch (e) {
      log('Error restarting session: $e', name: _logName);
    }
  }

  /// Check if UXCam is recording
  static Future<bool> isRecording() async {
    try {
      final recording = await FlutterUxcam.isRecording();
      log('Recording status: $recording', name: _logName);
      return recording;
    } catch (e) {
      log('Error checking recording status: $e', name: _logName);
      return false;
    }
  }

  /// Get session URL for sharing with support
  static Future<String?> getSessionUrl() async {
    try {
      final url = await FlutterUxcam.urlForCurrentSession();
      log('Session URL: $url', name: _logName);
      return url;
    } catch (e) {
      log('Error getting session URL: $e', name: _logName);
      return null;
    }
  }
}
