import 'dart:io';

import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:zod/core/app_theme.dart';
import 'package:zod/features/app/app_providers.dart';
import 'package:zod/core/services/uxcam_service.dart';

import '../data/models/onboarding.dart';
import 'providers/onboarding_providers.dart';
import 'widgets/onboarding_shimmer.dart';
import 'views/onboarding_top_controls_view.dart';
import 'widgets/onboarding_screen_view.dart';
import 'views/onboarding_error_view.dart';
import 'views/onboarding_empty_view.dart';
import 'package:zod/core/localizations/app_localizations_extension.dart';
import 'package:zod/core/resources/app_colors.dart';
import 'package:zod/core/widgets/app_button.dart';

class OnboardingPage extends ConsumerStatefulWidget {
  static const String route = '/onboarding';

  const OnboardingPage({super.key});

  @override
  ConsumerState<OnboardingPage> createState() => _OnboardingPageState();
}

class _OnboardingPageState extends ConsumerState<OnboardingPage> {
  final PageController _pageController = PageController();

  @override
  void initState() {
    super.initState();
    // Make status bar transparent for full screen effect
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
      ),
    );

    // Track onboarding start
    UXCamService.trackAppOpened(properties: {'source': 'onboarding'});
    UXCamService.tagScreen('Onboarding');
  }

  @override
  void dispose() {
    _pageController.dispose();
    // Reset status bar when leaving
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
      ),
    );
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final onboardingScreens = ref.watch(onboardingScreensProvider);
    final currentPage = ref.watch(currentOnboardingPageProvider);
    final locale = ref.watch(localProvider);

    return onboardingScreens.when(
      data: (screens) => _buildOnboardingContent(screens, currentPage, locale),
      loading: () => const OnboardingShimmer(),
      error: (error, stack) => OnboardingErrorView(
        error: error,
        onSkip: _completeOnboarding,
      ),
    );
  }

  Widget _buildOnboardingContent(List screens, int currentPage, Locale locale) {
    if (screens.isEmpty) {
      return OnboardingEmptyView(onSkip: _completeOnboarding);
    }

    final onboardingScreens = screens.cast<Onboarding>();

    return Scaffold(
      body: Stack(
        children: [
          // Full screen PageView that contains everything
          PageView.builder(
            controller: _pageController,
            onPageChanged: (index) {
              ref.read(currentOnboardingPageProvider.notifier).state = index;
              // Track onboarding page view
              UXCamService.logEvent('onboarding_page_viewed', {
                'page_index': index,
                'page_title': onboardingScreens[index].title,
                'total_pages': onboardingScreens.length,
              });
            },
            itemCount: onboardingScreens.length,
            itemBuilder: (context, index) {
              return _buildOnboardingPage(onboardingScreens[index], index,
                  onboardingScreens.length, locale);
            },
          ),

          // Top controls (Skip button)
          OnboardingTopControlsView(
            onSkip: _completeOnboarding,
            locale: locale,
          ),
        ],
      ),
    );
  }

  Widget _buildOnboardingPage(
      Onboarding screen, int currentIndex, int totalPages, Locale locale) {
    final isLastPage = currentIndex == totalPages - 1;

    return Column(
      children: [
        // Image area (70% of screen)
        Expanded(
          flex: 7,
          child: OnboardingScreenView(screen: screen),
        ),

        // Content area (30% of screen)
        Expanded(
          flex: 3,
          child: Container(
            decoration: const BoxDecoration(
              color: Colors.white,
            ),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Column(
                children: [
                  const SizedBox(height: 8),

                  // Page indicators
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: List.generate(
                      totalPages,
                      (index) => Container(
                        margin: const EdgeInsets.symmetric(horizontal: 4),
                        width: currentIndex == index ? 13 : 8.5,
                        height: currentIndex == index ? 13 : 8.5,
                        decoration: BoxDecoration(
                          color: currentIndex == index
                              ? AppColors.primaryColor
                              : AppColors.lightGray,
                          borderRadius: BorderRadius.circular(6),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 12),

                  // Title
                  Text(
                    screen.title,
                    style: Theme.of(context).textTheme.titleLarge,
                    textAlign: TextAlign.center,
                  ),

                  const SizedBox(height: 12),

                  // Description
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 13.5),
                      child: Text(
                        screen.description,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: AppColors.lightSlateGray,
                            ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),

                  // Action button
                  Padding(
                    padding: EdgeInsets.only(bottom: Platform.isIOS ? 30 : 24),
                    child: AppButton(
                      onPressed: () => _onNext(isLastPage),
                      title: isLastPage
                          ? context.localizations.get_started
                          : context.localizations.continue_label,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _onNext(bool isLastPage) {
    if (isLastPage) {
      _completeOnboarding();
    } else {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _completeOnboarding() async {
    try {
      // Track onboarding completion
      UXCamService.logEvent('onboarding_completed');

      // Mark onboarding as complete
      await ref.read(onboardingServiceProvider).markOnboardingComplete();

      // Navigate to home
      if (mounted) {
        context.go('/home');
      }
    } catch (e) {
      // Track onboarding error
      UXCamService.trackError(
        errorType: 'onboarding_completion_error',
        errorMessage: e.toString(),
        screenName: 'OnboardingPage',
      );

      // If there's an error, still navigate to home
      if (mounted) {
        context.go('/home');
      }
    }
  }
}
