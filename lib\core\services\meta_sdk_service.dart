import 'package:flutter/foundation.dart';
import 'package:flutter_meta_sdk/flutter_meta_sdk.dart';

/// Service to handle Meta SDK initialization and events
class MetaSdkService {
  static final MetaSdkService _instance = MetaSdkService._internal();
  final FlutterMetaSdk _metaSdk = FlutterMetaSdk();

  factory MetaSdkService() => _instance;

  MetaSdkService._internal();

  /// Initialize the Meta SDK
  Future<void> initialize() async {
    try {
      // Enable automatic logging of app events
      await _metaSdk.setAutoLogAppEventsEnabled(true);

      // Enable advertiser tracking (for iOS)
      await _metaSdk.setAdvertiserTracking(enabled: true);

      // Get the app ID to verify configuration
      final appId = await _metaSdk.getApplicationId();

      debugPrint('Meta SDK initialized successfully with App ID: $appId');
    } catch (e) {
      debugPrint('Error initializing Meta SDK: $e');
    }
  }

  /// Log a custom event
  Future<void> logEvent({
    required String eventName,
    Map<String, dynamic>? parameters,
  }) async {
    try {
      await _metaSdk.logEvent(
        name: eventName,
        parameters: parameters,
      );
      debugPrint('Logged Meta event: $eventName with params: $parameters');
    } catch (e) {
      debugPrint('Error logging Meta event: $e');
    }
  }

  /// Log app open event
  Future<void> logAppOpen() async {
    try {
      await _metaSdk.activateApp();
      debugPrint('Logged Meta app open event');
    } catch (e) {
      debugPrint('Error logging Meta app open event: $e');
    }
  }

  /// Log purchase event
  Future<void> logPurchase({
    required double amount,
    required String currency,
    Map<String, dynamic>? parameters,
  }) async {
    try {
      await _metaSdk.logPurchase(
        amount: amount,
        currency: currency,
        parameters: parameters,
      );
      debugPrint('Logged Meta purchase event: $amount $currency');
    } catch (e) {
      debugPrint('Error logging Meta purchase event: $e');
    }
  }
}
