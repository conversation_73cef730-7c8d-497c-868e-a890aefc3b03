import 'package:bond_app_analytics/bond_app_analytics.dart';

class PurchaseEvent extends AnalyticsEvent with UserMadePurchase {
  final String purchaseTransactionId;
  final double amount;
  final double taxAmount;
  final String purchaseCurrency;
  final String paymentMethod;
  final int pointsPurchased;
  final String? auctionId;
  final String? auctionName;
  final List<EventItem> purchaseItems;
  final String paymentType; // point_purchase, entry_fee, bid_place, etc.
  final int userId;

  PurchaseEvent({
    required this.purchaseTransactionId,
    required this.amount,
    required this.taxAmount,
    required this.purchaseCurrency,
    required this.paymentMethod,
    required this.pointsPurchased,
    this.auctionId,
    this.auctionName,
    required this.purchaseItems,
    required this.paymentType,
    required this.userId,
  });

  @override
  String get key => 'purchase';

  @override
  Map<String, dynamic> get params => {
        'transaction_id': purchaseTransactionId,
        'payment_method': paymentMethod,
        'payment_type': paymentType,
        'points_purchased': pointsPurchased,
        'auction_id': auctionId,
        'auction_name': auctionName,
        'currency': purchaseCurrency,
        'user_id': userId,
        'timestamp': DateTime.now().toIso8601String(),
      };

  // UserMadePurchase interface implementation for Firebase Analytics
  @override
  String get transactionId => purchaseTransactionId;

  @override
  double get value => amount;

  @override
  double get tax => taxAmount;

  @override
  String get currency => purchaseCurrency;

  @override
  String? get coupon => null; // No coupon support in current implementation

  @override
  List<EventItem> get items => purchaseItems;

  @override
  String? get affiliation => null; // No affiliation tracking needed

  @override
  double get shipping => 0.0; // No shipping costs for digital points
}
