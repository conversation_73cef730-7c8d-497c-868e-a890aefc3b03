# Manual Subscription Management

This document explains the manual subscription management feature that allows administrators to trigger subscription events directly from the dashboard.

## Overview

The manual subscription management system provides a fallback approach for managing user subscriptions when the normal RevenueCat webhook flow is not available. This is useful for:

- Testing subscription flows
- Customer support scenarios
- Handling edge cases
- Manual subscription management
- Debugging webhook issues

## Features

### 1. Manual Subscription Page
- **Location**: `/dashboard/manual-subscription-management`
- **Access**: Admin only
- **Functionality**: 
  - View all users with their current subscription status
  - Trigger individual subscription events
  - Bulk trigger events for multiple users

### 2. Subscription Resource Integration
- **Location**: `/dashboard/subscriptions`
- **Access**: Admin only  
- **Functionality**: Trigger events directly from existing subscriptions

### 3. Webhook Event Logs
- **Location**: `/dashboard/revenuecat-webhook-events`
- **Access**: Admin only
- **Functionality**: 
  - View all webhook events (manual and automatic)
  - Reprocess failed events
  - Monitor event processing status

## Available Event Types

### INITIAL_PURCHASE
- **Purpose**: Create a new subscription for a user
- **Parameters**: Product ID, price, duration, store, currency
- **Result**: Creates new active subscription

### RENEWAL
- **Purpose**: Renew an existing subscription
- **Parameters**: Duration, price (optional)
- **Result**: Extends subscription end date, creates payment record

### PRODUCT_CHANGE
- **Purpose**: Change user's subscription plan
- **Parameters**: New product ID, price, duration
- **Result**: Updates subscription with new plan details

### CANCELLATION
- **Purpose**: Cancel user's subscription
- **Parameters**: None required
- **Result**: Marks subscription as cancelled

### EXPIRATION
- **Purpose**: Expire user's subscription
- **Parameters**: None required
- **Result**: Marks subscription as expired

### BILLING_ISSUE
- **Purpose**: Log a billing problem
- **Parameters**: None required
- **Result**: Logs issue but keeps subscription active

## How It Works

1. **Event Creation**: The system creates a mock RevenueCat webhook payload with proper pricing and product information
2. **Processing**: The payload is sent through the existing webhook processing system
3. **Validation**: All normal validation and business logic applies
4. **Database Updates**: Subscriptions, payment history, and user data are updated automatically
5. **Payment History**: For purchase events (INITIAL_PURCHASE, RENEWAL), payment history records are created automatically with the specified amount and currency
6. **Logging**: All events are logged in the webhook events table with manual trigger identification

## Usage Examples

### Create New Subscription
```
Event Type: INITIAL_PURCHASE
Product ID: rydo_plus_monthly
Price: 9.99
Currency: USD
Duration: 30 days
Store: manual
```

### Renew Subscription
```
Event Type: RENEWAL
Duration: 30 days
```

### Cancel Subscription
```
Event Type: CANCELLATION
```

## Payment History Integration

The manual subscription system automatically creates payment history records for relevant events:

### Events That Create Payment History
- **INITIAL_PURCHASE**: Creates payment record with specified amount and currency
- **RENEWAL**: Creates payment record for the renewal amount

### Payment History Details
- **Amount**: Automatically determined by product ID or manually specified
- **Currency**: Defaults to USD, can be customized
- **Payment Method**: Mapped from store type (e.g., "Manual/Dashboard")
- **Transaction ID**: Auto-generated unique identifier with "manual_" prefix
- **Status**: Set to "COMPLETED" for successful events
- **Notes**: Includes event type and store information

### Default Pricing
- **rydo_premium_monthly**: $9.99
- **rydo_premium_3_months**: $24.99  
- **rydo_premium_yearly**: $89.99

### Trial Subscriptions
Trial subscriptions (marked with `is_trial_period: true`) do not create payment history records, as no actual payment is processed.

## Security Notes

- Only administrators can access manual subscription management
- All manual events are logged and traceable
- Events include the admin user ID who triggered them
- Manual events are clearly marked in the logs

## Technical Implementation

### Service Class
- `App\Services\ManualSubscriptionService`
- Handles event creation and processing
- Creates mock webhook payloads
- Integrates with existing webhook system

### Filament Pages
- `ManualSubscriptionManagement`: Main user selection and event triggering page
- `RevenueCatWebhookEventResource`: Webhook event viewing and management

### Database Integration
- Uses existing webhook event storage
- Maintains data consistency with normal webhook flow
- All events stored in `revenuecat_webhook_events` table

## Troubleshooting

### Event Not Processing
1. Check webhook event logs for error details
2. Verify user exists and is not anonymous (for guest users)
3. Check subscription validation logic
4. Review application logs for detailed error information

### Permission Issues
- Ensure user has admin role
- Check `IsAdminAction::handle()` returns true

### Data Inconsistency
- All manual events use the same logic as real webhooks
- Database transactions ensure data consistency
- Failed events can be reprocessed from the webhook events page

## Monitoring

- All events appear in the webhook events table
- Processing status is tracked (pending/processed/failed)
- Failed events can be reprocessed
- Full webhook payloads are stored for debugging 