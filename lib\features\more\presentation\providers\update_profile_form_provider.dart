import 'package:bond_core/bond_core.dart';
import 'package:bond_form/bond_form.dart';
import 'package:bond_form_riverpod/bond_form_riverpod.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/features/auth/auth.dart';

import 'package:zod/features/auth/data/api.dart';
import 'package:zod/features/auth/data/errors/validation_error.dart';

class UpdateProfileFormProvider
    extends FormStateNotifier<UserMApiResult, ValidationError> {
  final AuthApi authApi;

  UpdateProfileFormProvider(this.authApi);

  User get user => Auth.user();

  @override
  Map<String, FormFieldState> fields() => {
        'avatar_id': TextFieldState(
          user.avatarId.toString(),
          label: appContext.localizations.filed_select_avatar_label,
          rules: [
            Rules.required(),
          ],
        ),
        'name': TextFieldState(
          user.name,
          label: appContext.localizations.filed_full_name_label,
          rules: [
            Rules.required(),
          ],
        ),
        'phone': TextFieldState(
          "${user.countryCode}${user.phone}",
          label: appContext.localizations.filed_phone_label,
          rules: [
            Rules.required(),
            Rules.phoneNumber(),
          ],
        ),
        'email': TextFieldState(
          user.email,
          label: appContext.localizations.filed_email_label,
          rules: [
            Rules.required(),
            Rules.email(),
          ],
        ),
        'nick_name': TextFieldState(
          user.nickName,
          label: appContext.localizations.filed_nickname_label,
          rules: [
            Rules.required(),
          ],
        ),
      };

  @override
  Future<UserMApiResult> onSubmit() async {
    final avatarId = state.required().textFieldValue('avatar_id');
    final name = state.required().textFieldValue('name');
    final email = state.required().textFieldValue('email');
    final nickname = state.required().textFieldValue('nick_name');

    final bodyForm = {
      'avatar_id': avatarId,
      'name': name,
      'nick_name': nickname,
      'email': email
    };
    final data = await authApi.updateProfile(bodyForm);
    return data;
  }
}

final updateProfileFormProvider = NotifierProvider<UpdateProfileFormProvider,
    BondFormState<UserMApiResult, ValidationError>>(
  () => UpdateProfileFormProvider(
    sl(),
  ),
);
