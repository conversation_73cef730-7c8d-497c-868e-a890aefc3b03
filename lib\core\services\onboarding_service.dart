import 'package:bond_cache/bond_cache.dart';

class OnboardingService {
  static const String _hasSeenOnboardingKey = 'has_seen_onboarding';

  /// Check if the user has already seen the onboarding screens
  Future<bool> hasSeenOnboarding() async {
    return Cache.get(_hasSeenOnboardingKey, defaultValue: false);
  }

  /// Mark onboarding as completed
  Future<void> markOnboardingComplete() async {
    await Cache.put(_hasSeenOnboardingKey, true);
  }

  /// Reset onboarding status (for testing purposes)
  Future<void> resetOnboarding() async {
    await Cache.forget(_hasSeenOnboardingKey);
  }
}
