library;

import 'package:go_router/go_router.dart';

import '../../core/route_helpers/modal_bottom_sheet_page.dart';
import 'presentations/page/soft_update_page.dart';
import 'presentations/page/update_app_page.dart';
import 'presentations/page/update_app_test_page.dart';

final updateAppRoutes = [
  GoRoute(
    path: '/update_app',
    builder: (context, state) => UpdateAppPage(
      message: state.uri.queryParameters['message'] as String,
    ),
  ),
  GoRoute(
    path: '/soft_update',
    pageBuilder: (context, state) {
      return ModalBottomSheetPage(
        child: SoftUpdatePage(
          message: state.uri.queryParameters['message'] as String,
        ),
        isScrollControlled: true,
        showDragHandle: false,
      );
    },
  ),
  GoRoute(
    path: UpdateAppTestPage.route,
    builder: (context, state) => const UpdateAppTestPage(),
  ),
];
