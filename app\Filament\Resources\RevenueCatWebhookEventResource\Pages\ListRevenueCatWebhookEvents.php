<?php

namespace App\Filament\Resources\RevenueCatWebhookEventResource\Pages;

use App\Filament\Resources\RevenueCatWebhookEventResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListRevenueCatWebhookEvents extends ListRecords
{
    protected static string $resource = RevenueCatWebhookEventResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('manualTrigger')
                ->label('Manual Subscription Management')
                ->icon('heroicon-o-user-plus')
                ->url('/dashboard/manual-subscription-management')
                ->color('primary'),
        ];
    }
} 