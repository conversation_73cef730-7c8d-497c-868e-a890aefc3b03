import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/features/auction/data/models/auction.dart';

import '../../../../core/resources/app_colors.dart';

class AuctionStatusLabel extends StatelessWidget {
  const AuctionStatusLabel({super.key, required this.status});

  final AuctionStatus status;

  @override
  Widget build(BuildContext context) {
    return _StatusView(
      title: status.displayName,
      textColor: status.textColor,
      backgroundColor: status.backgroundColor,
      borderColor: status.borderColor,
      status: status,
    );
  }
}

class _StatusView extends StatelessWidget {
  const _StatusView({
    required this.title,
    required this.textColor,
    required this.backgroundColor,
    required this.borderColor,
    required this.status,
  });

  final String title;
  final Color textColor;
  final Color backgroundColor;
  final Color borderColor;
  final AuctionStatus status;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        color: backgroundColor,
        border: Border.all(color: borderColor),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: 8,
          vertical: 4,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Visibility(
              visible: status == AuctionStatus.live,
              child: Row(
                children: [
                  Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                        color: AppColors.deepGreen,
                        borderRadius: BorderRadius.circular(30)),
                  ),
                  SizedBox(width: 5.5),
                ],
              ),
            ),
            Text(
              title,
              style: context.textTheme.labelMedium?.copyWith(
                color: textColor,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

extension TransactionStatusColorsExtension on AuctionStatus {
  String get displayName {
    switch (this) {
      case AuctionStatus.upComing:
        return appContext.localizations.comingSoon;
      case AuctionStatus.closed:
        return appContext.localizations.ended;
      case AuctionStatus.live:
        return appContext.localizations.live;
      case AuctionStatus.suspended:
        return appContext.localizations.suspended;
    }
  }

  Color get textColor {
    return switch (this) {
      AuctionStatus.upComing => AppColors.russetBrown,
      AuctionStatus.closed => AppColors.darkRed,
      AuctionStatus.live => AppColors.deepGreen,
      AuctionStatus.suspended => AppColors.slateGray,
    };
  }

  Color get backgroundColor {
    return switch (this) {
      AuctionStatus.upComing => AppColors.ivory,
      AuctionStatus.closed => AppColors.lightRed,
      AuctionStatus.live => AppColors.mintCream,
      AuctionStatus.suspended => AppColors.lightGray,
    };
  }

  Color get borderColor {
    return switch (this) {
      AuctionStatus.upComing => AppColors.goldenrod,
      AuctionStatus.closed => AppColors.red,
      AuctionStatus.live => AppColors.mintGreen,
      AuctionStatus.suspended => AppColors.charcoalGray,
    };
  }
}

extension StringExtensions on String {
  String capitalize() {
    return "${this[0].toUpperCase()}${substring(1)}";
  }
}
