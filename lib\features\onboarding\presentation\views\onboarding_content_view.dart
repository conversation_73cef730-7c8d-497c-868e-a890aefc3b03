import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zod/core/localizations/app_localizations_extension.dart';
import 'package:zod/core/resources/app_colors.dart';
import 'package:zod/core/widgets/app_button.dart';
import '../../data/models/onboarding.dart';

class OnboardingContentView extends ConsumerWidget {
  final List<Onboarding> screens;
  final int currentPage;
  final Locale locale;
  final VoidCallback onNext;

  const OnboardingContentView({
    super.key,
    required this.screens,
    required this.currentPage,
    required this.locale,
    required this.onNext,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isLastPage = currentPage == screens.length - 1;

    return Positioned(
      left: 0,
      right: 0,
      bottom: 0,
      height: MediaQuery.of(context).size.height * 0.29,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Column(
            children: [
              // Page indicators
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(
                  screens.length,
                  (index) => Container(
                    margin: EdgeInsets.symmetric(horizontal: 4),
                    width: currentPage == index ? 13 : 8.5,
                    height: currentPage == index ? 13 : 8.5,
                    decoration: BoxDecoration(
                      color: currentPage == index
                          ? AppColors.primaryColor
                          : AppColors.lightGray,
                      borderRadius: BorderRadius.circular(6),
                    ),
                  ),
                ),
              ),
              SizedBox(height: 12),

              // Title
              Text(
                screens[currentPage].title,
                style: Theme.of(context).textTheme.titleLarge,
                textAlign: TextAlign.center,
              ),

              SizedBox(height: 12),

              // Description
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 13.5),
                child: Text(
                  screens[currentPage].description,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppColors.lightSlateGray,
                      ),
                  textAlign: TextAlign.center,
                ),
              ),

              Spacer(),

              // Action button
              Padding(
                padding: const EdgeInsets.only(bottom: 24),
                child: AppButton(
                  onPressed: onNext,
                  title: isLastPage
                      ? context.localizations.get_started
                      : context.localizations.continue_label,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
