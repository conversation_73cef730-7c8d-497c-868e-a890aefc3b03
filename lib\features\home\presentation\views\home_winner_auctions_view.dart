import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/core/theme/app_text_theme.dart';
import 'package:zod/features/home/<USER>/providers/winner_auctions_provider.dart';
import 'package:zod/features/home/<USER>/views/winner_auctions_item_view.dart';

import 'home_winner_auction_shimmer.dart';

class HomeWinnerAuctionsView extends ConsumerWidget {
  const HomeWinnerAuctionsView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final winnerState = ref.watch(winnerAuctionsProvider);
    return winnerState.when(
      data: (winnerAuctions) {
        final auctions = winnerAuctions.data.data;
        return auctions.isEmpty
            ? SizedBox.shrink()
            : Padding(
                padding: EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: 32),
                    Text(
                      '${context.localizations.winner_chicken_dinner} 🎉',
                      style: context.textTheme.titleSmall?.w700,
                    ),
                    SizedBox(height: 8),
                    SizedBox(
                      height: 250,
                      child: ListView.separated(
                        itemCount: auctions.length,
                        scrollDirection: Axis.horizontal,
                        physics: BouncingScrollPhysics(),
                        separatorBuilder: (_, __) => SizedBox(width: 8),
                        itemBuilder: (context, index) {
                          final auction = auctions[index];
                          return WinnerAuctionsItemView(auction: auction);
                        },
                      ),
                    ),
                  ],
                ),
              );
      },
      error: (e, s) => Center(child: Text(e.toString())),
      loading: () => HomeWinnerAuctionShimmer(),
    );
  }
}
