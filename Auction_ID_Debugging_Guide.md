# Auction ID Debugging Guide

## 🔍 Why Auction ID Can Be Null

The auction ID in purchase events can be `null` for **legitimate reasons**. Here's when it should vs shouldn't be populated:

### ✅ **When Auction ID Should Be NULL** (Expected)

1. **General Wallet Charges**: User charging wallet from the main wallet page
2. **Direct Point Purchases**: Not related to any specific auction
3. **Regular Top-ups**: User just wants to add points to their account

### ❌ **When Auction ID Should NOT Be NULL** (Issue)

1. **Auction Entry**: User charging wallet to join a specific auction
2. **Bidding Context**: User needs more points for bidding in an auction
3. **Auction-Specific Charges**: Triggered from auction details page

## 🛠️ **How to Debug**

### Step 1: Check Debug Logs

Look for these log messages in your Flutter console:

```bash
# Expected for general charges
[PurchaseTracker] Purchase event: No auction ID provided - this is a general wallet charge

# Expected for auction charges  
[PurchaseTracker] Purchase event: Auction-specific purchase - ID: 123, Name: Premium Auction
```

### Step 2: Trace the Data Flow

The auction data flows like this:

```
Auction Page (bid_placement_view.dart)
           ↓
Routes to: /charge_wallet/:id/:fee/:increment
           ↓
Creates: ChargeWalletAuction(auctionId, auction)
           ↓
ChargeWalletProvider.chargeWallet()
           ↓
Creates: WalletChargeDto(price, points, auction, auctionId)
           ↓
PaymentProvider receives WalletChargeDto
           ↓
Events fired with: chargeDto.auctionId, chargeDto.auction?.name
```

### Step 3: Verify Route Parameters

Check if the auction route is being called correctly:

```dart
// In bid_placement_view.dart:338
final url = "/charge_wallet/${auction.id}/${auction.entryFee}/${auction.incrementBidPoints}";
goRouter.push(url, extra: auction);
```

### Step 4: Check WalletChargeDto Creation

Verify that both `auction` and `auctionId` are passed:

```dart
// In charge_wallet_provider.dart:97 (FIXED)
WalletChargeDto walletChargeDto = WalletChargeDto(state.price, state.points,
    auction: chargeWalletAuction.auction,      // ✅ Auction object
    auctionId: chargeWalletAuction.auctionId); // ✅ Auction ID
```

## 🔧 **Common Issues & Fixes**

### Issue 1: Missing auctionId Parameter

**Problem**: `WalletChargeDto` created without `auctionId`
**Solution**: Always pass both `auction` and `auctionId` parameters

```dart
// ❌ WRONG
WalletChargeDto(price, points, auction: auction)

// ✅ CORRECT  
WalletChargeDto(price, points, auction: auction, auctionId: auction.id)
```

### Issue 2: Route Parameters Not Parsed

**Problem**: Route parameters not being extracted correctly
**Solution**: Check route parsing in `wallet/routes.dart`

```dart
// Verify these are being parsed correctly:
final auctionId = int.tryParse(state.pathParameters['id'].toString()) ?? 0;
final fee = int.tryParse(state.pathParameters['fee'].toString()) ?? 0;
final increment = int.tryParse(state.pathParameters['increment'].toString()) ?? 0;
```

### Issue 3: Auction Object Missing

**Problem**: Auction object is null even when auctionId exists
**Solution**: Check if `state.extra as Auction?` is being passed correctly

```dart
// In routes.dart
ChargeWalletAuction(
    auction: state.extra as Auction?,  // ✅ Make sure auction is passed as extra
    auctionId: auctionId,
    fee: fee,
    increment: increment
)
```

## 🧪 **Testing Scenarios**

### Test Case 1: Auction-Specific Charge
1. Go to auction details page
2. Click "Place Bid" with insufficient points
3. Should redirect to charge wallet with auction context
4. **Expected**: Auction ID should be populated in events

### Test Case 2: General Wallet Charge  
1. Go to main wallet page
2. Click "Charge Wallet"
3. **Expected**: Auction ID should be null in events

### Test Case 3: Debug Logs
1. Enable Flutter debug logs
2. Perform both test cases above
3. **Expected**: Should see appropriate debug messages

## 📊 **Analytics Verification**

### Firebase Analytics DebugView
1. Enable DebugView in Firebase Analytics
2. Perform test transactions
3. Check event parameters:
   - `auction_id`: Should be present for auction charges, absent for general charges
   - `auction_name`: Should match the auction name when present

### Event Parameters to Verify
```json
{
  "event_name": "purchase",
  "transaction_id": "txn_123",
  "payment_method": "card", 
  "points_purchased": 1000,
  "auction_id": "123",        // ← Should be present for auction charges
  "auction_name": "Premium Auction", // ← Should match auction name
  "currency": "SAR"
}
```

## 🚨 **Quick Fixes**

If you're seeing null auction IDs when they should be populated:

1. **Check the route**: Ensure `/charge_wallet/:id/:fee/:increment` format
2. **Verify route params**: Print `auctionId` in `routes.dart` 
3. **Check DTO creation**: Ensure both `auction` and `auctionId` are passed
4. **Test the flow**: Follow the complete flow from auction page to payment

## 📝 **Future Improvements**

1. **Add validation**: Warn if auction context is expected but missing
2. **Enhanced logging**: Include more context in debug logs
3. **Type safety**: Use sealed classes for charge contexts
4. **Unit tests**: Test auction vs non-auction charge scenarios

---

**Remember**: Null auction ID is **normal** for general wallet charges. Only investigate when it's null in auction-specific contexts. 