# Purchase Events Implementation

## Overview

This document outlines the implementation of purchase-related analytics events in the Zod mobile application. The purchase events provide comprehensive tracking of the payment funnel from initiation to completion or failure, with enhanced context including payment types and user identification.

## Events Implemented

### 1. PurchaseEvent
**Purpose**: Track successful purchase completions  
**Key**: `purchase`  
**Firebase Integration**: Implements `UserMadePurchase` interface for enhanced e-commerce tracking

**Parameters**:
- `transaction_id`: Unique transaction identifier
- `payment_method`: Method used (card, apple_pay, saved_card)
- `payment_type`: Type of purchase (point_purchase, entry_fee, bid_place, prize_purchase)
- `points_purchased`: Number of points purchased
- `auction_id`: Associated auction ID (if applicable)
- `auction_name`: Associated auction name (if applicable)
- `currency`: Currency code (SAR)
- `user_id`: User identifier for user-specific tracking
- `timestamp`: ISO 8601 timestamp

**Firebase Analytics Parameters**:
- `value`: Total purchase amount including tax
- `tax`: Tax amount
- `currency`: Currency code
- `items`: Array of purchased items with details
- `transaction_id`: Transaction reference

### 2. PaymentInitiatedEvent
**Purpose**: Track when users start the payment process  
**Key**: `payment_initiated`

**Parameters**:
- `amount`: Payment amount
- `currency`: Currency code
- `payment_method`: Selected payment method
- `payment_type`: Type of purchase (point_purchase, entry_fee, bid_place, prize_purchase)
- `points_to_purchase`: Number of points to purchase
- `auction_id`: Associated auction ID (if applicable)
- `auction_name`: Associated auction name (if applicable)
- `user_id`: User identifier
- `timestamp`: ISO 8601 timestamp

### 3. PaymentFailedEvent
**Purpose**: Track payment failures and cancellations  
**Key**: `payment_failed`

**Parameters**:
- `amount`: Attempted payment amount
- `currency`: Currency code
- `payment_method`: Attempted payment method
- `payment_type`: Type of purchase (point_purchase, entry_fee, bid_place, prize_purchase)
- `points_to_purchase`: Number of points attempted
- `error_message`: Failure reason
- `auction_id`: Associated auction ID (if applicable)
- `auction_name`: Associated auction name (if applicable)
- `transaction_id`: Transaction ID (if available)
- `user_id`: User identifier
- `timestamp`: ISO 8601 timestamp

## Payment Types

The system automatically determines payment type based on context:

| Payment Type | Value | Description | Context |
|--------------|-------|-------------|---------|
| Point Purchase | `point_purchase` | General wallet charging | User charging wallet from main page |
| Entry Fee | `entry_fee` | Auction entry payment | User joining an auction |
| Bid Place | `bid_place` | Bidding-related payment | User running out of points while bidding |
| Prize Purchase | `prize_purchase` | Buying auction prize | Winner purchasing prize directly |
| Subscription | `subscription` | Premium features | Future subscription payments |

## Implementation Details

### File Structure
```
lib/
├── core/analytics/events/
│   ├── purchase_event.dart
│   ├── payment_initiated_event.dart
│   ├── payment_failed_event.dart
│   ├── payment_type.dart (enum)
│   └── events.dart (exports)
├── features/payment/data/events/
│   └── purchase_event_tracker.dart
└── features/payment/presentation/providers/
    └── payment_provider_new.dart (integration)
```

### Usage Examples

#### Tracking a Successful Purchase
```dart
import 'package:zod/features/payment/data/events/purchase_event_tracker.dart';

trackPurchase(
  transactionId: 'txn_123456789',
  amount: 115.0, // Including tax
  taxAmount: 15.0,
  currency: 'SAR',
  paymentMethod: 'card',
  pointsPurchased: 1000,
  paymentType: PaymentType.entryFee,
  auctionId: 'auction_456',
  auctionName: 'Premium Auction',
  userId: 12345, // Optional - auto-fetched if not provided
);
```

#### Tracking Payment Initiation
```dart
trackPaymentInitiated(
  amount: 115.0,
  currency: 'SAR',
  paymentMethod: 'apple_pay',
  pointsPurchased: 1000,
  paymentType: PaymentType.pointPurchase,
  auctionId: null, // No auction context
  auctionName: null,
);
```

#### Tracking Payment Failure
```dart
trackPaymentFailed(
  amount: 115.0,
  currency: 'SAR',
  paymentMethod: 'card',
  pointsPurchased: 1000,
  errorMessage: 'Insufficient funds',
  paymentType: PaymentType.bidPlace,
  auctionId: 'auction_456',
  auctionName: 'Premium Auction',
  transactionId: 'txn_123456789',
);
```

## Integration Points

### Payment Provider Integration
The events are automatically triggered in `PaymentProvider` at key points:

1. **Payment Initiation**: Called when `processCardPayment()`, `processApplePayPayment()`, or `process3DSecureTokenizedCardPayment()` starts
2. **Payment Success**: Called when `paymentApi.processPayment()` succeeds
3. **Payment Failure**: Called in exception handlers and result processing failures

### Automatic Payment Type Detection
```dart
// Automatically determines payment type based on auction context
final paymentType = PaymentType.fromAuctionContext(
  isAuctionRelated: chargeDto.auctionId != null,
  isEntryFee: chargeDto.auction?.isNotJoined == true,
  isBidding: chargeDto.auction?.isJoined == true,
);
```

### Event Flow
```
User Selects Payment Method
           ↓
PaymentInitiatedEvent fired (with payment_type & user_id)
           ↓
Payment Gateway Processing
           ↓
    Success? ──→ PurchaseEvent fired (with payment_type & user_id)
           ↓
    Failure? ──→ PaymentFailedEvent fired (with payment_type & user_id)
```

## Analytics Benefits

### Enhanced Key Metrics Available
- **Conversion Rate by Payment Type**: `(PurchaseEvent count / PaymentInitiatedEvent count) * 100` per payment type
- **Payment Method Performance**: Success rates by payment method and type
- **User Behavior Analysis**: Purchase patterns by individual users
- **Auction Impact Analysis**: Entry fees vs general purchases vs bidding
- **Revenue Breakdown**: Revenue by payment type and user segments
- **Failure Analysis**: Common failure reasons by payment type

### Firebase Analytics Integration
- Automatic e-commerce event tracking through `UserMadePurchase` interface
- Enhanced funnel analysis with payment type dimensions
- User-specific purchase behavior tracking
- Custom audience creation based on payment types and user behavior
- Integration with Firebase Predictions for churn prevention

### Enhanced Analytics Queries

#### Revenue by Payment Type (BigQuery)
```sql
SELECT 
  event_params.value.string_value as payment_type,
  event_params.value.string_value as payment_method,
  COUNT(*) as transaction_count,
  SUM(event_params.value.double_value) as total_revenue,
  COUNT(DISTINCT user_pseudo_id) as unique_users
FROM `project.analytics_xxx.events_*`,
UNNEST(event_params) as event_params
WHERE event_name = 'purchase'
  AND _TABLE_SUFFIX BETWEEN '20240101' AND '20240131'
  AND event_params.key IN ('payment_type', 'payment_method')
GROUP BY payment_type, payment_method
ORDER BY total_revenue DESC
```

#### User Purchase Behavior Analysis
```sql
SELECT 
  user_pseudo_id,
  COUNT(*) as total_purchases,
  COUNT(CASE WHEN event_params.value.string_value = 'entry_fee' THEN 1 END) as entry_fees,
  COUNT(CASE WHEN event_params.value.string_value = 'point_purchase' THEN 1 END) as point_purchases,
  COUNT(CASE WHEN event_params.value.string_value = 'bid_place' THEN 1 END) as bid_purchases,
  SUM(event_params.value.double_value) as total_spent
FROM `project.analytics_xxx.events_*`,
UNNEST(event_params) as event_params
WHERE event_name = 'purchase'
  AND _TABLE_SUFFIX BETWEEN '20240101' AND '20240131'
  AND event_params.key = 'payment_type'
GROUP BY user_pseudo_id
ORDER BY total_spent DESC
```

#### Payment Funnel by Type
```sql
WITH funnel AS (
  SELECT 
    user_pseudo_id,
    event_name,
    event_params.value.string_value as payment_type
  FROM `project.analytics_xxx.events_*`,
  UNNEST(event_params) as event_params
  WHERE event_name IN ('payment_initiated', 'purchase', 'payment_failed')
    AND event_params.key = 'payment_type'
    AND _TABLE_SUFFIX BETWEEN '20240101' AND '20240131'
)
SELECT 
  payment_type,
  COUNT(CASE WHEN event_name = 'payment_initiated' THEN 1 END) as initiated,
  COUNT(CASE WHEN event_name = 'purchase' THEN 1 END) as completed,
  COUNT(CASE WHEN event_name = 'payment_failed' THEN 1 END) as failed,
  SAFE_DIVIDE(COUNT(CASE WHEN event_name = 'purchase' THEN 1 END), 
              COUNT(CASE WHEN event_name = 'payment_initiated' THEN 1 END)) * 100 as conversion_rate
FROM funnel
GROUP BY payment_type
ORDER BY conversion_rate DESC
```

## Testing

### Development Testing
1. Enable Firebase Analytics DebugView
2. Test each payment method (card, Apple Pay, saved card)
3. Test different payment types (general wallet vs auction context)
4. Test payment failures (network issues, declined cards)
5. Verify events appear in Firebase Analytics DebugView with correct payment_type and user_id

### Event Validation Checklist
- [ ] `payment_initiated` fires when payment starts with correct payment_type
- [ ] `purchase` fires on successful completion with correct payment_type and user_id
- [ ] `payment_failed` fires on failures/cancellations with error context
- [ ] Payment type correctly determined (entry_fee for auctions, point_purchase for general)
- [ ] User ID populated correctly (from Auth.user().id)
- [ ] All required parameters are populated
- [ ] Firebase Analytics receives events correctly
- [ ] Events appear in Firebase Analytics DebugView

## Troubleshooting

### Common Issues
1. **Events not appearing**: Check Firebase Analytics configuration and network connectivity
2. **Missing parameters**: Verify `WalletChargeDto` contains required fields
3. **Incorrect payment type**: Check auction context detection logic
4. **Missing user ID**: Verify Auth.user() is available and valid
5. **Duplicate events**: Ensure events are only fired once per payment attempt

### Debug Logging
Enhanced debug logging now includes payment type and user context:
```
[PurchaseTracker] Purchase event: entry_fee - auction context ID: 123, Name: Premium Auction (User: 456)
[PurchaseTracker] Payment initiated: point_purchase - no auction context (User: 456)
[PurchaseTracker] Payment failed: bid_place - auction context ID: 123, Name: Premium Auction (User: 456)
```

## Future Enhancements

### Planned Features
- Payment method preferences tracking per user
- Subscription purchase events
- Refund event tracking with original payment type
- A/B testing integration for payment flow optimization
- Cohort analysis based on payment behavior

### Additional Analytics
- Cart abandonment tracking by payment type
- Payment retry attempts with type context
- Geographic payment method and type preferences
- Time-based purchase pattern analysis by user segments
- Auction performance impact on payment types

---

**Last Updated**: January 2025  
**Version**: 2.0  
**Maintainers**: Product Analytics Team 