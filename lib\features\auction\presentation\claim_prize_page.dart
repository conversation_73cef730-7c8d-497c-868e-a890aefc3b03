import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/core/widgets/title_app_bar.dart';
import 'package:zod/features/auction/data/states/claim_prize_state.dart';
import 'package:zod/features/auction/presentation/providers/claim_prize_provider.dart';
import 'package:zod/features/auction/presentation/providers/timer_provider.dart';
import 'package:zod/features/auction/presentation/views/claim_prize_bottom_bar_view.dart';
import 'package:zod/features/auction/presentation/views/delivery_address_section_view.dart';
import 'package:zod/features/auction/presentation/views/order_summary_section_view.dart';
import 'package:zod/features/auction/presentation/views/product_card_view.dart';
import 'package:zod/features/auction/presentation/views/shimmer_claim_prize_view.dart';
import 'package:zod/features/auction/presentation/views/shimmer_claim_prize_bottom_bar_view.dart';

class ClaimPrizePage extends ConsumerWidget {
  static const String route = '/claim-prize/:id';
  final int auctionId;

  const ClaimPrizePage({super.key, required this.auctionId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Watch the claim prize state
    final claimPrizeState = ref.watch(claimPrizeProvider(auctionId));

    log("ClaimPrizePage claimPrizeState: ${claimPrizeState.claimPrize?.toJson()}");
    return Scaffold(
      appBar: TitleAppBar(title: context.localizations.claim_your_prize),
      body: claimPrizeState.isLoading
          ? const ShimmerClaimPrizeView()
          : claimPrizeState.error != null
              ? Center(child: Text(claimPrizeState.error!))
              : _buildContent(context, ref, claimPrizeState),
      bottomNavigationBar:
          claimPrizeState.error != null || claimPrizeState.claimPrize == null
              ? null
              : claimPrizeState.isLoading
                  ? const ShimmerClaimPrizeBottomBarView()
                  : _buildBottomBar(context, ref, claimPrizeState),
    );
  }

  Widget _buildContent(
      BuildContext context, WidgetRef ref, ClaimPrizeState claimPrizeState) {
    if (claimPrizeState.claimPrize == null) {
      return Center(child: Text('Failed to load claim prize data'));
    }

    final timerState = ref.watch(
      countdownTimerProvider(claimPrizeState.claimPrize!.claimBefore ??
          DateTime.now().add(const Duration(hours: 24))),
    );

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ProductCardView(
              claimPrize: claimPrizeState.claimPrize!, timerState: timerState),
          const SizedBox(height: 24),
          DeliveryAddressSectionView(
            auctionId: auctionId,
            selectedAddress: claimPrizeState.selectedAddress,
          ),
          const SizedBox(height: 16),
          OrderSummarySectionView(claimPrize: claimPrizeState.claimPrize!),
          const SizedBox(height: 120),
        ],
      ),
    );
  }

  Widget _buildBottomBar(
      BuildContext context, WidgetRef ref, ClaimPrizeState claimPrizeState) {
    final timerState = ref.watch(
      countdownTimerProvider(claimPrizeState.claimPrize!.claimBefore ??
          DateTime.now().add(const Duration(hours: 24))),
    );

    return ClaimPrizeBottomBarView(
      auctionId: auctionId,
      claimPrize: claimPrizeState.claimPrize!,
      selectedAddress: claimPrizeState.selectedAddress,
      timerState: timerState,
    );
  }
}
