import 'package:flutter/material.dart';

class ModalBottomSheetPage<T> extends Page<T> {
  final Widget child;
  final bool isScrollControlled;
  final bool showDragHandle;
  const ModalBottomSheetPage({
    required this.child,
    this.isScrollControlled = false,
    this.showDragHandle = true,
    super.key,
    super.name,
    super.arguments,
  });

  @override
  Route<T> createRoute(BuildContext context) {
    return ModalBottomSheetRoute<T>(
      builder: (BuildContext context) {
        return child;
      },
      useSafeArea: true,
      showDragHandle: showDragHandle,
      isScrollControlled: isScrollControlled,
      settings: this,
    );
  }
}
