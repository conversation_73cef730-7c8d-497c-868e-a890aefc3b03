// ignore_for_file: deprecated_member_use

import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/core/resources/app_icons.dart';
import 'package:zod/features/auction/presentation/providers/open_auctions_provider.dart';
import 'package:zod/features/auth/auth.dart';
import 'package:zod/features/wallet/presentation/providers/wallet_transactions_provider.dart';

import '../../../core/resources/app_colors.dart';
import '../../auth/presentation/providers/me_provider.dart';
import '../../home/<USER>/providers/coming_auctions_provider.dart';
import '../../home/<USER>/providers/how_it_work_provider.dart';
import '../../home/<USER>/providers/winner_auctions_provider.dart';
import '../../home/<USER>/providers/winner_reviews_auctions_provider.dart';

class MainPage extends ConsumerWidget {
  const MainPage({
    super.key,
    required this.body,
  });

  final StatefulNavigationShell body;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Listen to the meProvider to rebuild when auth state changes
    ref.listen(meProvider, (previous, next) {
      // This will cause the widget to rebuild when the user logs in or out
    });

    SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
    ));
    // Determine if user is logged in
    final isLoggedIn = Auth.check();

    // Adjust the current index for the bottom navigation bar
    // If not logged in and on the More tab (index 3), we need to show it as index 2
    int adjustedCurrentIndex = body.currentIndex;
    if (!isLoggedIn && body.currentIndex >= 3) {
      adjustedCurrentIndex =
          2; // More tab becomes index 2 when wallet is hidden
    } else if (!isLoggedIn && body.currentIndex == 2) {
      // If we're on the wallet tab but not logged in, default to home
      adjustedCurrentIndex = 0;
    }

    return Scaffold(
      body: body,
      bottomNavigationBar: Container(
        decoration: const BoxDecoration(
          color: AppColors.white,
          border: Border(
            top: BorderSide(
              color: AppColors.lightPlatinum,
              width: 1,
            ),
          ),
        ),
        child: BottomNavigationBar(
          type: BottomNavigationBarType.fixed,
          currentIndex: adjustedCurrentIndex,
          onTap: (int index) => onTap(index, ref),
          items: _buildNavigationItems(context, adjustedCurrentIndex),
        ),
      ),
    );
  }

  List<BottomNavigationBarItem> _buildNavigationItems(
      BuildContext context, int currentIndex) {
    final isLoggedIn = Auth.check();
    final items = [
      BottomNavigationBarItem(
        icon: SvgPicture.asset(
          AppIcons.home,
          color:
              currentIndex == 0 ? AppColors.primaryColor : AppColors.slateGray,
        ),
        label: context.localizations.navigation_bar_home,
      ),
      BottomNavigationBarItem(
        icon: SvgPicture.asset(
          AppIcons.hammer,
          color:
              currentIndex == 1 ? AppColors.primaryColor : AppColors.slateGray,
        ),
        label: context.localizations.auctions,
      ),
    ];

    // Only add wallet tab if user is logged in
    if (isLoggedIn) {
      items.add(
        BottomNavigationBarItem(
          icon: SvgPicture.asset(
            AppIcons.wallet,
            color: currentIndex == 2
                ? AppColors.primaryColor
                : AppColors.slateGray,
          ),
          label: context.localizations.wallet,
        ),
      );
    }

    // Add more tab (will be at index 2 if not logged in, or 3 if logged in)
    final moreTabIndex = isLoggedIn ? 3 : 2;
    items.add(
      BottomNavigationBarItem(
        icon: SvgPicture.asset(
          AppIcons.menu,
          color: currentIndex == moreTabIndex
              ? AppColors.primaryColor
              : AppColors.slateGray,
        ),
        label: context.localizations.more,
      ),
    );

    return items;
  }

  void onTap(int index, WidgetRef ref) {
    log("int index $index");
    final isLoggedIn = Auth.check();

    // Adjust the index for the branch navigation if wallet tab is hidden
    int branchIndex = index;
    if (!isLoggedIn && index == 2) {
      // If not logged in and tapping on More tab (which is at index 2),
      // we need to navigate to branch index 3 (More branch)
      branchIndex = 3;
    }

    // Invalidate providers based on the tab being navigated to
    switch (branchIndex) {
      case 0: // Home
        ref.invalidate(upComingAuctionsProvider);
        ref.invalidate(howItWorkProvider);
        ref.invalidate(winnerAuctionsProvider);
        ref.invalidate(winnerReviewsAuctionsProvider);
        ref.invalidate(meProvider);
        break;
      case 1: // Auctions
        ref.invalidate(openAuctionsProvider(''));
        if (isLoggedIn) {
          ref.invalidate(meProvider);
        }
        break;
      case 2: // Wallet (only accessible when logged in)
        if (!isLoggedIn) {
          // Should not happen due to our UI, but just in case
          return;
        }
        break;
      case 3: // More
        // No specific providers to invalidate

        break;
    }
    try {
      if (isLoggedIn) {
        ref.invalidate(walletTransactionsProvider);
      }
    } catch (_) {}

    // Navigate to the appropriate branch
    body.goBranch(branchIndex);
  }
}
