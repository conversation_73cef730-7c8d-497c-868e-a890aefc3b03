# UXCam Integration Guide

This document provides a comprehensive guide for the UXCam integration in the Zod Flutter application.

## Overview

UXCam is a user experience analytics platform that provides session recordings, heatmaps, and user journey analytics for mobile applications. This integration allows you to:

- Record user sessions automatically
- Track user interactions and gestures
- Analyze user behavior patterns
- Identify usability issues
- Monitor app performance from a UX perspective

## Installation

The UXCam package has been integrated into the project with the following components:

### 1. Dependencies Added

```yaml
# pubspec.yaml
dependencies:
  flutter_uxcam: ^2.6.0
```

### 2. Environment Configuration

UXCam app keys have been added to all environment files:

- `env.json` (Production)
- `env.staging.json` (Staging)
- `env.example.json` (Example/Template)

```json
{
  "UXCAM_APP_KEY": "YOUR_UXCAM_APP_KEY_HERE"
}
```

### 3. Initialization

UXCam is automatically initialized in `lib/app/app_run_tasks.dart` during app startup:

```dart
// Initialize UXCam
try {
  final uxcamAppKey = config('UXCAM_APP_KEY');
  if (uxcamAppKey != null && uxcamAppKey.isNotEmpty && uxcamAppKey != 'YOUR_UXCAM_APP_KEY_HERE') {
    FlutterUxcam.optIntoSchematicRecordings();
    FlutterUxConfig uxConfig = FlutterUxConfig(userAppKey: uxcamAppKey);
    FlutterUxcam.startWithConfiguration(uxConfig);
    log('UXCam initialized with key: ${uxcamAppKey.substring(0, 8)}...', name: 'RunAppTasks');
  } else {
    log('UXCam app key not configured or is placeholder', name: 'RunAppTasks');
  }
} catch (e) {
  log('Error initializing UXCam: $e', name: 'RunAppTasks');
}
```

## Configuration

### Getting Your UXCam App Key

1. Sign up or log in to [UXCam](https://uxcam.com)
2. Create a new app or select an existing one
3. Navigate to Settings > App Settings
4. Copy your App Key
5. Replace `YOUR_UXCAM_APP_KEY_HERE` in your environment files

### Environment Setup

Update your environment files with the actual UXCam app key:

```bash
# For production
# Update env.json with your production UXCam app key

# For staging
# Update env.staging.json with your staging UXCam app key
```

## Usage

### Basic Usage

Once configured, UXCam will automatically start recording user sessions. No additional code is required for basic functionality.

### Advanced Usage with UXCamService

A custom service class `UXCamService` has been created to provide additional functionality:

```dart
import 'package:zod/core/services/uxcam_service.dart';

// Tag user sessions
await UXCamService.tagUserSession(
  userId: 'user123',
  userName: 'John Doe',
  additionalProperties: {
    'subscription_type': 'premium',
    'app_version': '1.0.0',
  },
);

// Log custom events
await UXCamService.logEvent('auction_bid_placed', {
  'auction_id': 'auction123',
  'bid_amount': 100.0,
  'currency': 'SAR',
});

// Tag specific screens
await UXCamService.tagScreen('Auction Details');

// Control recording
await UXCamService.pauseSession();
await UXCamService.resumeSession();

// Check recording status
bool isRecording = await UXCamService.isRecording();

// Get session URL for support
String? sessionUrl = await UXCamService.getSessionUrl();
```

### Screen Tagging

You can automatically tag screens by adding the following to your page widgets:

```dart
class AuctionDetailsPage extends StatefulWidget {
  @override
  void initState() {
    super.initState();
    UXCamService.tagScreen('Auction Details');
  }
  
  // ... rest of your widget
}
```

### Event Tracking

Track important user actions:

```dart
// When user places a bid
UXCamService.logEvent('bid_placed', {
  'auction_id': auction.id,
  'bid_amount': bidAmount,
  'user_id': currentUser.id,
});

// When user completes payment
UXCamService.logEvent('payment_completed', {
  'amount': paymentAmount,
  'payment_method': 'credit_card',
  'transaction_id': transactionId,
});

// When user views a product
UXCamService.logEvent('product_viewed', {
  'product_id': product.id,
  'category': product.category,
  'price': product.price,
});
```

### User Identification

Tag users when they log in:

```dart
// In your authentication flow
await UXCamService.tagUserSession(
  userId: user.id.toString(),
  userName: user.name,
  additionalProperties: {
    'user_type': user.type,
    'registration_date': user.createdAt.toIso8601String(),
    'preferred_language': user.preferredLanguage,
  },
);
```

### Privacy and Sensitive Data

For screens containing sensitive information (like payment details), you can pause recording:

```dart
class PaymentPage extends StatefulWidget {
  @override
  void initState() {
    super.initState();
    UXCamService.pauseSession(); // Pause recording for sensitive screens
  }
  
  @override
  void dispose() {
    UXCamService.resumeSession(); // Resume when leaving sensitive screen
    super.dispose();
  }
  
  // ... rest of your widget
}
```

## Features

### Automatic Features
- **Session Recording**: Automatically records user interactions
- **Crash Analytics**: Captures crashes and errors
- **Performance Monitoring**: Tracks app performance metrics
- **User Journey Mapping**: Maps user flows through the app

### Manual Features
- **Custom Events**: Track specific user actions
- **User Tagging**: Associate sessions with user identities
- **Screen Tagging**: Label different screens and views
- **Session Control**: Pause/resume recording as needed

## Best Practices

### 1. User Privacy
- Always inform users about session recording in your privacy policy
- Pause recording on sensitive screens (payment, personal info)
- Consider implementing user consent mechanisms

### 2. Event Naming
- Use consistent naming conventions for events
- Include relevant context in event properties
- Avoid logging sensitive information in events

### 3. Performance
- UXCam is designed to have minimal performance impact
- Avoid excessive custom event logging
- Use meaningful screen names for better analytics

### 4. Testing
- Test with staging environment first
- Verify events are being logged correctly
- Check session recordings in UXCam dashboard

## Troubleshooting

### Common Issues

1. **UXCam not initializing**
   - Check that the app key is correctly set in environment files
   - Verify the app key is not the placeholder value
   - Check console logs for initialization errors

2. **Sessions not appearing in dashboard**
   - Ensure you're using the correct app key for your environment
   - Check network connectivity
   - Verify the app is in foreground when testing

3. **iOS build issues**
   - Run `cd ios && pod install` after adding the dependency
   - Ensure iOS deployment target is 10.0 or higher

### Debug Mode

Enable debug logging to troubleshoot issues:

```dart
// Add this during development
FlutterUxcam.setLogLevel(LogLevel.debug);
```

## Dashboard Access

Access your UXCam dashboard at [https://dashboard.uxcam.com](https://dashboard.uxcam.com) to:

- View session recordings
- Analyze user behavior
- Set up funnels and goals
- Generate reports
- Configure alerts

## Support

For technical support:
- UXCam Documentation: [https://help.uxcam.com](https://help.uxcam.com)
- Flutter Plugin Issues: [https://github.com/uxcam/flutter-plugin](https://github.com/uxcam/flutter-plugin)

## Security Considerations

- UXCam recordings are encrypted in transit and at rest
- Sensitive data should be masked or excluded from recordings
- Implement proper user consent mechanisms
- Review UXCam's privacy policy and data handling practices
- Consider GDPR compliance requirements for EU users

## Integration Checklist

- [x] Add flutter_uxcam dependency to pubspec.yaml
- [x] Configure environment variables for UXCam app keys
- [x] Initialize UXCam in app startup flow
- [x] Install iOS pods
- [x] Create UXCamService helper class
- [ ] Replace placeholder app keys with actual keys from UXCam dashboard
- [ ] Test initialization and basic recording
- [ ] Implement user tagging in authentication flow
- [ ] Add screen tagging to important pages
- [ ] Implement custom event tracking for key user actions
- [ ] Test privacy controls for sensitive screens
- [ ] Update privacy policy to mention session recording
- [ ] Train team on UXCam dashboard usage

## Next Steps

1. **Get UXCam App Keys**: Sign up at uxcam.com and get your app keys
2. **Update Environment Files**: Replace placeholder keys with actual keys
3. **Test Integration**: Build and run the app to verify UXCam is working
4. **Implement Custom Tracking**: Add user tagging and event tracking
5. **Review Privacy**: Ensure compliance with privacy requirements
6. **Train Team**: Familiarize team with UXCam dashboard and features 