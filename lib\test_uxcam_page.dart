import 'package:flutter/material.dart';
import 'package:zod/core/services/uxcam_service.dart';

class TestUXCamPage extends StatefulWidget {
  const TestUXCamPage({super.key});

  @override
  State<TestUXCamPage> createState() => _TestUXCamPageState();
}

class _TestUXCamPageState extends State<TestUXCamPage> {
  String _status = 'Ready to test UXCam';
  bool _isRecording = false;

  @override
  void initState() {
    super.initState();
    UXCamService.tagScreen('UXCam Test Page');
    _checkRecordingStatus();
  }

  Future<void> _checkRecordingStatus() async {
    final recording = await UXCamService.isRecording();
    setState(() {
      _isRecording = recording;
      _status = recording ? 'UXCam is recording ✅' : 'UXCam not recording ❌';
    });
  }

  Future<void> _testUserSession() async {
    await UXCamService.tagUserSession(
      userId: 'test_user_123',
      userName: 'Test User',
      additionalProperties: {
        'app_version': '1.0.0',
        'test_mode': true,
        'platform': 'flutter',
      },
    );
    setState(() {
      _status = 'User session tagged! 👤';
    });
  }

  Future<void> _testLogEvent() async {
    await UXCamService.logEvent('test_button_clicked', {
      'button_name': 'Test Event Button',
      'timestamp': DateTime.now().toIso8601String(),
      'user_action': 'manual_test',
    });
    setState(() {
      _status = 'Event logged! 📊';
    });
  }

  Future<void> _testPauseResume() async {
    await UXCamService.pauseSession();
    setState(() {
      _status = 'Session paused ⏸️';
    });

    await Future.delayed(const Duration(seconds: 2));

    await UXCamService.resumeSession();
    setState(() {
      _status = 'Session resumed ▶️';
    });
  }

  Future<void> _getSessionUrl() async {
    final url = await UXCamService.getSessionUrl();
    setState(() {
      _status = url != null
          ? 'Session URL: ${url.substring(0, 50)}...'
          : 'No session URL';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('UXCam Integration Test'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              color: _isRecording ? Colors.green.shade50 : Colors.red.shade50,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Icon(
                      _isRecording ? Icons.videocam : Icons.videocam_off,
                      size: 48,
                      color: _isRecording ? Colors.green : Colors.red,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _status,
                      style: const TextStyle(
                          fontSize: 16, fontWeight: FontWeight.bold),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),
            const Text(
              'Test UXCam Functions:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _checkRecordingStatus,
              child: const Text('Check Recording Status'),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: _testUserSession,
              child: const Text('Test User Session Tagging'),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: _testLogEvent,
              child: const Text('Test Event Logging'),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: _testPauseResume,
              child: const Text('Test Pause/Resume'),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: _getSessionUrl,
              child: const Text('Get Session URL'),
            ),
            const SizedBox(height: 24),
            const Card(
              color: Colors.blue,
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Text(
                      'Instructions:',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      '1. Make sure UXCam is recording\n'
                      '2. Test each function\n'
                      '3. Check your UXCam dashboard\n'
                      '4. Look for events and user data',
                      style: TextStyle(color: Colors.white),
                      textAlign: TextAlign.left,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
