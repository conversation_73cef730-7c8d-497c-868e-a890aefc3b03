import 'package:json_annotation/json_annotation.dart';

part 'platform_version.g.dart';

@JsonSerializable()
class PlatformVersion {
  const PlatformVersion({
    required this.minVersion,
    required this.maxVersion,
    required this.messageAr,
    required this.messageEn,
  });
  @Json<PERSON>ey(name: 'min_version')
  final int minVersion;
  @JsonKey(name: 'max_version')
  final int maxVersion;
  @JsonKey(name: 'message_ar')
  final String messageAr;
  @JsonKey(name: 'message_en')
  final String messageEn;

  factory PlatformVersion.fromJson(Map<String, dynamic> json) =>
      _$PlatformVersionFromJson(json);

  Map<String, dynamic> toJson() => _$PlatformVersionToJson(this);
}
