<?php

namespace App\Filament\Resources;

use App\Enums\SubscriptionPlan;
use App\Enums\SubscriptionStatus;
use App\Filament\Actions\IsAdminAction;
use App\Filament\Actions\IsUserAction;
use App\Filament\Resources\SubscriptionResource\Pages;
use App\Filament\Resources\SubscriptionResource\RelationManagers;
use App\Models\Plan;
use App\Models\Subscription;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Tables\Actions\Action;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Model;

class SubscriptionResource extends Resource
{
    protected static ?string $model = Subscription::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $navigationGroup = 'Subscription Management';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('user_id')
                    ->label('User')
                    ->relationship('user', 'name')
                    ->searchable()
                    ->preload()
                    ->required(),
                Forms\Components\Select::make('product_id')
                    ->label('Product')
                    ->options([
                        'rydo_premium_monthly' => 'Premium Monthly ($9.99)',
                        'rydo_premium_3_months' => 'Premium 3 Months ($24.99)',
                        'rydo_premium_yearly' => 'Premium Yearly ($89.99)',
                    ])
                    ->required(),
                Forms\Components\DateTimePicker::make('starts_at')
                    ->label('Start Date')
                    ->default(now())
                    ->required(),
                Forms\Components\DateTimePicker::make('expires_at')
                    ->label('End Date')
                    ->required(),
                Forms\Components\Toggle::make('revenuecat_is_active')
                    ->label('Is Active')
                    ->default(true),
                Forms\Components\Toggle::make('revenuecat_is_trial_period')
                    ->label('Is Trial Period')
                    ->default(false),
                Forms\Components\TextInput::make('revenuecat_original_transaction_id')
                    ->label('Original Transaction ID')
                    ->maxLength(255),
                Forms\Components\TextInput::make('revenuecat_transaction_id')
                    ->label('Transaction ID')
                    ->maxLength(255),
                Forms\Components\Select::make('revenuecat_store')
                    ->label('Store')
                    ->options(collect([
                        'APP_STORE' => 'App Store',
                        'PLAY_STORE' => 'Google Play Store',
                        'MANUAL_DASHBOARD' => 'Manual Dashboard',
                    ])
                        ->toArray())
                    ->default('APP_STORE'),
                Forms\Components\Select::make('revenuecat_environment')
                    ->label('Environment')
                    ->options(collect([
                        'PRODUCTION' => 'Production',
                        'SANDBOX' => 'Sandbox',
                    ])
                        ->toArray())
                    ->default('PRODUCTION'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID')
                    ->sortable(),
                Tables\Columns\TextColumn::make('user.name')
                    ->label('User')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('product_id')
                    ->label('Product')
                    ->searchable(),
                Tables\Columns\TextColumn::make('status')
                    ->label('Status'),
                Tables\Columns\TextColumn::make('start_date')
                    ->label('Start Date')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('end_date')
                    ->label('End Date')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('revenuecat_store')
                    ->label('Store')
                    ->badge(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Action::make('send_to_webhook')
                    ->label('Send to Webhook')
                    ->icon('heroicon-o-paper-airplane')
                    ->color('success')
                    ->form([
                        Forms\Components\Select::make('event_type')
                            ->label('Event Type')
                            ->options([
                                'INITIAL_PURCHASE' => 'Initial Purchase',
                                'RENEWAL' => 'Renewal',
                                'PRODUCT_CHANGE' => 'Product Change',
                                'CANCELLATION' => 'Cancellation',
                                'EXPIRATION' => 'Expiration',
                                'BILLING_ISSUE' => 'Billing Issue',
                            ])
                            ->default('INITIAL_PURCHASE')
                            ->required(),
                        Forms\Components\TextInput::make('price')
                            ->label('Price')
                            ->numeric()
                            ->step(0.01)
                            ->default(fn($record) => match ($record->product_id) {
                                'rydo_premium_monthly' => 9.99,
                                'rydo_premium_3_months' => 24.99,
                                'rydo_premium_yearly' => 89.99,
                                default => 0
                            })
                            ->required(),
                        Forms\Components\Select::make('store')
                            ->label('Store')
                            ->options([
                                'APP_STORE' => 'App Store',
                                'PLAY_STORE' => 'Google Play Store',
                                'MANUAL_DASHBOARD' => 'Manual Dashboard',
                            ])
                            ->default('APP_STORE')
                            ->required(),
                    ])
                    ->action(function (array $data, Subscription $record): void {
                        self::sendWebhookEvent($record, $data);
                    }),
            ])
            ->headerActions([
                Action::make('create_subscription_webhook')
                    ->label('Create Subscription via Webhook')
                    ->icon('heroicon-o-plus')
                    ->color('primary')
                    ->form([
                        Forms\Components\Select::make('user_id')
                            ->label('User')
                            ->relationship('user', 'name', fn(Builder $query) => $query->orderBy('name'))
                            ->searchable()
                            ->preload()
                            ->required(),
                        Forms\Components\Select::make('product_id')
                            ->label('Product')
                            ->options([
                                'rydo_premium_monthly' => 'Premium Monthly ($9.99)',
                                'rydo_premium_3_months' => 'Premium 3 Months ($24.99)',
                                'rydo_premium_yearly' => 'Premium Yearly ($89.99)',
                            ])
                            ->reactive()
                            ->required(),
                        Forms\Components\Select::make('event_type')
                            ->label('Event Type')
                            ->options([
                                'INITIAL_PURCHASE' => 'Initial Purchase',
                                'RENEWAL' => 'Renewal',
                                'PRODUCT_CHANGE' => 'Product Change',
                                'CANCELLATION' => 'Cancellation',
                                'EXPIRATION' => 'Expiration',
                                'BILLING_ISSUE' => 'Billing Issue',
                            ])
                            ->default('INITIAL_PURCHASE')
                            ->required(),
                        Forms\Components\TextInput::make('price')
                            ->label('Price')
                            ->numeric()
                            ->step(0.01)
                            ->reactive()
                            ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                $productId = $get('product_id');
                                if (!$state && $productId) {
                                    $defaultPrice = match ($productId) {
                                        'rydo_premium_monthly' => 9.99,
                                        'rydo_premium_3_months' => 24.99,
                                        'rydo_premium_yearly' => 89.99,
                                        default => 0
                                    };
                                    $set('price', $defaultPrice);
                                }
                            })
                            ->required(),
                        Forms\Components\Select::make('store')
                            ->label('Store')
                            ->options([
                                'APP_STORE' => 'App Store',
                                'PLAY_STORE' => 'Google Play Store',
                                'MANUAL_DASHBOARD' => 'Manual Dashboard',
                            ])
                            ->default('APP_STORE')
                            ->required(),
                        Forms\Components\DateTimePicker::make('start_date')
                            ->label('Start Date')
                            ->default(now())
                            ->required(),
                        Forms\Components\DateTimePicker::make('end_date')
                            ->label('End Date')
                            ->default(function (callable $get) {
                                $productId = $get('product_id');
                                $days = match ($productId) {
                                    'rydo_premium_monthly' => 30,
                                    'rydo_premium_3_months' => 90,
                                    'rydo_premium_yearly' => 365,
                                    default => 30
                                };
                                return now()->addDays($days);
                            })
                            ->reactive()
                            ->required(),
                    ])
                    ->action(function (array $data): void {
                        self::createSubscriptionViaWebhook($data);
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSubscriptions::route('/'),
            'edit' => Pages\EditSubscription::route('/{record}/edit'),
        ];
    }

    protected static function sendWebhookEvent(Subscription $subscription, array $data): void
    {
        try {
            $webhookPayload = [
                'event' => [
                    'type' => $data['event_type'],
                    'app_user_id' => (string) $subscription->user_id,
                    'event_timestamp_ms' => now()->timestamp * 1000,
                    'product_id' => $subscription->product_id,
                    'period_type' => 'NORMAL',
                    'purchased_at_ms' => $subscription->starts_at?->timestamp * 1000 ?? now()->timestamp * 1000,
                    'expiration_at_ms' => $subscription->expires_at?->timestamp * 1000,
                    'environment' => 'PRODUCTION',
                    'entitlement_id' => null,
                    'entitlement_ids' => ['premium'],
                    'is_family_share' => false,
                    'country_code' => 'US',
                    'app_id' => config('app.name'),
                    'offer_code' => null,
                    'currency' => 'USD',
                    'price' => $data['price'],
                    'price_in_usd' => $data['price'],
                    'subscriber_attributes' => [],
                    'store' => $data['store'],
                    'takehome_percentage' => 0.7,
                    'transaction_id' => 'manual_' . uniqid(),
                    'original_transaction_id' => 'manual_' . uniqid(),
                    'presented_offering_id' => null,
                    'tax_percentage' => 0,
                    'commission_percentage' => 0.3,
                    'id' => uniqid(),
                    'aliases' => [],
                    'is_trial_conversion' => false,
                    'new_product_id' => $subscription->product_id,
                    'original_app_user_id' => (string) $subscription->user_id,
                ]
            ];

            $response = Http::post(url('/api/revenuecat/webhook'), $webhookPayload);

            if ($response->successful()) {
                Notification::make()
                    ->title('Webhook Event Sent Successfully')
                    ->success()
                    ->send();

                Log::info('Subscription webhook event sent successfully', [
                    'subscription_id' => $subscription->id,
                    'event_type' => $data['event_type'],
                    'user_id' => $subscription->user_id,
                ]);
            } else {
                Notification::make()
                    ->title('Failed to Send Webhook Event')
                    ->danger()
                    ->send();

                Log::error('Failed to send subscription webhook event', [
                    'subscription_id' => $subscription->id,
                    'response_status' => $response->status(),
                    'response_body' => $response->body(),
                ]);
            }
        } catch (\Exception $e) {
            Notification::make()
                ->title('Error Sending Webhook Event')
                ->danger()
                ->send();

            Log::error('Exception while sending subscription webhook event', [
                'subscription_id' => $subscription->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    protected static function createSubscriptionViaWebhook(array $data): void
    {
        try {
            // Convert string dates to Carbon instances if needed
            $startDate = is_string($data['start_date']) ? \Carbon\Carbon::parse($data['start_date']) : $data['start_date'];
            $endDate = is_string($data['end_date']) ? \Carbon\Carbon::parse($data['end_date']) : $data['end_date'];
            
            $webhookPayload = [
                'event' => [
                    'type' => $data['event_type'],
                    'app_user_id' => (string) $data['user_id'],
                    'event_timestamp_ms' => now()->timestamp * 1000,
                    'product_id' => $data['product_id'],
                    'period_type' => 'NORMAL',
                    'purchased_at_ms' => $startDate->timestamp * 1000,
                    'expiration_at_ms' => $endDate->timestamp * 1000,
                    'environment' => 'PRODUCTION',
                    'entitlement_id' => null,
                    'entitlement_ids' => ['premium'],
                    'is_family_share' => false,
                    'country_code' => 'US',
                    'app_id' => config('app.name'),
                    'offer_code' => null,
                    'currency' => 'USD',
                    'price' => $data['price'],
                    'price_in_usd' => $data['price'],
                    'subscriber_attributes' => [],
                    'store' => $data['store'],
                    'takehome_percentage' => 0.7,
                    'transaction_id' => 'manual_' . uniqid(),
                    'original_transaction_id' => 'manual_' . uniqid(),
                    'presented_offering_id' => null,
                    'tax_percentage' => 0,
                    'commission_percentage' => 0.3,
                    'id' => uniqid(),
                    'aliases' => [],
                    'is_trial_conversion' => false,
                    'new_product_id' => $data['product_id'],
                    'original_app_user_id' => (string) $data['user_id'],
                ]
            ];

            $response = Http::post(url('/api/revenuecat/webhook'), $webhookPayload);

            if ($response->successful()) {
                Notification::make()
                    ->title('Subscription Created Successfully via Webhook')
                    ->success()
                    ->send();

                Log::info('Subscription created via webhook successfully', [
                    'user_id' => $data['user_id'],
                    'product_id' => $data['product_id'],
                    'event_type' => $data['event_type'],
                ]);
            } else {
                Notification::make()
                    ->title('Failed to Create Subscription via Webhook')
                    ->danger()
                    ->send();

                Log::error('Failed to create subscription via webhook', [
                    'user_id' => $data['user_id'],
                    'response_status' => $response->status(),
                    'response_body' => $response->body(),
                ]);
            }
        } catch (\Exception $e) {
            Notification::make()
                ->title('Error Creating Subscription via Webhook')
                ->danger()
                ->send();

            Log::error('Exception while creating subscription via webhook', [
                'user_id' => $data['user_id'],
                'error' => $e->getMessage(),
            ]);
        }
    }

    public static function canViewAny(): bool
    {
        return IsAdminAction::handle() || IsUserAction::handle();
    }

    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery()->orderBy('created_at', 'desc');

        // If user is not admin, only show their own subscriptions
        if (!IsAdminAction::handle() && IsUserAction::handle()) {
            $query->where('user_id', IsUserAction::getUserId());
        }

        return $query;
    }

    public static function canCreate(): bool
    {
        return false;
    }

    public static function canUpdate(): bool
    {
        return false;
    }

    public static function canEdit(Model $record): bool
    {
        return IsAdminAction::handle() || IsUserAction::handle();
    }
}
