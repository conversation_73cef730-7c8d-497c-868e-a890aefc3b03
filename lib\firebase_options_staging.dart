// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options_staging.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBLLbGUsCyPReodUJZ6GIKNzehzJ0OQCnE',
    appId: '1:1060161913171:web:68b14b55cce605b655a1a6',
    messagingSenderId: '1060161913171',
    projectId: 'flutter-bond-staging',
    authDomain: 'flutter-bond-staging.firebaseapp.com',
    storageBucket: 'flutter-bond-staging.appspot.com',
    measurementId: 'G-FF63T3YF4K',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyCGyc2yHmyoKNDS8ePior9rfigEgZe0Rzw',
    appId: '1:846434674360:android:ca0f4a66d53b66fad9764d',
    messagingSenderId: '846434674360',
    projectId: 'zod-staging-93107',
    storageBucket: 'zod-staging-93107.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyACuEg8UdSiC45eD6WFB3OH0C71M9DkWyE',
    appId: '1:846434674360:ios:953815b77eb8bee6d9764d',
    messagingSenderId: '846434674360',
    projectId: 'zod-staging-93107',
    storageBucket: 'zod-staging-93107.firebasestorage.app',
    iosBundleId: 'sa.zod.app.staging',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyCz95QyOlPlhPWZFUrduUsS5Q8g5boiQgc',
    appId: '1:390157281889:ios:ba7ec93993371072ab9820',
    messagingSenderId: '390157281889',
    projectId: 'flutter-bond-staging-7dd42',
    storageBucket: 'flutter-bond-staging-7dd42.appspot.com',
    iosBundleId: 'ps.app.flutterBond.staging',
  );
}
