import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/core/widgets/title_app_bar.dart';
import 'package:zod/features/auction/presentation/providers/add_address_controller_provider.dart';
import 'package:zod/features/auction/presentation/providers/map_location_provider.dart';
import 'package:zod/features/auction/presentation/views/address_form_view.dart';
import 'package:zod/features/auction/presentation/views/map_view.dart';
import 'package:zod/features/auction/presentation/views/save_address_button_view.dart';
import 'package:zod/features/auction/presentation/views/shimmer_add_address_view.dart';

class AddAddressPage extends ConsumerStatefulWidget {
  const AddAddressPage({super.key, this.auctionId});

  final int? auctionId;
  static const String route = '/add-address';
  @override
  ConsumerState<AddAddressPage> createState() => _AddAddressPageState();
}

class _AddAddressPageState extends ConsumerState<AddAddressPage> {
  @override
  void initState() {
    super.initState();
    // Add a post-frame callback to initialize the controller after the first build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Initialize the controller
      final controller = ref.read(addAddressControllerProvider.notifier);

      // Set the custom info window content first
      // This is now just a placeholder since we create a new instance
      // in _createInfoWindowContent
      controller.setCustomInfoWindowContent(
        () => Container(), // Empty container as placeholder
      );

      // Then initialize the address controller
      controller.initialize();

      // Force marker to stay visible after shimmer effect
      Future.delayed(const Duration(seconds: 3), () {
        // Get the map provider and force update the marker
        final mapProvider = ref.read(mapLocationProvider.notifier);
        final mapState = ref.read(mapLocationProvider);

        if (mapState.currentLocation != null) {
          // Force update the marker position to ensure it stays visible
          mapProvider.updateMarkerPosition(mapState.currentLocation!);
        } else {
          // Use default location if no current location
          mapProvider.updateMarkerPosition(const LatLng(24.7136, 46.6753));
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    final addressState = ref.watch(addAddressControllerProvider);

    return Scaffold(
      resizeToAvoidBottomInset: true, // نخليها true لتتحرك الشاشة مع الكيبورد
      appBar: TitleAppBar(title: context.localizations.add_address),
      bottomNavigationBar: addressState.isLoading
          ? const ShimmerSaveAddressButtonView()
          : SaveAddressButtonView(auctionId: widget.auctionId),
      body: SafeArea(
        child: LayoutBuilder(
          builder: (context, constraints) {
            if (addressState.isLoading) {
              return const ShimmerAddAddressView();
            }

            return Column(
              children: [
                // Map container with fixed height and no scrolling
                Container(
                  height: constraints.maxHeight * 0.45,
                  width: double.infinity,
                  margin: const EdgeInsets.only(bottom: 8),
                  // Use ClipRRect to ensure the map doesn't overflow its container
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: const MapView(),
                  ),
                ),
                // Form in scrollable container
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.only(bottom: 16),
                    child: const AddressFormView(),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }
}
