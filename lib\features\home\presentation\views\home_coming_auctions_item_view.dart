import 'dart:developer';

import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:zod/app/routes.dart';
import 'package:zod/core/localizations/app_localizations_extension.dart';
import 'package:zod/core/resources/app_colors.dart';
import 'package:zod/core/widgets/number_coins_view.dart';
import 'package:zod/features/auction/data/models/auction.dart';
import 'package:zod/features/auction/presentation/views/auction_details/category_badge_view.dart';
import 'package:zod/features/auction/presentation/views/auction_status_label.dart';
import 'package:zod/features/auction/presentation/views/footer_auction_item_view.dart';
import 'package:zod/features/auction/presentation/views/count_down_timer_view.dart';

import '../../../../core/widgets/network_image_widget.dart';

class HomeComingAuctionsItemView extends StatelessWidget {
  const HomeComingAuctionsItemView({
    super.key,
    required this.auction,
    this.totalItems,
    this.index,
  });

  final Auction auction;
  final int? totalItems;
  final int? index;

  @override
  Widget build(BuildContext context) {
    // Determine if this is the only item in the list
    final isSingleItem = totalItems == 1;

    // Calculate screen width
    final screenWidth = MediaQuery.of(context).size.width;

    log("isSingleItem $isSingleItem");
    // Set width based on number of items
    final width = isSingleItem
        ? screenWidth -
            32 // Full width minus start padding (16) and end padding (16)
        : screenWidth * 0.85; // 85% width for multiple items

    return InkWell(
      onTap: () =>
          goRouter.push('/auction/${auction.id}', extra: {'from_home': true}),
      child: Container(
        width: width,
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(15),
          border: Border.all(color: AppColors.softSteel1),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Stack(
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 12),
                      Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(15),
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(15),
                          child: Stack(
                            children: [
                              NetworkImageView(
                                auction.image ?? '',
                                height: 170,
                                width: double.infinity,
                                radius: 10,
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            children: [
                              Text(
                                context.localizations.start_bidding,
                                style: context.textTheme.labelMedium,
                              ),
                              const SizedBox(width: 4),
                              NumberCoinsView(
                                textColor: AppColors.goldenBrown,
                                number: auction.openBidPoints.toString(),
                              ),
                            ],
                          ),
                          Row(
                            children: [
                              if (auction.endedAt != null && auction.isLive)
                                Skeleton.ignore(
                                  child: CountdownTimerView(
                                    title: context.localizations.time_left,
                                    targetTime:
                                        auction.endedAt ?? DateTime.now(),
                                  ),
                                ),
                              if (auction.startAt != null && auction.isUpComing)
                                Skeleton.ignore(
                                  child: CountdownTimerView(
                                    title: context.localizations.start_in,
                                    targetTime:
                                        auction.startAt ?? DateTime.now(),
                                  ),
                                ),
                              const SizedBox(width: 4),
                              if ((auction.showBadgeNestedStartTimer &&
                                      auction.isUpComing) ||
                                  auction.showBadgeNestedEndTimer)
                                AuctionStatusLabel(status: auction.status),
                            ],
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      SizedBox(
                        height: 48,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              auction.name,
                              style: context.textTheme.titleSmall,
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                PositionedDirectional(
                  end: 24,
                  top: 24,
                  child: CategoryBadgeView(title: auction.category?.name ?? ''),
                ),
              ],
            ),
            const SizedBox(height: 6),
            Divider(color: AppColors.softSteel1, thickness: 1),
            FooterAuctionItemView(
              auction: auction,
              isHome: true,
            ),
          ],
        ),
      ),
    );
  }
}
