/// Enum defining different types of payments in the application
enum PaymentType {
  /// General wallet charging not related to any specific auction
  pointPurchase('point_purchase'),

  /// Payment for auction entry fee to join an auction
  entryFee('entry_fee'),

  /// Payment for placing bids in an auction (when user runs out of points)
  bidPlace('bid_place'),

  /// Payment for prize purchase after winning an auction
  prizePurchase('prize_purchase'),

  /// Payment for subscription or premium features
  subscription('subscription');

  const PaymentType(this.value);

  final String value;

  /// Convert payment type to string for analytics
  @override
  String toString() => value;

  /// Get payment type from auction context
  static PaymentType fromAuctionContext({
    required bool isAuctionRelated,
    bool isEntryFee = false,
    bool isBidding = false,
    bool isPrize = false,
  }) {
    if (!isAuctionRelated) return PaymentType.pointPurchase;

    if (isPrize) return PaymentType.prizePurchase;
    if (isBidding) return PaymentType.bidPlace;
    if (isEntryFee) return PaymentType.entryFee;

    // Default to entry fee for auction context
    return PaymentType.entryFee;
  }
}
