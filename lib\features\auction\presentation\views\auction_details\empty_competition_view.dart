import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/core/app_theme.dart';
import 'package:zod/core/resources/app_colors.dart';
import 'package:zod/core/resources/app_icons.dart';
import 'package:zod/features/auction/data/models/auction.dart';
import 'package:zod/features/auction/presentation/utils/auction_date_formatter.dart';
import 'package:zod/features/auction/presentation/views/auction_details/auction_start_time_view.dart';

class EmptyCompetitionView extends StatelessWidget {
  final Auction auction;
  const EmptyCompetitionView({required this.auction, super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: AppColors.white,
      child: SingleChildScrollView(
        child: Column(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(height: 8),
                SvgPicture.asset(AppIcons.emptyCompetition),
                SizedBox(height: 5),
                Text(
                    (auction.isJoined ?? false)
                        ? auction.showTimer
                            ? context.localizations.the_auction_will_start_soon
                            : context.localizations.you_have_joined_this_auction
                        : context.localizations.join_auction_and_start_biding,
                    style: context.textTheme.titleSmall?.w700),
                SizedBox(height: 4),
                Visibility(
                    visible: auction.showTimer,
                    replacement: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0),
                      child: RichText(
                        textAlign: TextAlign.center,
                        text: TextSpan(
                          text: context.localizations.auction_will_starts_at,
                          style: context.textTheme.bodyMedium,
                          children: <TextSpan>[
                            TextSpan(
                                text: " ${auction.startAtTime ?? ""} ",
                                style: context
                                    .textTheme.bodyMedium?.royalBlue.w700),
                            if (!auction.isFullySubscribed)
                              TextSpan(
                                text: context.localizations
                                    .on_day_after_bidders_completed,
                              ),
                            if (auction.isFullySubscribed)
                              TextSpan(
                                text: "\n${context.localizations.on}",
                              ),
                            if (auction.isFullySubscribed)
                              TextSpan(
                                  text: " ${auction.getLocalizedStartDate()} ",
                                  style: context.textTheme.bodyMedium?.w700),
                          ],
                        ),
                      ),
                    ),
                    child: AuctionStartTimeView(auction: auction)),
                if (!(auction.isJoined ?? false)) SizedBox(height: 16),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
