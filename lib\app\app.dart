import 'package:bond_core/bond_core.dart';
import 'package:zod/features/invoice/invoice_service_provider.dart';
import 'package:zod/features/notification/notification_service_provider.dart';
import 'package:zod/providers/notifications_service_provider.dart';
import 'package:zod/features/onboarding/onboarding_service_provider.dart';

import '../features/auction/auction_service_provider.dart';
import '../features/auth/auth_service_provider.dart';
import '../features/home/<USER>';
import '../features/more/more_service_provider.dart';
import '../features/payment/payment_service_provider.dart';
import '../features/wallet/wallet_service_provider.dart';
import '../providers/analytics_service_provider.dart';
import '../providers/api_service_provider.dart';
import '../providers/app_service_provider.dart';
import '../providers/cache_service_provider.dart';
import '../providers/firebase_service_provider.dart';
import '../providers/forms_service_provider.dart';
import '../providers/meta_sdk_service_provider.dart';

final List<ServiceProvider> providers = [
  // Framework Service Providers
  FirebaseServiceProvider(),
  AppServiceProvider(),
  CacheServiceProvider(),
  FormsServiceProvider(),
  AuthServiceProvider(),
  ApiServiceProvider(),
  AnalyticsServiceProvider(),
  NotificationsServiceProvider(),
  MetaSdkServiceProvider(),

  // Modules Service Providers
  MoreServiceProvider(),
  WalletServiceProvider(),
  AuctionServiceProvider(),
  HomeServiceProvider(),
  PaymentServiceProvider(),
  InvoiceServiceProvider(),
  NotificationServiceProvider(),
  OnboardingServiceProvider(),
];
