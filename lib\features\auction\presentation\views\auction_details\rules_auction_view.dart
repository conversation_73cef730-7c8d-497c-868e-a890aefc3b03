import 'dart:developer';

import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/core/app_theme.dart';
import 'package:zod/core/resources/app_colors.dart';
import 'package:zod/core/resources/app_icons.dart';
import 'package:zod/features/auction/data/models/auction.dart';
import 'package:html/parser.dart' as html_parser;
import 'package:html/dom.dart' as dom;
import 'competition_item_view.dart';
import 'package:zod/features/auction/presentation/widgets/entry_fee_display_widget.dart';

class RulesAuctionView extends StatelessWidget {
  RulesAuctionView({super.key, required this.auction});

  final Auction auction;

  @override
  Widget build(BuildContext context) {
    final sections = splitHtmlByH2(auction.rules ?? '');

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16),
      color: AppColors.white,
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 16),
            Text(context.localizations.competition_details,
                style: context.textTheme.titleSmall?.w700),
            SizedBox(height: 16),
            Visibility(
              visible: auction.enableFee != null,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CompetitionItemView(
                      title: context.localizations.participations_fees,
                      child: auction.buildEntryFeeDisplay(
                        style: context.textTheme.labelLarge,
                        discountedStyle:
                            context.textTheme.labelMedium?.royalBlue.w700,
                        hideCurrency: true,
                        strikethroughStyle:
                            context.textTheme.labelMedium?.copyWith(
                          decoration: TextDecoration.lineThrough,
                          decorationStyle: TextDecorationStyle.solid,
                          color: AppColors.secondaryColor,
                        ),
                      )),
                  SizedBox(height: 12),
                  Divider(color: AppColors.softSilver, height: 3),
                  SizedBox(height: 12),
                ],
              ),
            ),
            CompetitionItemView(
                title: context.localizations.open_price,
                value: auction.openBidPoints.toString()),
            SizedBox(height: 12),
            Divider(color: AppColors.softSilver, height: 3),
            SizedBox(height: 12),
            CompetitionItemView(
                title: context.localizations.increment_bid,
                value: auction.incrementBidPoints.toString()),
            SizedBox(height: 12),
            Divider(color: AppColors.softSilver, height: 3),
            SizedBox(height: 12),
            CompetitionItemView(
                title: context.localizations.maximum_bid_point,
                value: auction.maxBidPoints.toString()),
            SizedBox(height: 32),
            Text(context.localizations.auction_rules,
                style: context.textTheme.titleSmall?.w700),
            Container(
              color: AppColors.white,
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: sections.asMap().entries.map((entry) {
                    int index = entry.key;
                    String section = entry.value;
                    final sectionData = extractHeadingAndContent(section);
                    final String svgPath =
                        index < svgIcons.length ? svgIcons[index] : defaultSvg;
                    log("svgPath $svgPath");
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(height: 18),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            SvgPicture.asset(svgPath),
                            SizedBox(width: 10),
                            Text(sectionData['heading'],
                                style: context.textTheme.labelLarge),
                          ],
                        ),
                        Transform.translate(
                          offset: Offset(0, -5),
                          child: Html(
                            data: sectionData['content'],
                            style: {
                              "p": Style(
                                color: AppColors.slateGray,
                                fontSize: FontSize(14),
                                fontWeight: FontWeight.w400,
                                margin: Margins(bottom: Margin(0)),
                              ),
                              "strong": Style(
                                color: AppColors.textColor,
                                fontWeight: FontWeight.w700,
                              ),
                              "b": Style(
                                color: AppColors.textColor,
                                fontWeight: FontWeight.w700,
                              ),
                            },
                          ),
                        ),
                      ],
                    );
                  }).toList(),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<String> splitHtmlByH2(String htmlString) {
    final document = html_parser.parse(htmlString);
    final List<String> sections = [];
    List<dom.Node> currentSectionNodes = [];

    for (var node in document.body!.nodes) {
      // Check if the node is an element and its tag is <h2> or <h3>
      if (node is dom.Element &&
          (node.localName == 'h2' || node.localName == 'h3')) {
        // If we have nodes in the current section, convert them to HTML and add to sections
        if (currentSectionNodes.isNotEmpty) {
          final sectionHtml = currentSectionNodes
              .map((n) => n is dom.Element ? n.outerHtml : (n.text ?? ''))
              .join('');
          sections.add(sectionHtml);
          currentSectionNodes = [];
        }
      }
      currentSectionNodes.add(node);
    }

    // Add the last section if it exists
    if (currentSectionNodes.isNotEmpty) {
      final sectionHtml = currentSectionNodes
          .map((n) => n is dom.Element ? n.outerHtml : (n.text ?? ''))
          .join('');
      sections.add(sectionHtml);
    }

    return sections;
  }

  final List<String> svgIcons = [
    AppIcons.num1,
    AppIcons.num2,
    AppIcons.num3,
  ];

  final String defaultSvg = AppIcons.info;

  Map<String, dynamic> extractHeadingAndContent(String section) {
    final document = html_parser.parse(section);
    String headingText = '';
    String remainingContent = '';
    bool foundHeading = false;

    for (var node in document.body!.nodes) {
      if (!foundHeading &&
          node is dom.Element &&
          (node.localName == 'h2' || node.localName == 'h3')) {
        headingText = node.text; // Extract the text inside <h2> or <h3>
        foundHeading = true;
      } else {
        remainingContent +=
            node is dom.Element ? node.outerHtml : (node.text ?? '');
      }
    }

    return {
      'heading': headingText,
      'content': remainingContent,
    };
  }
}
