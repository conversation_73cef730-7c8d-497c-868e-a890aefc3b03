import 'dart:async';
import 'dart:developer';

import 'package:flutter_paytabs_bridge/PaymentSDKNetworks.dart';
import 'package:flutter_paytabs_bridge/PaymentSdkTransactionType.dart';
import 'package:flutter_paytabs_bridge/flutter_paytabs_bridge.dart';
import 'package:flutter_paytabs_bridge/BaseBillingShippingInfo.dart';
import 'package:flutter_paytabs_bridge/PaymentSdkConfigurationDetails.dart';
import 'package:flutter_paytabs_bridge/PaymentSdkLocale.dart';
import 'package:flutter_paytabs_bridge/PaymentSdkTokeniseType.dart';
import 'package:flutter_paytabs_bridge/PaymentSdkTokenFormat.dart';
import 'package:flutter_paytabs_bridge/PaymentSDKSavedCardInfo.dart';
import 'package:zod/features/auth/auth.dart';
import 'package:zod/features/payment/data/api.dart';
import 'package:zod/features/payment/data/models/payment_initialization.dart';
import 'package:zod/features/wallet/data/models/wallet_charge_dto.dart';

class PaymentService {
  // Configuration details
  final String profileId;
  final String serverKey;
  final String clientKey;
  final String merchantName;
  final String merchantCountryCode;
  final String merchantApplePayIdentifier;
  final PaymentApi paymentApi;

  PaymentService({
    required this.profileId,
    required this.serverKey,
    required this.clientKey,
    required this.merchantName,
    required this.merchantCountryCode,
    required this.merchantApplePayIdentifier,
    required this.paymentApi,
  });

  PaymentSdkConfigurationDetails _createBaseConfiguration({
    required double amount,
    required String currency,
    required String cartDescription,
    required String cartId,
    BillingDetails? billingDetails,
    bool enableTokenization = true,
  }) {
    log("profile ID $profileId");
    log("server key $serverKey");
    log("client key $clientKey");
    log("merchant name $merchantName");
    // Create configuration
    var configuration = PaymentSdkConfigurationDetails(
      transactionType: PaymentSdkTransactionType.SALE,
      profileId: profileId,
      serverKey: serverKey,
      clientKey: clientKey,
      cartId: cartId,
      cartDescription: cartDescription,
      merchantName: "Arabian Information Technology Co.",
      screentTitle: "Payment",
      showBillingInfo: false,
      showShippingInfo: false,
      billingDetails: billingDetails,
      locale: PaymentSdkLocale.EN,
      amount: amount,
      currencyCode: "SAR",
      merchantCountryCode: "SA",
    );

    // Enable tokenization if requested
    if (enableTokenization) {
      configuration.tokeniseType = PaymentSdkTokeniseType.MERCHANT_MANDATORY;
      configuration.tokenFormat = PaymentSdkTokenFormat.AlphaNum20Format;
    }

    // Set iOS theme
    // var theme = IOSThemeConfigurations();
    // theme.primaryColor = "4e1d80"; // Primary color (hex)
    // theme.primaryFontColor = "ffffff"; // White text
    // // theme.logoImage = "assets/images/logo.png"; // App logo
    // configuration.iOSThemeConfigurations = theme;

    return configuration;
  }

  BillingDetails _createBillingDetails() {
    final user = Auth.user();

    return BillingDetails(
      user.name ?? "Customer${user.id}",
      user.email ?? "user_${user.id}@zod.sa",
      user.phone, // state
      user.countryCode, // country code
      "SA", // country
      "Riyadh", // city
      "Riyadh", // state
      "0000", // street
    );
  }

  Future<Map<String, dynamic>> startCardPayment({
    required double amount,
    required String currency,
    required String cartDescription,
    required WalletChargeDto chargeDto,
  }) async {
    // Initialize payment with backend

    log("startCardPayment amount: $amount");
    log("startCardPayment currency: $currency");
    log("startCardPayment cartDescription: $cartDescription");
    log("startCardPayment chargeDto: ${chargeDto.toJson()}");

    final PaymentInitialization initialization =
        await paymentApi.initializePayment(chargeDto);

    log("startCardPayment initialization: ${initialization.toJson()}");
    // Create billing details
    final billingDetails = _createBillingDetails();

    log("startCardPayment using amount: $amount");
    // Create configuration with the amount passed from frontend (already calculated correctly)
    final configuration = _createBaseConfiguration(
      amount: amount,
      currency: currency,
      cartDescription: cartDescription,
      cartId: initialization.transactionId,
      billingDetails: billingDetails,
    );

    // Create completer to handle async result
    final completer = Completer<Map<String, dynamic>>();

    // Start payment
    FlutterPaytabsBridge.startCardPayment(configuration, (event) {
      // Add transaction ID to the result
      final result = Map<String, dynamic>.from(event);
      log("result $result");

      result['transaction_id'] = initialization.transactionId;
      completer.complete(result);
    });

    return completer.future;
  }

  Future<Map<String, dynamic>> startApplePayPayment({
    required double amount,
    required String currency,
    required String cartDescription,
    required WalletChargeDto chargeDto,
  }) async {
    // Initialize payment with backend
    final initialization = await paymentApi.initializePayment(chargeDto);
    final billingDetails = _createBillingDetails();

    // Create configuration with the amount passed from frontend (already calculated correctly)
    final configuration = _createBaseConfiguration(
        amount: amount,
        currency: currency,
        cartDescription: cartDescription,
        cartId: initialization.transactionId,
        billingDetails: billingDetails,
        enableTokenization: false);

    // Set Apple Pay specific configuration
    configuration.merchantApplePayIndentifier = merchantApplePayIdentifier;
    configuration.simplifyApplePayValidation = true;

    final configuration2 = PaymentSdkConfigurationDetails(
      profileId: profileId, //"109484",
      serverKey: serverKey, //"SWJNGZM9JD-JHWWNWLTWK-BHBBJJ26K6",
      clientKey: clientKey, //"CKKMN6-DNPK6H-VVQVB7-K92TRB",
      cartId: initialization.transactionId,
      cartDescription: cartDescription,
      merchantName: "Arabian Information Technology Co.",
      amount: amount,
      currencyCode: "SAR",
      merchantCountryCode: "SA",
      billingDetails: billingDetails,
      merchantApplePayIndentifier: merchantApplePayIdentifier,
      simplifyApplePayValidation: true,
      paymentNetworks: [PaymentSDKNetworks.visa, PaymentSDKNetworks.amex],
    );

    // Create completer to handle async result
    final completer = Completer<Map<String, dynamic>>();

    // Start Apple Pay payment
    FlutterPaytabsBridge.startApplePayPayment(configuration2, (event) {
      // Add transaction ID to the result

      final result = Map<String, dynamic>.from(event);
      log("result $result");
      result['transaction_id'] = initialization.transactionId;
      completer.complete(result);
    });

    return completer.future;
  }

  Future<Map<String, dynamic>> startTokenizedCardPayment({
    required double amount,
    required String currency,
    required String cartDescription,
    required WalletChargeDto chargeDto,
    required String cardToken,
    required String transactionReference,
  }) async {
    // Initialize payment with backend
    final initialization = await paymentApi.initializePayment(chargeDto);

    // Create billing details
    final billingDetails = _createBillingDetails();

    // Create configuration with the amount passed from frontend (already calculated correctly)
    final configuration = _createBaseConfiguration(
      amount: amount,
      currency: currency,
      cartDescription: cartDescription,
      cartId: initialization.transactionId,
      billingDetails: billingDetails,
    );

    // Create completer to handle async result
    final completer = Completer<Map<String, dynamic>>();

    // Start tokenized payment
    FlutterPaytabsBridge.startTokenizedCardPayment(
        configuration, cardToken, transactionReference, (event) {
      // Add transaction ID to the result
      final result = Map<String, dynamic>.from(event);
      log("tokenized payment result $result");

      result['transaction_id'] = initialization.transactionId;
      completer.complete(result);
    });

    return completer.future;
  }

  /// Start a 3D secure tokenized card payment that only requires CVV
  /// This is used for saved cards where the user only needs to enter the CVV
  Future<Map<String, dynamic>> start3DSecureTokenizedCardPayment({
    required double amount,
    required String currency,
    required String cartDescription,
    required WalletChargeDto chargeDto,
    required String cardToken,
    required String cardMask,
    required String cardType,
  }) async {
    // Initialize payment with backend
    final initialization = await paymentApi.initializePayment(chargeDto);

    // Create billing details
    final billingDetails = _createBillingDetails();

    // Create configuration with the amount passed from frontend (already calculated correctly)
    final configuration = _createBaseConfiguration(
      amount: amount,
      currency: currency,
      cartDescription: cartDescription,
      cartId: initialization.transactionId,
      billingDetails: billingDetails,
    );

    // Create saved card info object
    final savedCardInfo = PaymentSDKSavedCardInfo(cardMask, cardType);

    // Create completer to handle async result
    final completer = Completer<Map<String, dynamic>>();

    // Start 3D secure tokenized payment
    FlutterPaytabsBridge.start3DSecureTokenizedCardPayment(
        configuration, savedCardInfo, cardToken, (event) {
      // Add transaction ID to the result
      final result = Map<String, dynamic>.from(event);
      log("3D secure tokenized payment result $result");

      result['transaction_id'] = initialization.transactionId;
      completer.complete(result);
    });

    return completer.future;
  }
}
