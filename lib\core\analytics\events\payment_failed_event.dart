import 'package:bond_app_analytics/bond_app_analytics.dart';

class PaymentFailedEvent extends AnalyticsEvent {
  final double amount;
  final String currency;
  final String paymentMethod;
  final int pointsPurchased;
  final String errorMessage;
  final String? auctionId;
  final String? auctionName;
  final String? transactionId;
  final String paymentType; // point_purchase, entry_fee, bid_place, etc.
  final int userId;

  PaymentFailedEvent({
    required this.amount,
    required this.currency,
    required this.paymentMethod,
    required this.pointsPurchased,
    required this.errorMessage,
    this.auctionId,
    this.auctionName,
    this.transactionId,
    required this.paymentType,
    required this.userId,
  });

  @override
  String get key => 'payment_failed';

  @override
  Map<String, dynamic> get params => {
        'amount': amount,
        'currency': currency,
        'payment_method': paymentMethod,
        'payment_type': paymentType,
        'points_to_purchase': pointsPurchased,
        'error_message': errorMessage,
        'auction_id': auctionId,
        'auction_name': auctionName,
        'transaction_id': transactionId,
        'user_id': userId,
        'timestamp': DateTime.now().toIso8601String(),
      };
}
