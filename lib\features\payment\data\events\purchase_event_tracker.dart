import 'dart:developer';

import 'package:bond_app_analytics/bond_app_analytics.dart';
import 'package:zod/core/analytics/events/events.dart';
import 'package:zod/core/app_analytics.dart';
import 'package:zod/features/auth/auth.dart';

/// Track when a user successfully completes a purchase
void trackPurchase({
  required String transactionId,
  required double amount,
  required double taxAmount,
  required String currency,
  required String paymentMethod,
  required int pointsPurchased,
  required PaymentType paymentType,
  int? userId,
  String? auctionId,
  String? auctionName,
}) {
  // Get user ID from Auth if not provided
  final currentUserId = userId ?? Auth.user().id;

  // Debug logging to help identify missing auction data
  if (auctionId == null) {
    log('Purchase event: ${paymentType.value} - no auction context (User: $currentUserId)',
        name: 'PurchaseTracker');
  } else {
    log('Purchase event: ${paymentType.value} - auction context ID: $auctionId, Name: $auctionName (User: $currentUserId)',
        name: 'PurchaseTracker');
  }

  // Create purchase item for analytics
  final purchaseItem = EventItem(
    itemId: 'wallet_points',
    itemName: 'Wallet Points',
    itemCategory: 'Digital Currency',
    quantity: pointsPurchased,
    price: amount,
    currency: currency,
  );

  AppAnalytics.fire(PurchaseEvent(
    purchaseTransactionId: transactionId,
    amount: amount,
    taxAmount: taxAmount,
    purchaseCurrency: currency,
    paymentMethod: paymentMethod,
    pointsPurchased: pointsPurchased,
    auctionId: auctionId,
    auctionName: auctionName,
    purchaseItems: [purchaseItem],
    paymentType: paymentType.value,
    userId: currentUserId,
  ));

  log('Purchase event tracked: $transactionId, amount: $amount, points: $pointsPurchased, method: $paymentMethod, type: ${paymentType.value}, user: $currentUserId',
      name: 'PurchaseTracker');
}

/// Track when a user initiates a payment process
void trackPaymentInitiated({
  required double amount,
  required String currency,
  required String paymentMethod,
  required int pointsPurchased,
  required PaymentType paymentType,
  int? userId,
  String? auctionId,
  String? auctionName,
}) {
  // Get user ID from Auth if not provided
  final currentUserId = userId ?? Auth.user().id;

  // Debug logging to help identify auction context
  if (auctionId == null) {
    log('Payment initiated: ${paymentType.value} - no auction context (User: $currentUserId)',
        name: 'PurchaseTracker');
  } else {
    log('Payment initiated: ${paymentType.value} - auction context ID: $auctionId, Name: $auctionName (User: $currentUserId)',
        name: 'PurchaseTracker');
  }

  AppAnalytics.fire(PaymentInitiatedEvent(
    amount: amount,
    currency: currency,
    paymentMethod: paymentMethod,
    pointsPurchased: pointsPurchased,
    auctionId: auctionId,
    auctionName: auctionName,
    paymentType: paymentType.value,
    userId: currentUserId,
  ));

  log('Payment initiated event tracked: amount: $amount, points: $pointsPurchased, method: $paymentMethod, type: ${paymentType.value}, user: $currentUserId',
      name: 'PurchaseTracker');
}

/// Track when a payment fails
void trackPaymentFailed({
  required double amount,
  required String currency,
  required String paymentMethod,
  required int pointsPurchased,
  required String errorMessage,
  required PaymentType paymentType,
  int? userId,
  String? auctionId,
  String? auctionName,
  String? transactionId,
}) {
  // Get user ID from Auth if not provided
  final currentUserId = userId ?? Auth.user().id;

  // Debug logging to help identify auction context
  if (auctionId == null) {
    log('Payment failed: ${paymentType.value} - no auction context (User: $currentUserId)',
        name: 'PurchaseTracker');
  } else {
    log('Payment failed: ${paymentType.value} - auction context ID: $auctionId, Name: $auctionName (User: $currentUserId)',
        name: 'PurchaseTracker');
  }

  AppAnalytics.fire(PaymentFailedEvent(
    amount: amount,
    currency: currency,
    paymentMethod: paymentMethod,
    pointsPurchased: pointsPurchased,
    errorMessage: errorMessage,
    auctionId: auctionId,
    auctionName: auctionName,
    transactionId: transactionId,
    paymentType: paymentType.value,
    userId: currentUserId,
  ));

  log('Payment failed event tracked: amount: $amount, points: $pointsPurchased, method: $paymentMethod, type: ${paymentType.value}, error: $errorMessage, user: $currentUserId',
      name: 'PurchaseTracker');
}
