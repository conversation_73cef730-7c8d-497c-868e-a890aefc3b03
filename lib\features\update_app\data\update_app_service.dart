import 'dart:convert';
import 'dart:developer';

import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/material.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:bond_core/bond_core.dart';

import '../../../app/routes.dart';
import 'models/current_version.dart';
import 'models/platform_version.dart';

class UpdateAppService {
  UpdateAppService({required this.remoteConfig, required this.packageInfo});

  final FirebaseRemoteConfig remoteConfig;
  final PackageInfo packageInfo;

  // Flag to track if force update is active
  static bool _isForceUpdateActive = false;
  static bool get isForceUpdateActive => _isForceUpdateActive;

  void call() async {
    try {
      // Check if Remote Config is available
      debugPrint('Starting Remote Config fetch...');

      // Get settings info before fetch
      final settings = remoteConfig.settings;
      debugPrint('Remote Config Settings:');
      debugPrint('- Fetch Timeout: ${settings.fetchTimeout}');
      debugPrint('- Minimum Fetch Interval: ${settings.minimumFetchInterval}');

      // Fetch and activate with retry logic
      bool fetchSuccess = false;
      try {
        fetchSuccess = await remoteConfig.fetchAndActivate();
        debugPrint('Remote Config fetch success: $fetchSuccess');
      } catch (fetchError) {
        debugPrint('Remote Config fetch error: $fetchError');

        // Try to activate cached values if fetch fails
        try {
          await remoteConfig.activate();
          debugPrint('Using cached Remote Config values');
          fetchSuccess = true;
        } catch (activateError) {
          debugPrint('Remote Config activate error: $activateError');
        }
      }

      // If fetch failed, try a simple fetch without activate
      if (!fetchSuccess) {
        try {
          debugPrint('Trying simple fetch...');
          await remoteConfig.fetch();
          await remoteConfig.activate();
          debugPrint('Simple fetch and activate succeeded');
          fetchSuccess = true;
        } catch (simpleFetchError) {
          debugPrint('Simple fetch also failed: $simpleFetchError');
        }
      }

      // Check last fetch status and info
      final lastFetchStatus = remoteConfig.lastFetchStatus;
      final lastFetchTime = remoteConfig.lastFetchTime;
      debugPrint('Last fetch status: $lastFetchStatus');
      debugPrint('Last fetch time: $lastFetchTime');

      // Get all parameters to see what's available
      final allKeys = remoteConfig.getAll();
      debugPrint('All Remote Config keys: ${allKeys.keys.toList()}');
      debugPrint('Total parameters count: ${allKeys.length}');

      final jsonString = remoteConfig.getString('appCurrentVersion');
      log("Remote Config jsonString: $jsonString");

      // Check if the jsonString is empty or invalid
      if (jsonString.isEmpty) {
        debugPrint('Remote Config value for appCurrentVersion is empty');
        debugPrint('Available keys: ${allKeys.keys.toList()}');
        return;
      }

      try {
        final remoteConfigVersion =
            CurrentVersion.fromJson(json.decode(jsonString));
        final currentVersion = int.tryParse(packageInfo.buildNumber) ?? 0;
        final mustForceUpdate = checkForceUpdate(
          currentVersion,
          remoteConfigVersion.platformVersion,
        );
        if (mustForceUpdate) {
          showForceUpdate(remoteConfigVersion);
        } else {
          // Check for soft update only if force update is not required
          final isSoftUpdateApp = checkSoftUpdate(
            currentVersion,
            remoteConfigVersion.platformVersion,
          );
          Future.delayed(const Duration(seconds: 3), () {
            if (isSoftUpdateApp) {
              final currentLocale = _getCurrentLocale();
              final localizedMessage =
                  remoteConfigVersion.getLocalizedMessage(currentLocale);
              goRouter.push('/soft_update?message=$localizedMessage');
            }
          });
        }
      } catch (parseError) {
        debugPrint('Error parsing Remote Config JSON: $parseError');
      }
    } catch (e) {
      debugPrint('Error initializing Remote Config: $e');
    }
  }

  void showForceUpdate(CurrentVersion remoteConfigVersion) {
    // Set force update flag
    _isForceUpdateActive = true;
    debugPrint('🔴 Force Update: Activating force update mode');

    // Get localized message
    final currentLocale = _getCurrentLocale();
    final localizedMessage =
        remoteConfigVersion.getLocalizedMessage(currentLocale);

    // Use pushReplacement to prevent going back and clear navigation stack
    goRouter.pushReplacement('/update_app?message=$localizedMessage');
  }

  /// Get current app locale
  Locale _getCurrentLocale() {
    final context = appKey.currentContext;
    if (context != null) {
      return Localizations.localeOf(context);
    }
    // Fallback to Arabic if context is not available
    return const Locale('ar');
  }

  void showSoftUpdate() {
    try {
      final jsonString = remoteConfig.getString('appCurrentVersion');

      // Check if the jsonString is empty or invalid
      if (jsonString.isEmpty) {
        debugPrint('Remote Config value for appCurrentVersion is empty');
        return;
      }

      final remoteConfigVersion =
          CurrentVersion.fromJson(json.decode(jsonString));
      final currentVersion = int.tryParse(packageInfo.buildNumber) ?? 0;
      final isSoftUpdateApp = checkSoftUpdate(
        currentVersion,
        remoteConfigVersion.platformVersion,
      );
      if (isSoftUpdateApp) {
        final currentLocale = _getCurrentLocale();
        final localizedMessage =
            remoteConfigVersion.getLocalizedMessage(currentLocale);
        goRouter.push('/soft_update?message=$localizedMessage');
      }
    } catch (e) {
      debugPrint('Error in showSoftUpdate: $e');
    }
  }

  /// Manually show the soft update page with a custom message
  void showManualSoftUpdate({String? customMessage}) {
    try {
      final jsonString = remoteConfig.getString('appCurrentVersion');

      // Check if the jsonString is empty or invalid
      if (jsonString.isEmpty) {
        // Fallback message if remote config is empty
        final defaultMessage =
            "Consider updating the app to the latest version for the best experience.";
        goRouter
            .push('/soft_update?message=${customMessage ?? defaultMessage}');
        return;
      }

      try {
        final remoteConfigVersion =
            CurrentVersion.fromJson(json.decode(jsonString));
        final currentLocale = _getCurrentLocale();
        final message = customMessage ??
            remoteConfigVersion.getLocalizedMessage(currentLocale);
        goRouter.push('/soft_update?message=$message');
      } catch (parseError) {
        // Fallback message if JSON parsing fails
        final defaultMessage =
            "Consider updating the app to the latest version for the best experience.";
        goRouter
            .push('/soft_update?message=${customMessage ?? defaultMessage}');
        debugPrint('Error parsing Remote Config JSON: $parseError');
      }
    } catch (e) {
      // Fallback message if remote config fails
      final defaultMessage =
          "Consider updating the app to the latest version for the best experience.";
      goRouter.push('/soft_update?message=${customMessage ?? defaultMessage}');
      debugPrint('Error in showManualSoftUpdate: $e');
    }
  }

  /// Manually show the force update page with a custom message
  void showManualForceUpdate({String? customMessage}) {
    try {
      final jsonString = remoteConfig.getString('appCurrentVersion');

      // Check if the jsonString is empty or invalid
      if (jsonString.isEmpty) {
        // Fallback message if remote config is empty
        final defaultMessage =
            "Please update the app to the latest version for the best experience.";
        goRouter.pushReplacement(
            '/update_app?message=${customMessage ?? defaultMessage}');
        return;
      }

      try {
        final remoteConfigVersion =
            CurrentVersion.fromJson(json.decode(jsonString));
        final currentLocale = _getCurrentLocale();
        final message = customMessage ??
            remoteConfigVersion.getLocalizedMessage(currentLocale);
        goRouter.pushReplacement('/update_app?message=$message');
      } catch (parseError) {
        // Fallback message if JSON parsing fails
        final defaultMessage =
            "Please update the app to the latest version for the best experience.";
        goRouter.pushReplacement(
            '/update_app?message=${customMessage ?? defaultMessage}');
        debugPrint('Error parsing Remote Config JSON: $parseError');
      }
    } catch (e) {
      // Fallback message if remote config fails
      final defaultMessage =
          "Please update the app to the latest version for the best experience.";
      goRouter.pushReplacement(
          '/update_app?message=${customMessage ?? defaultMessage}');
      debugPrint('Error in showManualForceUpdate: $e');
    }
  }

  bool checkForceUpdate(int currentVersion, PlatformVersion platformVersion) =>
      currentVersion < platformVersion.minVersion;

  bool checkSoftUpdate(int currentVersion, PlatformVersion platformVersion) =>
      currentVersion < platformVersion.maxVersion;

  /// Test method to check if Remote Config is working properly
  Future<void> testRemoteConfig() async {
    debugPrint('=== Testing Remote Config ===');

    try {
      // Check connection and fetch
      debugPrint('1. Checking Remote Config connection...');
      final fetchSuccess = await remoteConfig.fetchAndActivate();
      debugPrint('   Fetch result: $fetchSuccess');

      // List all available parameters
      debugPrint('2. Available parameters:');
      final allParameters = remoteConfig.getAll();
      for (final entry in allParameters.entries) {
        debugPrint('   ${entry.key}: ${entry.value.asString()}');
      }

      // Test specific parameter
      debugPrint('3. Testing appCurrentVersion parameter:');
      final versionString = remoteConfig.getString('appCurrentVersion');
      debugPrint('   Raw value: $versionString');

      if (versionString.isNotEmpty) {
        try {
          final versionData = json.decode(versionString);
          debugPrint('   Parsed JSON: $versionData');

          final currentVersion = CurrentVersion.fromJson(versionData);
          debugPrint('   Parsed object: ${currentVersion.toString()}');
        } catch (e) {
          debugPrint('   JSON parsing error: $e');
        }
      }

      // Check current app version
      debugPrint('4. Current app info:');
      debugPrint('   App version: ${packageInfo.version}');
      debugPrint('   Build number: ${packageInfo.buildNumber}');
      debugPrint('   Package name: ${packageInfo.packageName}');

      debugPrint('=== Remote Config test completed ===');
    } catch (e) {
      debugPrint('Remote Config test failed: $e');
    }
  }
}
