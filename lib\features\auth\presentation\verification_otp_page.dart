import 'dart:developer';

import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/core/app_theme.dart';
import 'package:zod/core/resources/app_assets.dart';
import 'package:zod/core/resources/app_icons.dart';
import 'package:zod/core/widgets/arrow_app_bar.dart';
import 'package:zod/features/auth/data/models/phone_dto.dart';
import 'package:zod/features/auth/presentation/providers/allow_verify_provider.dart';
import 'package:zod/features/auth/presentation/providers/verification_otp_form_provider.dart';
import 'package:zod/features/auth/presentation/views/login/verification_otp_empty_bottom_nav_view.dart';
import 'package:zod/features/auth/presentation/views/pinput_view.dart';
import 'package:zod/features/auth/presentation/views/request_code_timer_view.dart';
import 'package:zod/features/auth/presentation/views/verification_otp_bottom_nav_view.dart';

import '../../../app/routes.dart';
import '../data/models/locked.dart';

class VerificationOtpPage extends ConsumerStatefulWidget {
  const VerificationOtpPage({
    super.key,
    required this.phone,
    required this.countryCode,
  });

  static const String route = '/otp';
  final String countryCode;
  final String phone;

  @override
  ConsumerState<VerificationOtpPage> createState() =>
      _VerificationOtpPageState();
}

class _VerificationOtpPageState extends ConsumerState<VerificationOtpPage> {
  late PhoneDto phoneDto;
  final TextEditingController _pinController = TextEditingController();

  @override
  void initState() {
    phoneDto = PhoneDto(widget.phone, widget.countryCode);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final allowVerifyState = ref.watch(allowVerifyProvider(phoneDto));
    ref.listen(allowVerifyProvider(phoneDto), (previous, next) {
      _allowVerifyListener(context, previous, next, ref, phoneDto);
    });
    return WillPopScope(
      onWillPop: () async {
        goRouter.pop(true);
        return Future.value(false);
      },
      child: Scaffold(
        extendBodyBehindAppBar: true,
        bottomNavigationBar: allowVerifyState.maybeWhen(
          data: (locked) {
            return VerificationOtpBottomNavView(
              dto: phoneDto,
              locked: locked,
            );
          },
          orElse: () => VerificationOtpEmptyBottomNavView(),
        ),
        appBar: ArrowAppBar(
          extendBodyBehindAppBar: true,
          backgroundColor: Colors.transparent,
        ),
        body: Stack(
          alignment: Alignment.topLeft,
          children: [
            Column(
              children: [
                Align(
                  alignment: Alignment.center,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      const SizedBox(height: 139),
                      SvgPicture.asset(AppIcons.otp),
                      const SizedBox(height: 16),
                      Text(
                        context.localizations.check_mobile_number,
                        style: context.textTheme.titleLarge?.textColor,
                      ),
                      const SizedBox(height: 4),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            context.localizations.send_to,
                            style: context.textTheme.bodyMedium?.textColor,
                          ),
                          const SizedBox(width: 4),
                          Directionality(
                            textDirection: TextDirection.ltr,
                            child: Text(
                              "${widget.countryCode}${widget.phone}",
                              style:
                                  context.textTheme.bodyMedium?.textColor.w700,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                Column(
                  children: [
                    const SizedBox(height: 16),
                    allowVerifyState.maybeWhen(
                        data: (locked) {
                          log(" locked.locked ${locked.locked}");
                          String? errorMessage = locked.message;
                          // Check if the message contains "Try again" or "حاول"
                          if (errorMessage != null &&
                              (errorMessage.contains("Try again") ||
                                  errorMessage.contains("حاول"))) {
                            // Replace with the fixed localized message
                            errorMessage =
                                context.localizations.invalid_activation_code;
                          } else {
                            // If it doesn't contain these phrases, don't show any message
                            errorMessage = null;
                          }
                          return PinPutView(
                              phoneDto: phoneDto,
                              message: errorMessage,
                              controller: _pinController,
                              enable: locked.locked == false ? true : false);
                        },
                        orElse: () => PinPutView(
                              phoneDto: phoneDto,
                              enable: false,
                              controller: _pinController,
                            )),
                    const SizedBox(height: 44),
                    allowVerifyState.maybeWhen(
                      data: (locked) {
                        log(" locked.resend ${locked.toJson()}");
                        return Visibility(
                          visible: !locked.locked,
                          child: RequestCodeTimer(
                            resendAvailableAt: locked.resendAvailableAt,
                            resendCode: () {
                              _pinController.clear();
                              ref.read(sendOtpLockerProvider.notifier).state =
                                  false;
                              ref.invalidate(allowVerifyProvider(phoneDto));
                              ref.invalidate(
                                  verificationCodeFormProvider(phoneDto));
                            },
                          ),
                        );
                      },
                      orElse: () => RequestCodeTimer(
                        resendCode: () {
                          _pinController.clear();
                          ref.invalidate(sendOtpProvider(phoneDto));
                        },
                      ),
                    ),
                  ],
                )
              ],
            ),
            Image.asset(
              AppImagesAssets.backgroundPattern,
              fit: BoxFit.cover,
            ),
          ],
        ),
      ),
    );
  }

  void _allowVerifyListener(BuildContext context, AsyncValue<Locked>? previous,
      AsyncValue<Locked> next, WidgetRef ref, PhoneDto phoneDto) {}
}
