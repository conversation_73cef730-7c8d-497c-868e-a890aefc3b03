import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/core/app_theme.dart';
import 'package:zod/core/app_widgets.dart';
import 'package:zod/core/resources/app_assets.dart';
import 'package:zod/core/resources/app_colors.dart';
import 'package:zod/core/resources/app_icons.dart';
import 'package:zod/features/auction/data/models/auction.dart';
import 'package:zod/features/auction/presentation/providers/join_auction_provider.dart';

class JoinToAuctionView extends ConsumerWidget {
  final Auction auction;

  const JoinToAuctionView({
    super.key,
    required this.auction,
  });

  static const String route = '/JoinToAuction/:id';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final joinState = ref.watch(joinAuctionProvider(auction));

    return Dialog(
      backgroundColor: AppColors.white,
      insetPadding: EdgeInsets.all(8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        padding: EdgeInsets.symmetric(
          vertical: 16,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.asset(
              AppImagesAssets.confirmJoin,
              width: 104,
            ),
            SizedBox(height: 10),
            Text(
              context.localizations.confirm_joining_the_auction,
              style: context.textTheme.bodyLarge!.w700,
            ),
            SizedBox(height: 10),
            Text.rich(
              TextSpan(
                text: "${context.localizations.participation_fees} ",
                style: context.textTheme.bodyMedium!.w400,
                children: <InlineSpan>[
                  WidgetSpan(
                      child: Transform.translate(
                          offset: Offset(0, -5),
                          child: SvgPicture.asset(
                            AppIcons.coin,
                          ))),
                  TextSpan(
                    text:
                        " ${context.localizations.participation_fee_non_refundable(auction.effectiveEntryFee ?? 0)}",
                  ),
                ],
              ),
            ),
            SizedBox(height: 10),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 20, vertical: 10),
              decoration: BoxDecoration(
                color: AppColors.softWhite,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      SvgPicture.asset(
                        AppIcons.coin,
                        width: 20,
                        height: 20,
                      ),
                      SizedBox(width: 5),
                      Expanded(
                        child: Text(
                            context.localizations
                                .the_fee_is_non_refundable_but_you_can,
                            style: context.textTheme.labelMedium),
                      ),
                    ],
                  ),
                  SizedBox(height: 5),
                  Row(
                    children: [
                      Icon(Icons.info_outline,
                          size: 20, color: AppColors.primaryColor),
                      SizedBox(width: 5),
                      Expanded(
                        child: Text(
                            context.localizations
                                .cancelation_wot_be_possible_after_joining,
                            style: context.textTheme.labelMedium),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            SizedBox(height: 20),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 10),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () => Navigator.pop(context),
                      style: TextButton.styleFrom(
                        backgroundColor: AppColors.disabled,
                        // padding: EdgeInsets.symmetric(
                        //     horizontal: 10, vertical: 10),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                          side: BorderSide(color: AppColors.softSteel),
                        ),
                      ),
                      child: Text(context.localizations.cancel,
                          style: context.textTheme.labelLarge!
                              .copyWith(color: AppColors.steelGray)),
                    ),
                  ),
                  SizedBox(width: 10),
                  Expanded(
                      child: AppButton(
                          loading: joinState.isLoading,
                          title: context.localizations.join_auction,
                          onPressed: () => ref
                              .read(joinAuctionProvider(auction).notifier)
                              .joinToAuction(ref)))
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
