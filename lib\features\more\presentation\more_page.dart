import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:zod/app/routes.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/core/app_theme.dart';
import 'package:zod/core/resources/app_colors.dart';
import 'package:zod/core/resources/app_icons.dart';
import 'package:zod/core/widgets/network_image_widget.dart';
import 'package:zod/features/app/app_providers.dart';
import 'package:zod/features/auth/presentation/logout_page.dart';
import 'package:zod/features/more/presentation/transactions_page.dart';
import 'package:zod/features/more/presentation/update_profile_page.dart';
import 'package:zod/features/more/presentation/views/guest_login_view.dart';
import 'package:zod/features/more/presentation/views/list_tile_view.dart';

import '../../../core/widgets/title_app_bar.dart';
import '../../auth/auth.dart';
import '../../auth/presentation/providers/me_provider.dart';

class MorePage extends ConsumerWidget {
  const MorePage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref.watch(meProvider);

    return Scaffold(
      appBar: TitleAppBar(
        title: context.localizations.more,
        hideBack: true,
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 16),
            if (Auth.check())
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      NetworkImageView(
                        Auth.user().image ?? '',
                        radius: 28,
                        width: 56,
                        height: 56,
                      ),
                      SizedBox(width: 9),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(Auth.user().name ?? '',
                              style: context.textTheme.titleSmall?.w700),
                          SizedBox(height: 4),
                          Directionality(
                            textDirection: TextDirection.ltr,
                            child: Text(Auth.user().fullPhone,
                                style: context.textTheme.bodyMedium),
                          ),
                        ],
                      ),
                    ],
                  ),
                  Divider(color: AppColors.lightGray, thickness: 1)
                ],
              ),
            SizedBox(height: 24),
            if (!Auth.check()) GuestLoginView(),
            SizedBox(height: 16),
            Text(context.localizations.settings,
                style: context.textTheme.titleSmall?.w700),
            SizedBox(height: 12),
            if (Auth.check())
              ListTileView(
                title: context.localizations.personal_information,
                leadingIcon: AppIcons.userProfile,
                onTap: () => goRouter.push(UpdateProfilePage.route),
              ),
            if (Auth.check()) SizedBox(height: 16),
            if (Auth.check())
              ListTileView(
                title: context.localizations.payment_transaction,
                leadingIcon: AppIcons.transactions,
                onTap: () => goRouter.push(TransactionsPage.route),
              ),
            if (Auth.check()) SizedBox(height: 16),
            ListTileView(
              title: context.localizations.popup_menu_language_title,
              leadingIcon: AppIcons.lang,
              onTap: () {
                final local = ref.read(localProvider);
                final newLocale = local == const Locale('en')
                    ? const Locale('ar')
                    : const Locale('en');
                ref.read(localProvider.notifier).update(newLocale);
              },
              trailingIcon: Row(
                children: [
                  Text(
                    ref
                            .read(localProvider)
                            .languageCode
                            .toLowerCase()
                            .contains("en")
                        ? "English"
                        : "العربية",
                    style: context.textTheme.bodyMedium?.medium,
                  ),
                  SizedBox(width: 5),
                  Icon(Icons.arrow_forward_ios, size: 15),
                ],
              ),
            ),
            SizedBox(height: 16),
            ListTileView(
              title: context.localizations.whatsappSupport,
              leadingIcon: AppIcons.whatsapp,
              onTap: () {
                launchUrl(Uri.parse("https://instagram.com/zod_auction"));
              },
            ),
            SizedBox(height: 16),
            ListTileView(
              title: context.localizations.privacy_policy,
              leadingIcon: AppIcons.info,
              onTap: () => goRouter.push('/privacy-policy'),
            ),
            SizedBox(height: 16),
            // Add a button to test the update app feature
            // ListTileView(
            //   title: "Test Update App",
            //   leadingIcon: AppIcons.info,
            //   onTap: () => goRouter.push('/update_app_test'),
            // ),
            // SizedBox(height: 16),
            Visibility(
              visible: Auth.check(),
              child: ListTileView(
                title: context.localizations.popup_menu_logout,
                leadingIcon: AppIcons.logout,
                onTap: () => goRouter.push(LogoutPage.route),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
