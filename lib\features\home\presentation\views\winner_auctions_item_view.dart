import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:zod/app/routes.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/core/app_theme.dart';
import 'package:zod/core/resources/app_colors.dart';
import 'package:zod/core/resources/app_icons.dart';
import 'package:zod/core/widgets/network_image_widget.dart';
import 'package:zod/features/home/<USER>/models/winner_auction.dart';

class WinnerAuctionsItemView extends StatelessWidget {
  const WinnerAuctionsItemView({super.key, required this.auction});

  final WinnerAuction auction;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () =>
          goRouter.push('/auction/${auction.id}', extra: {'from_home': true}),
      child: Container(
        padding: EdgeInsets.all(5),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          border: Border.all(color: AppColors.softSteel1),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Stack(
              children: [
                NetworkImageView(
                  auction.image,
                  width: 156,
                  height: 155,
                  radius: 15,
                ),
                PositionedDirectional(
                  top: 51,
                  start: 28,
                  child: SvgPicture.asset(
                    AppIcons.sold,
                  ),
                ),
              ],
            ),
            SizedBox(height: 6),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: AppColors.disabled,
                borderRadius: BorderRadius.circular(30),
              ),
              child: Row(
                children: [
                  Text(
                    context.localizations.sold_for,
                    style: context.textTheme.labelSmall?.w400,
                  ),
                  SizedBox(width: 11),
                  Row(
                    children: [
                      SvgPicture.asset(AppIcons.riyal, width: 9, height: 9),
                      SizedBox(width: 3),
                      Text(
                        auction.price.toString(),
                        style: context.textTheme.labelSmall,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                  SizedBox(width: 3),
                  Text('( ${auction.finalBidPoints}',
                      style: context.textTheme.labelSmall),
                  SizedBox(width: 2),
                  SvgPicture.asset(AppIcons.coin, width: 9, height: 9),
                  SizedBox(width: 1),
                  Text(')', style: context.textTheme.labelSmall),
                ],
              ),
            ),
            SizedBox(height: 6),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Stack(
                  children: [
                    NetworkImageView(
                      auction.user.avatar?.url ?? '',
                      width: 32,
                      height: 32,
                    ),
                    PositionedDirectional(
                      top: 1,
                      start: 20,
                      child: SvgPicture.asset(
                        AppIcons.verified,
                      ),
                    ),
                  ],
                ),
                SizedBox(width: 4),
                Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(auction.user.name,
                          style: context.textTheme.labelLarge),
                      Text(
                          context.localizations
                              .bids_count(auction.userBidCount),
                          style: context.textTheme.labelSmall?.w400.slateGray),
                    ],
                  ),
                ),
              ],
            )
          ],
        ),
      ),
    );
  }
}
