import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import '../../../../core/resources/app_colors.dart';

class PageIndicator extends StatelessWidget {
  final int currentPage;
  final int totalPages;
  final bool isLoading;

  const PageIndicator({
    super.key,
    required this.currentPage,
    required this.totalPages,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return _buildShimmerIndicators();
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(
        totalPages,
        (index) => Container(
          margin: EdgeInsets.symmetric(horizontal: 4),
          width: currentPage == index ? 13 : 8.5,
          height: currentPage == index ? 13 : 8.5,
          decoration: BoxDecoration(
            color: currentPage == index
                ? AppColors.primaryColor
                : AppColors.lightGray,
            borderRadius: BorderRadius.circular(6),
          ),
        ),
      ),
    );
  }

  Widget _buildShimmerIndicators() {
    return Shimmer.fromColors(
      baseColor: AppColors.platinum.withOpacity(0.4),
      highlightColor: AppColors.platinum.withOpacity(0.2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: List.generate(
          4, // Default number for shimmer
          (index) => Container(
            margin: EdgeInsets.symmetric(horizontal: 4),
            width: index == 0 ? 13 : 8.5,
            height: index == 0 ? 13 : 8.5,
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.circular(6),
            ),
          ),
        ),
      ),
    );
  }
}
