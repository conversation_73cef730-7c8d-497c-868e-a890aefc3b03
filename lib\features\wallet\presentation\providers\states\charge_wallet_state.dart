import 'package:bond_core/bond_core.dart';
import 'package:equatable/equatable.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/features/auth/data/models/user.dart';
import 'package:zod/features/wallet/data/models/points_per_price.dart';
import 'package:zod/features/wallet/data/models/wallet_charge_dto.dart';

class ChargeWalletState extends Equatable {
  final WalletChargeDto? walletChargeDto;
  final User? user;
  final bool isLoading;
  final String? error;
  final int points;
  final int flashPoints;
  final int increment;
  final int fee;
  final int minChargePoint;

  final PointsPerPrice? pointsPerPrice;
  const ChargeWalletState({
    this.walletChargeDto,
    this.user,
    this.isLoading = false,
    this.error,
    this.pointsPerPrice,
    this.flashPoints = 0,
    this.minChargePoint = 0,
    required this.points,
    required this.fee,
    required this.increment,
  });

  ChargeWalletState copyWith({
    WalletChargeDto? walletChargeDto,
    User? user,
    bool? isLoading,
    String? error,
    int? points,
    int? flashPoints,
    int? increment,
    int? fee,
    int? minChargePoint,
    PointsPerPrice? pointsPerPrice,
  }) {
    return ChargeWalletState(
      walletChargeDto: walletChargeDto ?? this.walletChargeDto,
      user: user ?? this.user,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      points: points ?? this.points,
      flashPoints: flashPoints ?? this.flashPoints,
      increment: increment ?? this.increment,
      fee: fee ?? this.fee,
      pointsPerPrice: pointsPerPrice ?? this.pointsPerPrice,
      minChargePoint: minChargePoint ?? this.minChargePoint,
    );
  }

  @override
  List<Object?> get props => [
        walletChargeDto,
        user,
        isLoading,
        error,
        increment,
        fee,
        points,
        flashPoints,
        minChargePoint,
        pointsPerPrice
      ];

  int get price =>
      (points / (pointsPerPrice?.pointsPerPrice ?? 1).toInt()).toInt();

  bool get hasError => error != null && error!.isNotEmpty;
  String get bidText => "$_bidTextFeeLocal $participationText ";
  String get _bidTextFeeLocal =>
      appKey.currentContext!.localizations.fee_participations_fees(fee);
  String get _bidTextLocal => appKey.currentContext!.localizations.bids;

  String get participationText {
    print("point  $points increment $increment");
    return ((points / (increment == 0 ? 1 : increment)) > 0 && !hasError)
        ? "& ${(points / (increment == 0 ? 1 : increment)).toInt()} $_bidTextLocal"
        : "";
  }

  String get participationTextJoind => ((points / increment) > 0 && !hasError)
      ? " ${(points / increment).toInt()} $_bidTextLocal"
      : "";

  int get min => 50; // Minimum charge is 50 points
  int get max => 999999;
}
