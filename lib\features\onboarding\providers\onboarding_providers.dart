import 'package:bond_core/bond_core.dart';
import 'package:bond_network/bond_network.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zod/features/onboarding/data/api.dart';
import 'package:zod/features/onboarding/data/models/onboarding.dart';

import '../../../../core/services/onboarding_service.dart';

// Service providers
final onboardingServiceProvider = Provider<OnboardingService>(
  (ref) => OnboardingService(),
);

final onboardingApiProvider = Provider<OnboardingApi>(
  (ref) => OnboardingApi(sl<BondFire>()),
);

// State providers
final onboardingScreensProvider = FutureProvider<List<Onboarding>>(
  (ref) async {
    final api = ref.read(onboardingApiProvider);
    final response = await api.getOnboardingScreens();
    return response.data;
  },
);

final hasSeenOnboardingProvider = FutureProvider<bool>(
  (ref) => ref.read(onboardingServiceProvider).hasSeenOnboarding(),
);

// Current page provider for onboarding PageView
final currentOnboardingPageProvider = StateProvider<int>((ref) => 0);
