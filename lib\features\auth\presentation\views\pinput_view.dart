import 'dart:developer';

import 'package:bond_core/bond_core.dart';
import 'package:bond_form/bond_form.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:pinput/pinput.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/core/app_theme.dart';
import 'package:zod/core/resources/app_colors.dart';
import 'package:zod/core/resources/app_icons.dart';
import 'package:zod/features/auth/data/models/phone_dto.dart';

import '../providers/verification_otp_form_provider.dart';

class PinPutView extends ConsumerWidget {
  const PinPutView({
    super.key,
    required this.phoneDto,
    this.initialValue,
    this.message,
    this.length = 4,
    this.enable = true,
    required this.controller,
  });

  final String? initialValue;
  final String? message;
  final int length;
  final bool enable;
  final PhoneDto phoneDto;
  final TextEditingController controller;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formController =
        ref.read(verificationCodeFormProvider(phoneDto).notifier);
    final formState = ref.watch(verificationCodeFormProvider(phoneDto));
    log("${formState.error('otp')}");
    final defaultPinTheme = PinTheme(
      width: 79,
      height: 56,
      padding: EdgeInsets.zero,
      margin: EdgeInsets.zero,
      textStyle: Theme.of(context).textTheme.titleLarge,
      decoration: BoxDecoration(),
    );
    final preFilledWidget = Column(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Container(
          width: 79,
          height: 2,
          decoration: BoxDecoration(
            color: AppColors.platinum,
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ],
    );
    return Column(
      children: [
        Directionality(
          textDirection: TextDirection.ltr,
          child: Pinput(
            defaultPinTheme: defaultPinTheme,
            autofocus: true,
            length: length,
            controller: controller,
            separatorBuilder: (index) => const SizedBox(width: 16),
            errorTextStyle: Theme.of(context)
                .textTheme
                .bodySmall
                ?.copyWith(color: AppColors.crimsonRed),
            onCompleted: (pin) {
              if (enable) {
                formController.submit();
              }
            },
            onChanged: (value) => formController.updateText('otp', value),
            preFilledWidget: preFilledWidget,
            closeKeyboardWhenCompleted: true,
            pinAnimationType: PinAnimationType.slide,
            cursor: Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Container(
                  width: 79,
                  height: 3,
                  color: AppColors.softPeriwinkle,
                ),
              ],
            ),
            animationCurve: Curves.easeInOut,
            completeCursor: Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Container(
                  width: 79,
                  height: 2,
                  color: (message?.contains('Try again') ?? false)
                      ? AppColors.crimsonRed
                      : AppColors.softPeriwinkle,
                ),
              ],
            ),
            focusedPinTheme: defaultPinTheme.copyWith(
              decoration: defaultPinTheme.decoration?.copyWith(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            submittedPinTheme: defaultPinTheme.copyWith(
              decoration: defaultPinTheme.decoration?.copyWith(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            errorPinTheme: defaultPinTheme.copyWith(
              decoration: defaultPinTheme.decoration?.copyWith(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
        if (message != null && (message?.contains('Try again') ?? false) ||
            (message?.contains('حاول') ?? false)) ...[
          SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SvgPicture.asset(AppIcons.alertInfo),
                SizedBox(width: 8),
                Flexible(
                  child: Text(
                    context.localizations.invalid_activation_code,
                    style: context.textTheme.labelLarge?.crimsonRed,
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }
}
