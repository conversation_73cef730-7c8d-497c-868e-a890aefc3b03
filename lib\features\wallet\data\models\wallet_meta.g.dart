// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'wallet_meta.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WalletMeta _$WalletMetaFromJson(Map<String, dynamic> json) => WalletMeta(
      success: json['success'] as bool?,
      message: json['message'] as String?,
      invoice_id: json['invoice_id'] as String?,
      totalToPay: (json['total_to_pay'] as num?)?.toDouble(),
      taxAmount: (json['tax_amount'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$Wallet<PERSON>etaTo<PERSON>son(WalletMeta instance) =>
    <String, dynamic>{
      'success': instance.success,
      'message': instance.message,
      'invoice_id': instance.invoice_id,
      'total_to_pay': instance.totalToPay,
      'tax_amount': instance.taxAmount,
    };
