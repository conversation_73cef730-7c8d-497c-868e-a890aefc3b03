import 'dart:io';

import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';
import 'package:open_store/open_store.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/core/app_theme.dart';

import 'package:zod/core/resources/app_assets.dart';
import 'package:zod/core/resources/app_colors.dart';
import 'package:zod/core/widgets/app_button.dart';

class SoftUpdatePage extends StatelessWidget {
  const SoftUpdatePage({super.key, required this.message});

  final String message;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: Platform.isIOS
          ? MediaQuery.of(context).size.height * 0.83
          : MediaQuery.of(context).size.height * 0.87,
      width: double.infinity,
      child: Column(
        children: [
          // Scrollable content
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Image that starts from status bar and takes full width
                  Stack(
                    alignment: Alignment.topCenter,
                    children: [
                      Image.asset(
                        AppImagesAssets.softUpdate,
                        width: double.infinity,
                        fit: BoxFit.contain,
                      ),
                      Positioned(
                        top: 16,
                        child: Container(
                          height: 3,
                          width: 31,
                          color: AppColors.secondaryColor,
                        ),
                      ),
                    ],
                  ),
                  Padding(
                    padding: const EdgeInsets.only(left: 16, right: 16),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          context.localizations.update_app,
                          textAlign: TextAlign.center,
                          style: context.textTheme.headlineMedium,
                        ),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 13.0),
                          child: Text(
                            message,
                            textAlign: TextAlign.center,
                            style: context.textTheme.labelLarge?.slateGray,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          // Fixed button at bottom
          Padding(
            padding: const EdgeInsets.only(
              left: 16,
              right: 16,
            ),
            child: AppButton(
              title: context.localizations.update_app_now,
              onPressed: _onUpdate,
            ),
          ),
          if (Platform.isIOS) const SizedBox(height: 32),
          if (Platform.isAndroid) const SizedBox(height: 16),
        ],
      ),
    );
  }

  void _onUpdate() {
    //TODO::CHANGE appStoreId
    OpenStore.instance.open(
      appStoreId: '6744919634',
      androidAppBundleId: 'app.zod.com',
    );
  }
}
